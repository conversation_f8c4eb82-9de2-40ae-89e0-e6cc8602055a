<!-- 公告栏组件 -->
<script src="//at.alicdn.com/t/c/font_4960088_a2hmzm7m59l.js"></script>
<div class="notice-bar" id="notice-bar">
    <div class="notice-icon">
        <!-- <svg class="icon fs18" aria-hidden="true">
            <use xlink:href="#icon-gonggao"></use>
        </svg> -->
        
        <i class="el-icon-message-solid" style="font-size: 16px; color: #ef436d"></i><span style="margin-left: 8px; color: #ef436d; font-size: 16px">通知公告</span>
    </div>
    <div class="notice-content">
        <div class="notice-scroll" id="notice-scroll">
            <span id="notice-text" style="font-size: 14px;">{$notice['content']}</span>
        </div>
    </div>
    <div class="notice-close">
        <i class="el-icon-close" id="notice-close-btn"></i>
    </div>
</div>

<!-- 公告栏样式 -->
<style>
    .notice-bar {
        display: flex;
        align-items: center;
        background-color: #fffbf0;
        padding: 8px 12px;
        border-radius: 4px;
        max-width: 1636px;
        width: 100%;
        height: 40px;
        overflow: hidden;
        transition: all 0.5s;
    }
    
    .notice-icon {
        color: #ff9500;
        margin-right: 10px;
        flex-shrink: 0;
    }
    
    .notice-content {
        flex-grow: 1;
        overflow: hidden;
        position: relative;
    }
    
    .notice-scroll {
        white-space: nowrap;
        color: #ff9500;
    }
    
    /* 静态显示样式 */
    .notice-static {
        white-space: nowrap;
        color: #ff9500;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    
    @keyframes scrollText {
        0% {
            transform: translateX(100%);
        }
        100% {
            transform: translateX(-100%);
        }
    }
    
    .notice-close {
        margin-left: 10px;
        cursor: pointer;
        color: #999;
        flex-shrink: 0;
    }
    
    .notice-close:hover {
        color: #666;
    }
</style>

<!-- 组件处理脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    var closeBtn = document.getElementById('notice-close-btn');
    var noticeBar = document.getElementById('notice-bar');
    var noticeText = document.getElementById('notice-text');
    var noticeScroll = document.getElementById('notice-scroll');
    
    // 添加关闭按钮点击事件
    if (closeBtn && noticeBar) {
        closeBtn.addEventListener('click', function() {
            // 使用CSS动画实现平滑消失效果
            noticeBar.style.transition = 'all 0.5s';
            noticeBar.style.height = '0';
            noticeBar.style.opacity = '0';
            noticeBar.style.padding = '0';
            
            // 完全移除元素
            setTimeout(function() {
                noticeBar.style.display = 'none';
            }, 500);
        });
    }
    
    // 根据字符数量设置滚动还是静态显示
    function adjustScrollSpeed() {
        if (!noticeText || !noticeScroll) return;
        
        var textContent = noticeText.textContent || noticeText.innerText;
        var textLength = textContent.length;
        
        // 如果字符数超过100，启用滚动
        if (textLength > 100) {
            // 添加滚动类
            noticeScroll.classList.add('notice-scroll');
            noticeScroll.classList.remove('notice-static');
            
            // 动态设置滚动速度，根据文本长度调整
            var textWidth = noticeText.offsetWidth;
            var duration = textWidth / 50; // 根据文本宽度调整动画持续时间
            
            // 设置滚动动画
            noticeScroll.style.animation = 'scrollText ' + duration + 's linear infinite';
        } else {
            // 字符数少于或等于100，静态显示
            noticeScroll.classList.remove('notice-scroll');
            noticeScroll.classList.add('notice-static');
            
            // 取消动画
            noticeScroll.style.animation = 'none';
        }
    }
    
    // 窗口大小改变时重新计算
    window.addEventListener('resize', adjustScrollSpeed);
    
    // 初始化
    setTimeout(adjustScrollSpeed, 500);
});
</script>
