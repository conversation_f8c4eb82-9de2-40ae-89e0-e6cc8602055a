{include file="common/resources" /}
{include file="common/daigouresourses" /}
<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box" id="Settle">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div id="app" class="bg-white max-w-screen-2xl mx-auto p-8 min-h-screen">
            <!-- 订单创建成功 -->
    
            <div class="flex items-center pt-8 pb-2">
                <div class="text-xl font-bold mr-4">訂單創建成功</div>
                <span class="bg-yellow-100 text-yellow-600 px-2 py-1 rounded text-xs mr-2">未付款</span>
                <span class="text-pink-500 text-xs ml-auto">*{{ countdown }}后可申请取消订单</span>
                
            </div>
            <div class="flex items-center text-gray-600 mb-2" style="background:#fff7f9;padding:10px">
                <span class="mr-8">{{formatTime(orderList.createtime)}}</span>
                <!-- <span class="mr-8">阿里订单号：<span class="font-bold" v-for="(item,index) in orderList.goods" :key="item.orderId">{{item.orderId}}</span></span> -->
                <!-- <span class="mr-8">平台订单编号：<span class="font-bold">{{orderList.order_no}}</span></span> -->
                <span class="mr-8">下单账号：<span class="font-bold">{{orderList.goods[0].buyerLoginId}}</span></span>
            </div>
    
            <!-- 订单明细 -->
            <div class="rounded-none pt-4 pb-8">
                <div class="text-pink-500 font-bold mb-2 text-base">订单明细</div>
                <div class="bg-white rounded-none border border-gray-200">
                    <!-- 为每个订单添加头部信息 -->
                    <div v-for="(item, index) in orderList.goods" :key="item.id" class="border-b border-gray-100">
                        <!-- 订单头部信息 -->
                        <div class=" bg-pink-50 px-6 py-3 border-b border-gray-200">
                            <div class="flex items-center ">
                                <div class="flex items-center">
                                    <span class="text-gray-600 font-bold text-sm">{{item.sellerLoginId}}</span>
                                </div>
                                <div class="flex items-center ml-4">
                                    <span class="text-gray-600 text-sm mr-2">1688订单号：</span>
                                    <span class="text-gray-600 font-bold text-sm">{{item.orderId}}</span>
                                    <span class="text-gray-600 text-sm ml-2 mr-2">流水号：</span>
                                    <span class="text-gray-600 font-bold text-sm">{{item.no}}</span>
                                </div>
                                <div style="margin-left:45%">
                                    <button class="border border-gray-300 px-3 py-1 rounded ml-4 text-gray-600 text-xs"
                                    :disabled="cancelDisabled" @click="cancelOrder(orderList.order_no,item.orderId)">取消订单</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 订单商品表格 -->
                        <table class="w-full text-center border-collapse">
                            <thead class="bg-gray-50 border-b border-gray-200">
                                <tr class="text-xs text-gray-500">
                                    <th class="py-2 font-normal w-1/3 text-left pl-6">货品信息</th>
                                    <th class="py-2 font-normal">发布价</th>
                                    <th class="py-2 font-normal">实付</th>
                                    <th class="py-2 font-normal">数量</th>
                                    <th class="py-2 font-normal">金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-100" v-for="(product, productIndex) in item.productItems.data" :key="productIndex">
                                    <td class="flex items-center py-2 px-2 text-left pl-6 border-none bg-white">
                                        <img :src="getImageProxyUrl(product.productImgUrl[0])" class="w-16 h-16 object-cover rounded border mr-4" />
                                        <div>
                                            <div class="text-pink-500 font-bold text-sm leading-tight" style="border-bottom:1px solid #ef436d;max-width: 450px; overflow: hidden;white-space: nowrap; text-overflow: ellipsis;">
                                               {{product.name}}</div>
                                            <div class="text-gray-500 text-xs mt-2" >
                                                规格： {{product.cargoNumber}}* {{product.quantity}}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-xs">¥{{product.price}}</td>
                                    <td class="text-xs">¥{{product.price}}</td>
                                    <td class="text-xs">{{product.quantity}}</td>
                                    <td class="text-xs">¥{{product.itemAmount}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="px-6 py-2">
                        <!-- <div class="flex text-xs text-gray-600 mb-3 mt-2">
                            <div class="w-1/2">货品编码：</div>
                            <div class="w-1/2 text-right">￥{{getTotalProductAmount().toFixed(2)}}</div>
                        </div> -->
                        <div class="flex text-xs text-gray-600 mb-3 mt-2">
                            <div class="w-1/2">运费：</div>
                            <div class="w-1/2 text-right">￥{{getTotalShippingFee().toFixed(2)}}</div>
                        </div>
                        <div class="flex text-xs text-gray-600 mb-3 mt-2">
                            <div class="w-1/2">订单金额：</div>
                            <div class="w-1/2 text-right text-pink-500 font-bold text-sm leading-tight">￥{{orderList.totalAmount}}<span>（计算匯率 {{ehg}}）</span></div>
                        </div>
                        <div class="flex text-xs text-gray-400 mb-3 mt-2">
                            <div class="w-1/2"></div>
                          <div class="w-1/2 text-right text-sm leading-tight"><span>如卖家已改价，请</span><span
                                    class="text-xs text-pink-500 cursor-pointer">更新价格</span></div>
                        </div>
                        <div class="flex text-xs text-gray-500 mb-3 mt-10">
                            <div class="w-1/2"></div>
                            
                        </div>
                        <div class="flex text-xs text-gray-600 mb-3 mt-2">
                            <div class="w-1/2">订单金额(台幣)：</div>
                            <div class="w-1/2 text-right text-pink-500  text-sm leading-tight">{{Math.ceil(orderList.totalAmount * ehg)}}元</div>
                        </div>
                        <div class="flex text-xs text-gray-600 mb-3 mt-2">
                            <div class="w-1/2">1%服务费(台幣)：</div>
                            <div class="w-1/2 text-right text-pink-500  text-sm leading-tight">{{Math.ceil(orderList.totalAmount * ehg * 0.01)}}元</div>
                        </div>
                        <div class="flex text-xs text-gray-800 mb-3 mt-2 ">
                            <div class="w-1/2">合计台幣：</div>
                            <div class="w-1/2 text-right text-pink-500  text-sm leading-tight">{{Math.ceil(orderList.totalAmount * ehg * 1.01)}}元</div>
                        </div>
                        <div class="flex text-xs text-gray-800 mb-3 mt-2 ">
                            <div class="w-1/2">最后处理时间：</div>
                            <div class="w-1/2 text-right text-gray-600  text-sm leading-tight">{{formatTime(orderList.createtime)}}</div>
                        </div>
                        <div class="flex text-xs text-gray-800 mb-3 mt-2 ">
                            <div class="w-1/2">交易创建时间：</div>
                            <div class="w-1/2 text-right text-gray-600  text-sm leading-tight">{{formatTime(orderList.createtime)}}</div>
                        </div>
                        <div class="flex text-xs text-gray-800 mb-3 mt-2 ">
                            <div class="w-1/2">收货地址：</div>
                            <div class="w-1/2 text-right text-gray-600  text-sm leading-tight">{{getAddressInfo()}}</div>
                        </div>
                        <div class="flex text-xs text-gray-800 mb-3 mt-2 ">
                            <div class="w-1/2"></div>
                            <div class="w-1/2 text-right text-gray-600  text-sm leading-tight">{{getFullAddress()}}</div>
                        </div>
                    </div>
                </div>
            </div>
    
            <!-- 支付方式 -->
            <div class="bg-white rounded-lg p-6 mb-8 mt-5 border">
                <div class="text-pink-500 font-bold mb-4">付款方式</div>
                <div v-if="orderList.pay_type==0">
                    <div class="flex items-center mb-4">
                        <label class="mr-8"><input type="radio" v-model="payType" value="F幣"> F幣<span
                                style="color:#FF9200">（剩餘：0）餘額不足 </span>
                                <!-- <span class="text-pink-500 cursor-pointer"
                                @click="showTipDialog = true">立即儲值</span> -->
                            </label>
                        <label class="mr-8"><input type="radio" v-model="payType" value="ATM"> 網路ATM/ATM櫃員機</label>
                        <!-- <label class="mr-8"><input type="radio" v-model="payType" value="Pay"> 台灣Pay掃碼付款</label> -->
                    </div>
                    <div class="flex space-x-4 mb-2" v-if="payType == 'ATM'">
                        <div v-for="(bank, index) in user_bank" :key="bank.id" 
                             class="bg-white border rounded px-6 py-2 flex items-center text-gray-700 cursor-pointer hover:bg-gray-50"
                             :class="{'border-pink-500 bg-pink-50': selectedUserBank === bank.id}"
                             @click="selectedBank(bank)">
                             {{bank.name}} {{bank.account_name}} {{bank.account_six}}
                        </div>
                    </div>
                </div>
                <div v-else-if="orderList.pay_type==1">
                    用户已选择钱包支付方式
                </div>
                <div v-else-if="orderList.pay_type==2">
                    <div>
                    用户已选择银行汇款方式
                    </div>
                    <div v-if="matchedBankInfo">
                        用户银行名称：{{matchedBankInfo.name}}，银行id：{{orderList.bank_id}}，用户名称：{{matchedBankInfo.account_name}}
                    </div>
                    <div v-else>
                        银行信息未找到
                    </div>
                </div>

                <!-- <div class="flex items-center mb-6">
                    <label class="mr-8"><input type="radio" v-model="payType" value="donate"> 台灣Pay捐款付費</label>
                </div> -->
                <!-- 发票Tab栏 -->
                <div class="flex border-b mb-6 mt-8">
                    <div :class="['px-4 py-2 cursor-pointer text-sm', invoiceTab === 'personal' ? 'text-pink-500 border-b-2 border-pink-500 font-bold' : 'text-gray-500']"
                        @click="invoiceTab = 'personal'">個人發票</div>
                    <div :class="['px-4 py-2 cursor-pointer text-sm', invoiceTab === 'company' ? 'text-pink-500 border-b-2 border-pink-500 font-bold' : 'text-gray-500']"
                        @click="invoiceTab = 'company'">公司用發票</div>
                    <div :class="['px-4 py-2 cursor-pointer text-sm', invoiceTab === 'donate' ? 'text-pink-500 border-b-2 border-pink-500 font-bold' : 'text-gray-500']"
                        @click="invoiceTab = 'donate'">發票捐贈</div>
                </div>
                <!-- 个人发票表单 -->
                <div v-if="invoiceTab === 'personal'">
                    <div class="text-pink-500 font-bold mb-4">個人發票</div>
                    <div class="flex items-center mb-4">
                        <div class="w-1/2 pr-8">
                            <div class="mb-2 text-sm">電子發票類型</div>
                            <input type="text" value="二聯式發票（公司行號）" readonly
                                class="border rounded px-3 py-2 w-full bg-gray-50 text-gray-700">
    
                        </div>
                        <div class="w-1/2 pl-8">
                            <div class="mb-2 text-sm font-bold">E-mail <span class="text-pink-500">*</span></div>
                            <input type="email" v-model="companyEmail" placeholder="请输入E-mail"
                                class="border rounded px-3 py-2 w-full">
                        </div>
                    </div>
                    <div class="flex items-center mb-4">
                        <label class="mr-8"><input type="radio" v-model="invoiceCarrier" value="member"> 會員載具</label>
                        <label class="mr-8"><input type="radio" v-model="invoiceCarrier" value="mobile"> 手機條碼載具</label>
                        <label class="mr-8"><input type="radio" v-model="invoiceCarrier" value="nature"> 自然人憑證號碼載具</label>
                    </div>
                    <div v-if="invoiceCarrier === 'mobile'" class="bg-gray-50 rounded p-4 mb-4">
                        <div class="mb-2 text-sm font-bold">手機條碼 <span class="text-pink-500">*</span></div>
                        <input type="text" v-model="mobileBarcode" placeholder="请输入 '/' 后大写英文字母数字7码"
                            class="border rounded px-3 py-1 w-80 mb-2">
                        <div class="flex items-center mt-2">
                            <input type="checkbox" v-model="agreeMobile" class="mr-2">
                            <span class="text-pink-500">我同意記錄本次手機條碼為常用載具</span>
                        </div>
                    </div>
                    <div v-if="invoiceCarrier === 'nature'" class="bg-gray-50 rounded p-4 mb-4">
                        <div class="mb-2 text-sm font-bold">自然人凭证号 <span class="text-pink-500">*</span></div>
                        <input type="text" v-model="natureCode" placeholder="请输入自然人凭证号"
                            class="border rounded px-3 py-1 w-80 mb-2">
                        <div class="flex items-center mt-2">
                            <input type="checkbox" v-model="agreeNature" class="mr-2">
                            <span class="text-pink-500">我同意記錄本次自然人凭证号为常用载具</span>
                        </div>
                    </div>
                    <div class="bg-yellow-50 text-yellow-700 p-3 rounded mb-4 text-sm flex items-center">
                        <span class="mr-2">⚠️</span>依法规，个人发票一经开立，不得更改或改用公司户发票。<a href="#"
                            class="text-pink-500 ml-1 underline">财政部电子发票流程说明</a>
                    </div>
                    <div class="flex items-center mb-4">
                        <input type="checkbox" v-model="agreeReturn" class="mr-2">
                        <span>我同意辦理退貨時，由買即可代為處理發票及銷售退回證明單以加速退貨退款作業。</span>
                    </div>
                </div>
                <!-- 公司用发票表单 -->
                <div v-if="invoiceTab === 'company'">
                    <div class="text-pink-500 font-bold mb-4">公司用發票</div>
                    <div class="flex mb-4">
                        <div class="w-1/2 pr-8">
                            <div class="mb-2 text-sm">電子發票類型</div>
                            <input type="text" value="三聯式發票（公司行號）" readonly
                                class="border rounded px-3 py-2 w-full bg-gray-50 text-gray-700">
                            <div class="mt-6 mb-2 text-sm font-bold">公司統編 <span class="text-pink-500">*</span></div>
                            <input type="text" v-model="companyTaxId" placeholder="请输入公司統編"
                                class="border rounded px-3 py-2 w-full">
                        </div>
                        <div class="w-1/2 pl-8">
                            <div class="mb-2 text-sm font-bold">E-mail <span class="text-pink-500">*</span></div>
                            <input type="email" v-model="companyEmail" placeholder="请输入E-mail"
                                class="border rounded px-3 py-2 w-full">
                        </div>
                    </div>
                    <div class="flex items-center text-pink-500 text-sm mb-1">
                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M16.707 5.293a1 1 0 00-1.414 0L9 11.586 6.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l7-7a1 1 0 000-1.414z" />
                        </svg>
                        我同意記錄本次統一編號為常用編號
                    </div>
                    <div class="flex items-center text-pink-500 text-sm mb-1">
                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M16.707 5.293a1 1 0 00-1.414 0L9 11.586 6.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l7-7a1 1 0 000-1.414z" />
                        </svg>
                        我同意辦理退貨時，由買即可代為處理發票及銷貨退回證明單以加速退貨退款作業。
                    </div>
                </div>
                <!-- 發票捐贈表单 -->
                <div v-if="invoiceTab === 'donate'">
                    <div class="text-pink-500 font-bold mb-4">發票捐贈</div>
                    <div class="flex mb-4">
                        <div class="w-1/2 pr-8">
                            <div class="mb-2 text-sm">電子發票類型</div>
                            <input type="text" value="捐贈發票" readonly
                                class="border rounded px-3 py-2 w-full bg-gray-50 text-gray-700">
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="grid grid-cols-2 gap-y-2 gap-x-8">
                            <label class="flex items-center">
                                <input type="radio" v-model="donateOrg" value="財團法人臺灣兒童社會福利基金會" class="mr-2">
                                財團法人臺灣兒童社會福利基金會
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="donateOrg" value="財團法人流浪動物之家基金會" class="mr-2">
                                財團法人流浪動物之家基金會
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="donateOrg" value="財團法人早見兒童發展基金會" class="mr-2">
                                財團法人早見兒童發展基金會
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="donateOrg" value="財團法人台灣公益聯盟" class="mr-2">
                                財團法人台灣公益聯盟
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="donateOrg" value="臺東家扶弱勢兒童協會" class="mr-2">
                                臺東家扶弱勢兒童協會
                            </label>
                            <label class="flex items-center">
                                <input type="radio" v-model="donateOrg" value="迦南身心障礙養護院" class="mr-2">
                                迦南身心障礙養護院
                            </label>
                        </div>
                    </div>
                    <div class="flex items-center text-pink-500 text-sm mb-1 mt-2">
                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M16.707 5.293a1 1 0 00-1.414 0L9 11.586 6.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l7-7a1 1 0 000-1.414z" />
                        </svg>
                        我同意辦理退貨時，由買即可代為處理發票及銷貨退回證明單以加速退貨退款作業。
                    </div>
                </div>
                <div class="bg-gray-50 p-4 rounded mb-4 text-sm">
                    會員委託本站跨境採購之商品於送達會員所指定之內地集運站後，會員需自行聯繫物流服務商完成清點、開箱驗貨等必要措施進行驗收，確認商品在內地段已無需退換貨之情事，則上述商品之所有權已移轉予會員，即為本委託案件已於海外完成交付結案，之後買即可不再提供任何售後服務。
                </div>
                <div class="flex items-center justify-between mt-6">
                    <div class="text-lg font-bold">应付金额：</div>
                    <div class="text-pink-500 text-2xl font-bold">NT$ {{Math.ceil(getTotalOrderAmount() * 4.585 * 1.01)}}元</div>
                </div>
                <div class="flex items-center mt-12">
                    <input type="checkbox" v-model="agreeEntrust" class="mr-2">
                    <span>同意 <span class="text-pink-500">《跨境采购代付委托书》</span></span>
                </div>
                <div class="flex justify-end mt-4" >
                    <button class="bg-pink-500 text-white px-12 py-2 rounded text-lg font-bold" v-if="orderList.pay_type == 0"
                        @click="gonext">下一步</button>
                </div>
                <div v-if="showCancelDialog"
                    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50"
                    style="z-index:9999;">
                    <div class="bg-white rounded-lg shadow-lg p-8 w-80 text-center relative">
                        <div class="text-pink-500 text-2xl mb-2 flex items-center justify-center font-bold">
                            <svg class="w-7 h-7 mr-2" fill="none" stroke="currentColor" stroke-width="2"
                                viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="#fff" />
                                <path d="M8 12l2 2 4-4" stroke="#ec4899" stroke-width="2" fill="none" />
                            </svg>
                            取消成功
                        </div>
                    </div>
                </div>
            </div>
            <!-- 弹框放在#app内，避免页面加载即弹框 -->
            <div v-if="showRechargeDialog"
                class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
                <div class="bg-white rounded-lg shadow-lg p-8 w-[600px] max-w-full text-center relative">
                    <div class="text-pink-500 text-xl font-bold mb-6 tracking-wider">收银臺6174910963399594</div>
                    <div class="text-left mb-6">
                        <div class="text-pink-500 font-bold mb-2 flex items-center">
                            <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>费用结算
                        </div>
                        <div class="bg-gray-50 rounded p-4 mb-4">
                            <div class="flex justify-between text-sm mb-2"><span>货品总价：</span><span>￥{{getTotalProductAmount().toFixed(2)}}</span></div>
                            <div class="flex justify-between text-sm mb-2"><span>运费：</span><span>￥{{getTotalShippingFee().toFixed(2)}}</span></div>
                            <div class="flex justify-between text-sm mb-2"><span>订单金额：</span><span
                                    class="text-pink-500 font-bold">{{getTotalOrderAmount().toFixed(2)}}（计费匯率 4.585）</span></div>
                            <div class="flex justify-between text-sm mb-2"><span>订单金额(台币)：</span><span>{{Math.ceil(getTotalOrderAmount() * 4.585)}}元</span></div>
                            <div class="flex justify-between text-sm mb-2"><span>1%服务费(台币)：</span><span>{{Math.ceil(getTotalOrderAmount() * 4.585 * 0.01)}}元</span></div>
                            <div class="flex justify-between text-base font-bold"><span>合计台币：</span><span
                                    class="text-pink-500">{{Math.ceil(getTotalOrderAmount() * 4.585 * 1.01)}}元</span></div>
                        </div>
                    </div>
                    <div class="text-left mb-6">
                        <div class="text-pink-500 font-bold mb-2 flex items-center">
                            <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>您的付款银行账户
                        </div>
                        <div class="flex space-x-4 mb-2">
                            <div 
                                 class="bg-white border rounded px-6 py-2 flex items-center text-gray-700 cursor-pointer hover:bg-gray-50  border-pink-500 bg-pink-50"
                                >
                                {{selecteName}}
                                {{selectedUserBank}}
                                 {{selectedaccount_six}}
                            </div>
                        </div>
                        <div class="bg-yellow-50 text-yellow-700 p-2 rounded text-xs flex items-center mb-2">
                            <span class="mr-2">⚠️</span>请使用您已登记的银行卡号转账付款，非上述银行卡号转账入账无法完成交易！
                        </div>
                    </div>
                    <div class="text-left mb-6">
                        <div class="500 font-bold mb-2 flex items-center">
                            <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>網路ATM/ATM櫃員機
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="rounded-xl py-6 flex flex-col items-center w-full max-w-md cursor-pointer"
                                @click="showBankModal = true">
                                <img src="__CDN__/assets/img/daigou/card.jpg" class="" />
    
    
                            </div>
                        </div>
                        <div class="bg-yellow-50 text-yellow-700 p-2 rounded text-xs flex items-center mb-2">
                            1688代采服务商的付款公司全程服务，请务必对订单金额入账转账，如金额有误或误购入其他账户将无法即时入账。
                        </div>
                    </div>

                    <div class="loading-more" v-if="isLoading"
                        style="width: 100%;display: flex; flex-direction: column; align-items: center; justify-content: center;">
                        <div><i class="el-icon-loading ml8 fs24 c999"></i></div>
                        <div style="margin-left: 10px; color: #999999;">加載中</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-center mt-6" v-else>
                        <button class="bg-pink-500 text-white px-12 py-2 rounded text-lg font-bold"
                            @click="confirm">我已匯款</button>
                    </div>
                </div>
            </div>
            <!-- 银行弹窗 -->
            <div v-if="showBankModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
                <div class="bg-white rounded-2xl shadow-xl p-8 w-full max-w-lg relative">
                    <div class="text-center text-pink-500 text-2xl font-bold mb-6">溫馨提示</div>
                    <div class="flex items-start mb-6">
                        <span class="text-pink-500 text-2xl mr-2">!</span>
                        <div class="text-gray-700 text-base leading-relaxed">
                            請使用以下銀行賬號至ATM或網路銀行完成轉賬
                        </div>
                    </div>
                    <div class="flex space-x-4 justify-center mb-6">
                        <div class="flex flex-col items-center border rounded-xl px-6 py-4 w-40">
                            <img src="__CDN__/assets/img/daigou/bank.png" class="w-10 h-10 mb-2" />
                            <div class="text-gray-700 font-bold">{{sys_bank.account_name}}</div>
                            <div class="text-gray-500 text-lg font-mono tracking-widest">{{sys_bank.account_num}}</div>
                        </div>
                    </div>
                    <div class="bg-yellow-100 text-yellow-700 rounded-lg px-4 py-3 flex items-center mb-4">
                        <span class="text-2xl mr-2">&#9888;</span>
                        <span>若您尚未完成轉賬，請儘快於ATM或網路銀行完成轉賬</span>
                    </div>
                    <div class="text-xs text-gray-500 mb-2">
                        *不是以上銀行匯款，請<a href="#" class="text-pink-500 underline">登記新的銀行末六位碼</a>
                        <span class="float-right text-green-500">已登記末六位碼</span>
                    </div>
                    <div class="flex justify-end space-x-4 mt-6">
                        <button class="px-8 py-2 rounded border text-gray-500 bg-white hover:bg-gray-100"
                            @click="showBankModal = false">取消</button>
                        <button class="px-8 py-2 rounded bg-pink-500 text-white font-bold hover:bg-pink-600"
                            @click="showBankModal = false">確定</button>
                    </div>
                </div>
            </div>
            <div v-if="showTipDialog" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
                <div class="bg-white rounded-xl shadow-lg p-8 text-center relative" style="width:400px">
                    <div class="flex justify-center items-center mb-4">
                        <span class="text-yellow-500 text-3xl mb-2 mr-4">!</span>
                        <span class="text-yellow-500 text-xl font-bold mb-2">温馨提示</span>
                    </div>
                    <div class="text-gray-700 text-base mb-2 leading-relaxed">
                        请於收到商品清点无误后，点击「完成订单」按钮，系统将於隔日凌晨开立电子发票。
                    </div>
                    <div class="text-pink-500 text-xs mb-4">
                        *系统默认包裹签收后15日，自动确认交易。
                    </div>
                    <div class="flex justify-between mt-6 space-x-4">
                        <button class="flex-1 px-4 py-2 rounded border text-gray-500 bg-white hover:bg-gray-100"
                            @click="showTipDialog = false">返回</button>
                        <button class="flex-1 px-4 py-2 rounded bg-pink-500 text-white font-bold hover:bg-pink-600"
                            @click="showRechargeDialogtrue">我已瞭解</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    
    const app = new Vue({
        el: '#Settle',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            orderList: <?php echo json_encode($list); ?>, 
            sys_bank: <?php echo json_encode($sys_bank); ?>, 
            user_bank: <?php echo json_encode($user_bank); ?>, 
            ehg: <?php echo json_encode($ehg); ?>,
            payType: 'F幣',
            invoiceType: 'personal',
            invoiceCarrier: 'member',
            email: '',
            agreeReturn: false,
            agreeEntrust: false,
            mobileBarcode: '',
            agreeMobile: false,
            natureCode: '',
            agreeNature: false,
            countdown: 59,
            cancelDisabled: false,
            showCancelDialog: false,
            invoiceTab: 'personal',
            companyTaxId: '',
            companyEmail: '',
            donateOrg: '',
            showRechargeDialog: false,
            showBankModal: false,
            showTipDialog: false,
            selectedUserBank: null,
            selectedaccount_six: null,
            selectedId: null,
            selecteName: null,
            isLoading:false,
        },
        computed: {
            canSubmit() {
                return this.payType && this.invoiceType && this.email && this.agreeEntrust;
            },
            selectedBankInfo() {
                if (!this.selectedUserBank || !this.user_bank) return null;
                return this.user_bank.find(bank => bank.id === this.selectedUserBank);
            },
            matchedBankInfo() {
                if (!this.orderList || !this.orderList.bank_id || !this.user_bank) return null;
                return this.user_bank.find(bank => bank.id == this.orderList.bank_id);
            }
        },
        mounted() {
            this.startCountdown();
            this.orderList.goods.forEach(item => {
                // 解析shop
                if (typeof item.productItems === 'string') {
                    try {
                        item.productItems = JSON.parse(item.productItems);
                    } catch (e) {
                    }
                }
            
            });
            // 设置默认选中的用户银行
            if (this.user_bank && this.user_bank.length > 0) {
                this.selectedUserBank = this.user_bank[0].id;
                this.selectedaccount_six = this.user_bank[0].account_six;
                this.selectedId = this.user_bank[0].id;
                this.selecteName = this.user_bank[0].name;
            }
            console.log(this.orderList,this.sys_bank,this.user_bank,77777)
        },
        methods: {
            getImageProxyUrl(originalUrl) {
                if (!originalUrl) return '';
                // 使用图片代理
                return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
            },
            showRechargeDialogtrue() {
                this.showTipDialog = false
                this.showRechargeDialog = true
            },
            startCountdown() {
                this.countdownTimer = setInterval(() => {
                    if (this.countdown > 0) {
                        this.countdown--;
                    } else {
                        this.cancelDisabled = true;
                        clearInterval(this.countdownTimer);
                    }
                }, 1000);
            },
            async cancelOrder(a,b) {
                let params = {no:a,id:b}
                console.log('params:', params);
                let res = await axios.post('daigouAliOrderCancel',params);
                console.log('取消:', res.data);
                
                if (res.data.code == '0') {
                    this.showCancelDialog = true;
                    let res1 = axios.get(`daigouAliOrderConfirm?no=${res.data.no}`);
                    setTimeout(() => {
                        this.showCancelDialog = false;
                        // 直接从数组中移除取消的订单
                        window.location.href = `daigouAliOrderConfirm?no=${res.data.no}`;
                    }, 1200);

                } else {
                    this.$message.error(res.data.msg || '订单创建失败');
                }
               
            },
            submitOrder() {
                if (!this.canSubmit) return;
                alert('订单已提交！');
            },
            // 计算商品总数量
            getTotalQuantity(productItems) {
                if (!productItems || !Array.isArray(productItems)) return 0;
                return productItems.reduce((total, product) => total + parseInt(product.quantity || 0), 0);
            },
            // 计算总货品金额
            getTotalProductAmount() {
                if (!this.orderList.goods) return 0;
                return this.orderList.goods.reduce((total, item) => total + parseFloat(item.sumProductPayment || 0), 0);
            },
            // 计算总运费
            getTotalShippingFee() {
                if (!this.orderList.goods) return 0;
                return this.orderList.goods.reduce((total, item) => total + parseFloat(item.shippingFee || 0), 0);
            },
            // 计算订单总金额
            getTotalOrderAmount() {
                return this.getTotalProductAmount() + this.getTotalShippingFee();
            },
            // 格式化时间
            formatTime(timestamp) {
                if (!timestamp) return '';
                const date = new Date(timestamp * 1000);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            },
            // 获取地址信息
            getAddressInfo() {
                if (!this.orderList.goods || this.orderList.goods.length === 0) return '';
                const firstGood = this.orderList.goods[0];
                if (!firstGood.address) return '';
                return `${firstGood.address.fullName || ''} ${firstGood.address.mobilePhone || ''}`;
            },
            // 获取完整地址
            getFullAddress() {
                if (!this.orderList.goods || this.orderList.goods.length === 0) return '';
                const firstGood = this.orderList.goods[0];
                if (!firstGood.address) return '';
                return `${firstGood.address.addressCodeText || ''} ${firstGood.address.address || ''}`;
            },
            confirm(){
                this.showRechargeDialog = false
            },
            selectedBank(i){
                this.selectedUserBank = i.id
                this.selectedaccount_six = i.account_six
                this.selectedId = i.id
                this.selecteName = i.name
            },
            async gonext(){
                this.isLoading = true
                setTimeout(() => {
                    this.isLoading = false
                    }, 120000);
                if(this.agreeEntrust == false){
                    this.$message.error('请勾选同意《跨境采购代付委托书》');
                    return 
                }
                
                let params = {};  // 在外部声明params，确保它在所有情况下都可访问
                if (this.payType == 'F幣') {
                    params.type = 1;
                    params.id = this.orderList.id;
                } else {
                    params.type = 2;
                    params.id = this.orderList.id;
                    params.bankid = this.selectedId;
                    this.showTipDialog = true
                }
                console.log(params,890)
                let res = await axios.post(`daigouAliOrderConfirm`,params);
                console.log(res,111)

                if(res.data.code=='0'){
                    this.$message.success(res.data.msg);
                }else{
                    this.$message.error(res.data.msg);
                }
            },
            golist(){
                window.location.href = 'aliOrderList';

                // 创建表单并提交数据
                // const form = document.createElement('form');
                // form.method = 'POST';
                // form.action = 'daigouAliOrderList';

                // // 创建隐藏的input字段来传递数据
                // const dataInput = document.createElement('input');
                // dataInput.type = 'hidden';
                // dataInput.name = 'data';
                // form.appendChild(dataInput);

                // // 将表单添加到页面并提交
                // document.body.appendChild(form);
                // form.submit();
            }
        }
    })
</script>