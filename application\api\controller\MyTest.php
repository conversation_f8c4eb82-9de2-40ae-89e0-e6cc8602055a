<?php


            // 测试数据
            // $data = "{\"account_name\":\"jack\"}";
            // $headers = array(
            //     "Content-Type: application/json",
            // );
            // $http = curl_init("https://ads.paytaotao.com/api/Launch/get_launch");

            // curl_setopt($http, CURLOPT_HTTPHEADER, $headers);
            // curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
            // curl_setopt($http, CURLOPT_POST, 1);
            // curl_setopt($http, CURLOPT_POSTFIELDS, $data);
            // $result = curl_exec($http);
            // echo json_encode($result, JSON_UNESCAPED_UNICODE);
            // curl_close($http);



         // // 玉山数据
         // $data = array("bk"=>"玉山銀行","id"=>"*************","timestamp"=>"**********","version"=>"1.0",
         //    "sign"=>"20121d0a52126f8820e1dfff3dee4a37","date_time"=>"2025-06-20  15:42:19","taken"=>"0","save"=>"500",
         //    "balance"=>"14250","abstract"=>"ＡＴＭ跨行轉","remark"=>"第一銀行007/****************");

         // $data = json_encode($data,JSON_UNESCAPED_UNICODE);

         // $http = curl_init("http://www.jiyun.com/api/Jiyun/save_bank");
         // curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
         // curl_setopt($http, CURLOPT_POST, 1);
         // curl_setopt($http, CURLOPT_POSTFIELDS, $data);
         // $result = curl_exec($http);
         // echo json_encode($result, JSON_UNESCAPED_UNICODE);
         // curl_close($http);



// 玉山数据
// $data = array("bk"=>"玉山銀行","id"=>"*************","timestamp"=>"**********","version"=>"1.0",
//    "sign"=>"20121d0a52126f8820e1dfff3dee4a37","date_time"=>"2025-06-26  15:42:19","taken"=>"0","save"=>"300",
//    "balance"=>"2050","abstract"=>"ＡＴＭ跨行轉","remark"=>"第一銀行007/****************");
         

// // 信托
// $data = array("bk"=>"中國信託","id"=>"************","timestamp"=>"**********","version"=>"1.0",
// "sign"=>"20121d0a52126f8820e1dfff3dee4a37","date_time"=>"2025-06-27  08:15:19","taken"=>"0","save"=>"600",
// "balance"=>"2300","abstract"=>"跨行轉　　","remark"=>"***********1234*");

// $data = array("bk"=>"中國信託","id"=>"************","timestamp"=>"**********","version"=>"1.0",
// "sign"=>"20121d0a52126f8820e1dfff3dee4a37","date_time"=>"2025-06-24  10:15:19","taken"=>"0","save"=>"15000",
// "balance"=>"30000","abstract"=>"跨行轉　　","remark"=>"***********1654*");

// // TWPAY
// $data = array("bk"=>"台灣PAY","id"=>"050900829946Q01","timestamp"=>"**********","version"=>"1.0",
// "sign"=>"20121d0a52126f8820e1dfff3dee4a37","date_time"=>"******** 090149","taken"=>"0","save"=>"500",
// "balance"=>"","abstract"=>"************-********","remark"=>"****2345");




// $data = json_encode($data,JSON_UNESCAPED_UNICODE);
// $http = curl_init("http://www.jiyun.com/api/Jiyun/save_bank");
// curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
// curl_setopt($http, CURLOPT_POST, 1);
// curl_setopt($http, CURLOPT_POSTFIELDS, $data);
// $result = curl_exec($http);
// echo json_encode($result, JSON_UNESCAPED_UNICODE);
// curl_close($http);







   // $url = "http://www.jiyun.com/api/Jiyun/test1";
   // $http = curl_init($url);
   // curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
   // $result = curl_exec($http);
   // echo json_encode($result, JSON_UNESCAPED_UNICODE);
   // curl_close($http);



   $data = array(
      'num'=>'6',
      'offer'=>'************',
      'spec'=>'09b4a5c219caa04129db9646ff0c962a',
      'addr'=>'**********'
   );

   $http = curl_init("http://www.jiyun.com/index/Daigou/aliOrderCreate");
   curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
   curl_setopt($http, CURLOPT_POST, 1);
   curl_setopt($http, CURLOPT_POSTFIELDS, $data);
   $result = curl_exec($http);
   echo $result;
   curl_close($http);



?>