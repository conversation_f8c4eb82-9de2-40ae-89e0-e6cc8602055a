<?php

namespace app\api\controller;

use app\common\controller\Backend;
use app\common\controller\Api;
use think\Exception;
use think\Config;
use think\Db;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

/**
 * 示例接口
 */
class Jiyun extends Api
{

    // 无需登录的接口,*表示全部
    protected $noNeedLogin = '*';
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = '*';

    
    
    /**
     * 读取.xlsx文件
     * @param mixed $filePath
     * @return array<array>
     */
    public function readXlsxFile($filePath)
    {
        // 创建阅读器实例
        $reader = new Xlsx();
        
        try {
            // 加载文件
            $spreadsheet = $reader->load($filePath);
            
            // 获取活动表单
            $sheet = $spreadsheet->getActiveSheet();
            
            // // 读取数据，例如读取第一行第一列的数据
            // $cellValue = $sheet->getCell('A1')->getValue();
            // echo '单元格 A1 的值是: ' . $cellValue;
            
            // 如果需要读取整行或整列数据，可以使用下面的方法
            $data = [];
            $rowIterator = $sheet->getRowIterator();
            foreach ($rowIterator as $key => $row) {
                if($key <= 2){
                    continue;
                }
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 这个很重要，它允许我们访问空的单元格
                $line_data = [];
                foreach ($cellIterator as $cell) {
                    if (!is_null($cell)) {
                        $value = $cell->getValue();
                        //echo '单元格 ' . $cell->getCoordinate() . ' 的值是: ' . $value . PHP_EOL;
                        // echo $cell->getCoordinate() . ':  ' . $value . PHP_EOL;
                        $line_data[] = $value;
                    }
                }
                // echo "</br>";
                $data[] = $line_data;
            }
            return $data;
        } catch (\Exception $e) {
            echo '读取文件时发生错误: ' . $e->getMessage();
        }
    }




    public function yiji($item, $level1, $db_data){
        for($j=0; $j < count($db_data); $j++){
            if( $level1 != $db_data[$j]['chineseName']){
                continue;
            }

            $line = $db_data[$j];

            $hy = empty($item[3])?'':'1';
            $hk = empty($item[4])?'':'1';
            $tmp1 = [];
            $tmp1[] = empty($item[5]) ? '' : '1';
            $tmp1[] = empty($item[6]) ? '' : '2';
            $tmp1 = array_filter($tmp1, function($value) {
                return $value !== "";
            });
            $ky = implode(',', $tmp1);

            $tmp2 = [];
            $tmp2[] = empty($item[9]) ? '' : $item[9];
            $tmp2[] = empty($item[10]) ? '' : $item[10];
            $tmp2 = array_filter($tmp2, function($value): bool {
                return $value !== "";
            });
            $judge = implode(',', $tmp2);

            $tmp3 = [];
            $tmp3[] = empty($item[11]) ? '' : $item[11];
            $tmp3[] = empty($item[12]) ? '' : $item[12];
            $tmp3 = array_filter($tmp3, function($value): bool {
                return $value !== "";
            });
            $refer = implode(',', $tmp3);

            $data = array(
                "id"      =>$line['id'],
                "name"    =>$line['chineseName'],
                //从索引数组 3 开始
                "hy"      =>$hy,
                "hk"      =>$hk,
                "ky"      =>$ky,
                "judge"   =>$judge,
                "refer"   =>$refer,
            );
            return $data;
        }

    }
    
    public function erji($item, $level1, $level2, $db_data){
        for($j=0; $j < count($db_data); $j++){
            if( $level1 != $db_data[$j]['chineseName']){
                continue;
            }
            if( !isset($db_data[$j]['list']) ){
                continue;
            }
            $db_data_tow = $db_data[$j]['list'];
            for($k=0; $k < count($db_data_tow); $k++){
                if( $level2 != $db_data_tow[$k]['chineseName']){
                    continue;
                }

                $line = $db_data_tow[$k];
                $hy = empty($item[3])?'':'1';
                $hk = empty($item[4])?'':'1';
                $tmp1 = [];
                $tmp1[] = empty($item[5]) ? '' : '1';
                $tmp1[] = empty($item[6]) ? '' : '2';
                $tmp1 = array_filter($tmp1, function($value) {
                    return $value !== "";
                });
                $ky = implode(',', $tmp1);

                $tmp2 = [];
                $tmp2[] = empty($item[9]) ? '' : $item[9];
                $tmp2[] = empty($item[10]) ? '' : $item[10];
                $tmp2 = array_filter($tmp2, function($value): bool {
                    return $value !== "";
                });
                $judge = implode(',', $tmp2);

                $tmp3 = [];
                $tmp3[] = empty($item[11]) ? '' : $item[11];
                $tmp3[] = empty($item[12]) ? '' : $item[12];
                $tmp3 = array_filter($tmp3, function($value): bool {
                    return $value !== "";
                });
                $refer = implode(',', $tmp3);

                $data = array(
                    "id"      =>$line['id'],
                    "name"    =>$line['chineseName'],
                    //从索引数组 3 开始
                    "hy"      =>$hy,
                    "hk"      =>$hk,
                    "ky"      =>$ky,
                    "judge"   =>$judge,
                    "refer"   =>$refer,
                );
                return $data;
            }
        }
    }



    public function sanji($item, $level1, $level2, $level3, $db_data){  
        for($j=0; $j < count($db_data); $j++){
            if( $level1 != $db_data[$j]['chineseName']){
                continue;
            }
            if( !isset($db_data[$j]['list']) ){
                continue;
            }
            $db_data_tow = $db_data[$j]['list'];
            for($k=0; $k < count($db_data_tow); $k++){
                if( $level2 != $db_data_tow[$k]['chineseName']){
                    continue;
                }
                if( !isset($db_data_tow[$k]['list']) ){
                    continue;
                }
                $db_data_three = $db_data_tow[$k]['list'];
                for($x=0; $x < count($db_data_three); $x++){
                    if( $level3 != $db_data_three[$x]['chineseName']){
                        continue;
                    }
                    $line = $db_data_three[$x];
                    $hy = empty($item[3])?'':'1';
                    $hk = empty($item[4])?'':'1';
                    $tmp1 = [];
                    $tmp1[] = empty($item[5]) ? '' : '1';
                    $tmp1[] = empty($item[6]) ? '' : '2';
                    $tmp1 = array_filter($tmp1, function($value) {
                        return $value !== "";
                    });
                    $ky = implode(',', $tmp1);

                    $tmp2 = [];
                    $tmp2[] = empty($item[9]) ? '' : $item[9];
                    $tmp2[] = empty($item[10]) ? '' : $item[10];
                    $tmp2 = array_filter($tmp2, function($value): bool {
                        return $value !== "";
                    });
                    $judge = implode(',', $tmp2);

                    $tmp3 = [];
                    $tmp3[] = empty($item[11]) ? '' : $item[11];
                    $tmp3[] = empty($item[12]) ? '' : $item[12];
                    $tmp3 = array_filter($tmp3, function($value): bool {
                        return $value !== "";
                    });
                    $refer = implode(',', $tmp3);

                    $data = array(
                        "id"      =>$line['id'],
                        "name"    =>$line['chineseName'],
                        //从索引数组 3 开始
                        "hy"      =>$hy,
                        "hk"      =>$hk,
                        "ky"      =>$ky,
                        "judge"   =>$judge,
                        "refer"   =>$refer,
                    );
                    return $data;
                }
            }
        }
    }


    public function main(){

         // //  第一步  编辑数据库表
         $rows = Db::name('alibaba_category')->select();
         $db_data = $this->list_to_tree($rows, 'categoryId', 'parentCateId');
 
 
 
          // 第二步 读取文件数据
         $xlsx_data = $this->readXlsxFile('D:/1/wp.xlsx');
 
         $data = [];
         $data_null = 0;
         for ($i=0; $i < count($xlsx_data); $i++) { 
         // for ($i=0; $i < 5; $i++) { 
             $item = $xlsx_data[$i];
             $level1 = $item[0];
             $level2 = $item[1];
             $level3 = $item[2];
             if( $level2 == null ){
                 $data_item = $this->yiji($item, $level1, $db_data);
                 if( $data_item == null ){
                     $data_null += 1;
                 }else{
                     $data[] = $data_item;
                 }
                 continue;
             }
 
             if( $level3 == null ){
                 $data_item = $this->erji($item, $level1, $level2, $db_data);
                 if( $data_item == null ){
                     $data_null += 1;
                 }else{
                     $data[] = $data_item;
                 }
                 continue;
             }
             
             $data_item = $this->sanji($item, $level1, $level2, $level3, $db_data);
             if( $data_item == null ){
                 $data_null += 1;
                 echo "sanji=" . json_encode($item, JSON_UNESCAPED_UNICODE) . "</br>";
             }else{
                 $data[] = $data_item;
             }
         }
         
         ini_set('max_execution_time', '0');
         for($i=0; $i < count($data); $i++) { 
             $item = $data[$i];
             $map = array(
                 'hy'  => $item['hy'],
                 'hk'  => $item['hk'],
                 'ky'  =>$item['ky'],
                 'judge'  =>$item['judge'],
                 'refer'  =>$item['refer']
             );
             $update_res = Db::name('alibaba_category')->where("id", $item['id'])->update($map);
             if( $update_res <= 0){
                 echo json_encode($item, JSON_UNESCAPED_UNICODE) . "</br>";
             }
         }

    }

    public function main2(){


        // $config = Config::get('site.alibaba');
        // $ali = new Alibaba($config);
        // $res = $ali->keywordQuery("帽子", 1, 10);

        //$token = $this->request->server('HTTP_TOKEN', $this->request->request('token', \think\Cookie::get('token')));

        ini_set('max_execution_time', '0');
        $language = "zh-tw";
        $config = Config::get('site.alibaba');
        $ali = new Alibaba($config);
        $this->get_data($ali, 0, $language);

       

        // $config = Config::get('site.kuaidi100');
        // $kuaidi = new KuaiDiHd($config);
        // $res = $kuaidi->query_do("zhongtong", "78917447152709");
        // return  $res;


    }


    
    //  //阿里巴巴类目 的数据爬虫
    public function get_data($ali, $id, $language ){

        $res = $ali->getById($id, $language);
        $res = json_decode($res, true);
        $code = $res['result']['code'];
        if($code != "S0000"){
            return;
        }
        if( empty($res['result']['result']['children']) ){
            return;
        }

        $children = $res['result']['result']['children'];
        $data = [];
        for($i=0; $i < count($children); $i++ ){
            $data[] = array(
                'categoryId'  =>$children[$i]['categoryId'],
                'chineseName'  =>$children[$i]['chineseName'],
                'translatedName'  =>$children[$i]['translatedName'],
                'language'  =>$children[$i]['language'],
                'leaf'  =>$children[$i]['leaf'],
                'level'  =>$children[$i]['level'],
                'parentCateId'  =>$children[$i]['parentCateId']
            );
        }
        $insert_res = Db::name('alibaba_category')->insertAll($data);
        if( $insert_res <= 0 ){
            echo "id=" . $id . "  更新0个" . "</br>";
        }
        for($i=0; $i < count($children); $i++){
            if($children[$i]['level'] == "3"){
                continue;
            }
            echo 'id=' . $children[$i]['categoryId'] . ', level=' . $children[$i]['level'] . "</br>";
            $this->get_data($ali, $children[$i]['categoryId'], $language);
        }
    }



}
