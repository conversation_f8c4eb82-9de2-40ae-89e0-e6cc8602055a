<?php

//https://open.1688.com/solution/solutionDetail.htm?solutionKey=1697014160788#apiAndMessageList
//https://open.1688.com/develop/app/list

namespace app\index\controller;

use addons\wechat\model\WechatCaptcha;
use app\common\controller\Frontend;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\Attachment;
use think\Config;
use think\Cookie;
use think\Hook;
use think\Session;
use think\Validate;
use think\Db;



/**
 * 会员中心
 */
class Login extends Frontend
{
    protected $layout = '';
    // protected $layout = 'login';
    protected $noNeedLogin = ['login', 'register', 'third', 'user', 'member', 'tran_agree', 'goods_type', 'prohibited', 'getCode', 'line_login','notice_msg'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'), '/');
        }

        //监听注册登录退出的事件
        Hook::add('user_login_successed', function ($user) use ($auth) {
            $expire = input('post.keeplogin') ? 30 * 86400 : 0;
            Cookie::set('uid', $user->id, $expire);
            Cookie::set('token', $auth->getToken(), $expire);
        });
        Hook::add('user_register_successed', function ($user) use ($auth) {
            Cookie::set('uid', $user->id);
            Cookie::set('token', $auth->getToken());
        });
        Hook::add('user_delete_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
    }

    /**
     * 会员中心
     */
    public function index()
    {
        return $this->view->fetch('login');
    }

    /**
     * 注册会员
     */
    public function register()
    {
        if ($this->auth->id) {
            $this->success(__('You\'ve logged in, do not login again'), url('user/index'));
        }
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
            $lineid = $this->request->post('lineid');
            // $lineid = "U744ed97aed973c16746f8ff087bbc2d0";


            /*检测用户是否被拉黑*/
			// $blacklist_user = Config::get('site.blacklist_user');
			// $user_array = explode(',',$blacklist_user);
			// if(in_array($mobile,$user_array)){
			// 	$this->error('系統錯誤，請聯絡客服');
			// }
            $code = $this->request->post('code');
            //$username = $this->request->post('username');
            $rule = [
                'code'  => 'require',
                'mobile'    => 'require|unique:user|regex:/^09\d{8}$/',
            ];

            $msg = [
                'mobile.require' => 'Mobile phone number cannot be empty',
                'mobile.regex'  => 'Incorrect format of mobile phone number',
                'mobile.unique'  => 'Mobile number already exists',
                'code.require' => 'code cannot be empty',
            ];
            $data = [
                'mobile'        => $mobile,
                'code'          => $code,
            ];
            $validate = new Validate($rule, $msg);
            $result = $validate->check($data);
            if (!$result) {
                $this->error(__($validate->getError()));
            }

            if ($this->auth->register($mobile, $code, $lineid)) {
                $this->success(__('Sign up successful'), url('user/index'), '', 3);
            } else {
                $this->error($this->auth->getError());
            }
        }
                //判断来源
                // $referer = $this->request->server('HTTP_REFERER', '', 'url_clean');
                // if (!$url && $referer && !preg_match("/(user\/login|user\/register|user\/logout)/i", $referer)) {
                //     $url = $referer;
                // }
        $lineid = $this->request->param('lineid');
        if( !empty($lineid) ){
            $user = $this->query_record('user', array('lineid'=>$lineid));
            $this->view->assign('username', $user['username']);
        }

        $this->view->assign('lineid', $lineid);
        $this->view->assign('title', __('Register'));
        return $this->view->fetch();
    }

    /**
     * 会员登录
     */
    public function login()
    {
        // $url = $this->request->request('url', '', 'url_clean');
        // if ($this->auth->id) {
        //     $this->success(__('You\'ve logged in, do not login again'), $url ?: url('user/index'));
        // }

        if ($this->auth->id) {
            $this->success(__('You\'ve logged in, do not login again'), url('user/index'));
        }

        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
            $code = $this->request->post('code', '', null);
            $rule = [
                'mobile'   => 'require|length:10',
                'code'  => 'require|length:6',
            ];

            $msg = [
                'mobile.require'  => 'Account can not be empty',
                'mobile.length'   => 'Account must 10 characters',
                'code.require' => 'code can not be empty',
                'code.length'  => 'code must be 6 characters',
            ];
            $data = [
                'mobile'   => $mobile,
                'code'  => $code,
            ];
            $validate = new Validate($rule, $msg);
            $result = $validate->check($data);
            if (!$result) {
                $this->error(__($validate->getError()), null, ['token' => $this->request->token()]);
            }
            if ($this->auth->login($mobile, $code)) {
                $this->success(__('Logged in successful'), url('user/index'), '', 1);
            } else {
                $this->error($this->auth->getError());
            }
        }

        $banner = $this->query_records('banner', array('type'=>1, 'status'=>'normal'));

        $this->view->assign('banner', $banner);
        $this->view->assign('title', __('Login'));
        return $this->view->fetch();
    }


    /**
     * 退出登录
     */
    public function logout()
    {
        // if ($this->request->isPost()) {
        //     $this->token();
        //     //退出本站
        //     $this->auth->logout();
        //     $this->success(__('Logout successful'), url('login/login'), '', 1);
        // }
        // $html = "<form id='logout_submit' name='logout_submit' action='' method='post'>" . token() . "<input type='submit' value='ok' style='display:none;'></form>";
        // $html .= "<script>document.forms['logout_submit'].submit();</script>";
        // return $html;

        $this->auth->logout();
        $this->success(__('Logout successful'), url('login/login'));
    }


    /* 注册获取验证码 */
    public function getCode(){

		$type = $this->auth->getTypecode()?$this->auth->getTypecode():'0';
		$this->sendCode($type);
	}
    
    public function user(){


        return $this->view->fetch();
    }

     public function member(){

        return $this->view->fetch('');
     }

     public function tran_agree(){

             return $this->view->fetch('login/tran_agree');
     }

     public function goods_type(){

        return $this->view->fetch('login/goods_type');
     }

     public function prohibited(){

        return $this->view->fetch('login/prohibited');
     }

     public function notice_msg(){

        return $this->view->fetch('login/notice_msg');
     }

}
