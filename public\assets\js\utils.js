const utils = {
    // 时间戳转换为日期格式
    formatDate: function (timestamp, type) {
        if (!timestamp) return '';

        // 自动兼容10位秒级时间戳
        if (typeof timestamp === 'number' && timestamp.toString().length === 10) {
            timestamp = timestamp * 1000;
        }
        if (typeof timestamp === 'string' && /^\d{10}$/.test(timestamp)) {
            timestamp = parseInt(timestamp) * 1000;
        }

        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return ''; // 检查日期是否有效

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');
        const second = String(date.getSeconds()).padStart(2, '0');

        switch (type) {
            case 1: // YYYY-MM-DD
                return `${year}-${month}-${day}`;
            case 2: // YYYY-MM-DD HH:mm:ss
                return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            case 3: // MM-DD HH:mm
                return `${month}-${day} ${hour}:${minute}`;
            case 4: // HH:mm:ss
                return `${hour}:${minute}:${second}`;
            default:
                return `${year}-${month}-${day}`;
        }
    },

    // 字符串转换为数组
    stringToArray: function (str, type = 'array', separator = ',') {
        if (typeof str !== 'string') return type === 'length' ? 0 : [];
        const arr = str.split(separator);
        return type === 'length' ? arr.length : arr;
    },

    // 进位制重量计算
    calculateWeight: function (weight) {
        const num = Number(weight);
        if (isNaN(num) || typeof weight === 'boolean' || weight === null) {
            console.warn('[calculateWeight] 无效输入，已返回0:', weight);
            return 0;
        }
        // 处理负值（根据需求可选择支持或不支持）
        if (num < 0) {
            console.warn('[calculateWeight] 不支持负值，已取绝对值处理:', weight);
            return calculateWeight(Math.abs(num));
        }
        const integerPart = Math.floor(num);
        const decimal = num - integerPart;
        const firstDecimal = Math.floor(decimal * 10);
        return firstDecimal >= 1 ? integerPart + 1 : integerPart;
    },

    // 複製
    copyText: function (text, onSuccess, onError) {
        const defaultSuccess = () => console.log('複製成功');
        const defaultError = (err) => console.error('複製失敗:', err);

        const successCallback = onSuccess || defaultSuccess;
        const errorCallback = onError || defaultError;

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text).then(() => {
                successCallback();
            }).catch(err => {
                errorCallback(err);
            });
        } else {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                successCallback();
            } catch (err) {
                errorCallback(err);
            } finally {
                document.body.removeChild(textarea);
            }
        }
    },

    // 手机号密文处理
    maskMobile: function (mobile) {
        // 检查输入是否为字符串或数字
        if (typeof mobile !== 'string' && typeof mobile !== 'number') {
            console.warn('[maskMobile] 输入必须是字符串或数字:', mobile);
            return mobile;
        }

        // 转换为字符串
        const mobileStr = String(mobile);

        // 验证是否为09开头的10位数字
        const mobileRegex = /^09\d{8}$/;
        if (!mobileRegex.test(mobileStr)) {
            console.warn('[maskMobile] 手机号格式不正确，应为09开头的10位数字:', mobile);
            return mobile; // 格式不正确时返回原值
        }

        // 对第3位到第6位进行加密处理（索引2-5）
        const maskedMobile = mobileStr.substring(0, 2) + '****' + mobileStr.substring(6);

        return maskedMobile;
    }

};
