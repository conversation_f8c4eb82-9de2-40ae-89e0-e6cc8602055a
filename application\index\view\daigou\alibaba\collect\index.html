{include file="common/resources" /}
{include file="common/daigouresourses" /}
<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box" id="collect">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
         <!-- 收藏 -->
    <div id="app" class="max-w-screen-2xl mx-auto p-8">
        <div class="text-lg font-bold mb-6">我的收藏</div>
        <div class="grid grid-cols-5 gap-6">
            <div v-for="item in itemList" :key="item.id" class="bg-white rounded-lg flex flex-col " style="margin-right: 20px;" @click="godetail(item.offer)">
                <img :src="getImageProxyUrl(item.images)" alt="商品图片" class="object-cover rounded mb-2" >
                <div class="text-gray-800 text-sm mb-1 break-words overflow-hidden" style="height: 40px;display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">{{ item.subject }}</div>
                <div class="text-pink-500 font-bold text-base mb-1">￥{{ item.price }}</div>
            </div>
        </div>
    </div>
    </div>
</div>

<script>
    
    const app = new Vue({
        el: '#collect',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            itemList: <?php echo json_encode($list); ?>, // 后端传入
        },
        mounted(){
            console.log(this.itemList,123123)
        },
        methods: {
            // 图片代理方法，解决跨域问题
            getImageProxyUrl(originalUrl) {
                if (!originalUrl) return '';
                // 使用图片代理
                return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
            },
            godetail(id){
                window.location.href = `aliGoods?offer=${id}`;
            }
        }
    })
</script>