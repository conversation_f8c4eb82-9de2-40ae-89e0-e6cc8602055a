{include file="common/resources" /}
{load href="__CDN__/assets/css/swiper.min.css,
__CDN__/assets/css/swiper-bundle.min.css," /}
{load
href="__CDN__/assets/js/swiper.min.js,
__CDN__/assets/js/swiper-bundle.min.js,
__CDN__/assets/js/common.js,
" /}
<div style="width: 1200px; position: absolute; left: 50%; top: 64px; transform: translateX(-50%); z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="nav">
    <img src="__CDN__/assets/img/pc/nav_logo.png" alt="" style="height: 50%; cursor: pointer;" onclick="window.location.href='/index/login/login'">
    <div class="nav-right">
        <div style="display: flex; align-items: center; gap: 8px;">
            <img class="app-download" src="__CDN__/assets/img/pc/new_index/parcel18.svg" alt="APP下載">
            <a href="javascript:void(0)" class="terms-link" onclick="window.scrollTo({top: document.documentElement.scrollHeight, behavior: 'smooth'})">APP下載</a>
        </div>
        <div class="login-message" onclick="window.open('/index/notice/index')">
            <img class="pointer" src="__CDN__/assets/img/pc/new_index/parcel19.svg" alt="通知公告">
            {if $notice_num}
            <span class="login-message-num">{$notice_num}</span>
            {/if}
            <a href="#" class="terms-link">通知公告</a>
        </div>
    </div>
</div>
<div class="swiper mySwiper">
    <div class="swiper-wrapper">
        {volist name="banner" id="item"  }
        <tr>
            <div class="swiper-slide"><img style="width: 100%; height: 450px" src="__CDN__{$item['path_image']}" alt="" /></div>
        </tr>
        {/volist}
    </div>
    <div class="swiper-pagination swiper_point"></div>
</div>

<div  id="content-container" class="container" style="position: relative; display: flex; flex-direction: column;">
    <!-- 引入底部导航栏组件 -->
    {include file="common/bottom_nav" /}
    <div class="login-menu">
        <div class="login-menu-item"  @click="openTool('calc')">
            <img src="__CDN__/assets/img/pc/login/menu01.png" alt="運費試算">
            <span>運費試算</span>
        </div>
        <div class="login-menu-item" @click="openTool('send')" >
            <img src="__CDN__/assets/img/pc/login/menu02.png" alt="能寄嗎" style="width: 96px; height: 72px;">
            <span>能寄嗎</span>
        </div>
        <div class="login-menu-item" onclick="window.open('/index/notice/details.html?id=19')">
            <img src="__CDN__/assets/img/pc/login/menu03.png" alt="禁運説明" style="width: 96px; height: 72px;">
            <span>禁運説明</span>
        </div>
        <div class="login-menu-item" onclick="window.open('/index/notice/details.html?id=25')">
            <img src="__CDN__/assets/img/pc/login/menu04.png" style="width: 96px; height: 72px;" alt="集運説明">
            <span>集運説明</span>
        </div>
    </div>
    <div class="process">
        <div class="process-tabs">
            <div class="tab" :class="{ active: activeProcessTab === 0 }" @click="switchProcessTab(0)">集運流程</div>
            <div class="tab" :class="{ active: activeProcessTab === 1 }" @click="switchProcessTab(1)">代購流程</div>
        </div>
        <div class="process-content">
            <!-- 集运流程 -->
            <div class="process-flow" id="shipping-process" v-show="activeProcessTab === 0">
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/process01.png" alt="選擇要發的包裹">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow03.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>1</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">選擇要發的包裹</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/process02.png" alt="選擇寄運方式和服務">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow03.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>2</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">選擇寄運方式和服務</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/process03.png" alt="支付運費">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow03.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>3</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">支付運費</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/process04.png" alt="包裹狀態實時追蹤">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow03.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>4</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">包裹狀態實時追蹤</div>
                </div> 
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/process05.png" alt="宅配到府">
                    </div>
                    <div class="step-number">5</div>
                    <div class="step-title">宅配到府</div>
                </div>
            </div>

            <!-- 代购流程 -->
            <div class="process-flow" id="purchase-process" v-show="activeProcessTab === 1">
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/shop01.png" alt="貼入淘寶/阿里巴巴商品鏈接">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow04.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>1</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">貼入淘寶/阿里巴巴商品鏈接</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/shop02.png" alt="選擇商品規格/數量">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow04.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>2</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">選擇商品規格/數量</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/shop03.png" alt="選擇代收倉庫">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow04.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>3</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">選擇代收倉庫</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/shop04.png" alt="銀行匯款支付">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow04.png" alt="箭頭">
                    </div>
                    <div class="step-number">
                        <span>4</span>
                        <div class="step-number-line"></div> 
                    </div>
                    <div class="step-title">銀行匯款支付</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/shop05.png" alt="代購完成">
                    </div>
                    <div class="step-arrow">
                        <img src="__CDN__/assets/img/pc/login/step_arrow04.png" alt="箭頭">
                    </div> 
                    <div class="step-number">
                        <span>5</span>
                        <div class="step-number-line"></div>
                    </div>
                    <div class="step-title">代購完成</div>
                </div>
                
                <div class="process-step">
                    <div class="step-image">
                        <img src="__CDN__/assets/img/pc/login/shop06.png" alt="等待收貨">
                    </div>
                    <div class="step-number">6</div>
                    <div class="step-title">等待收貨</div>
                </div>
            </div>
        </div>
        <div class="process-more" onclick="window.open('/index/notice/index/type/2')">
            瞭解更多>>
        </div>
    </div>
    <div class="comment-banner">
        <div class="comment-banner-title">會員的評價</div>
        <div class="comment-swiper">
            <div class="swiper-button swiper-button-prev"></div>
            <div class="swiper commentSwiper">
                <div class="swiper-wrapper">
                    <!-- 第一组评论 (6个) -->
                    <div class="swiper-slide">
                        <div class="comment-grid">
                            <!-- 评论1 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">21小時前</div>
                                        </div>
                                        
                                    </div>
                                    <div class="comment-replies">7則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    不愧是他家支付方式還是挺簡單，一推就搞定，操作就能完成文案，超級方便！
                                </div>
                            </div>
                            
                            <!-- 评论2 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">2天前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">21則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    集運省下來的錢，剛好拿去買杯奶粉，爽！
                                </div>
                            </div>
                            
                            <!-- 评论3 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">閔**</div>
                                            <div class="comment-time">1小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">8則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star"></span>
                                </div>
                                <div class="comment-content">
                                    原本以為沒差，結果每次運費都析，久了超有感！
                                </div>
                            </div>

                            <!-- 评论4 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">21小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">7則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    不愧是他家支付方式還是挺簡單，一推就搞定，操作就能完成文案，超級方便！
                                </div>
                            </div>

                            <!-- 评论5 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">閔**</div>
                                            <div class="comment-time">1小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">8則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star"></span>
                                </div>
                                <div class="comment-content">
                                    原本以為沒差，結果每次運費都析，久了超有感！
                                </div>
                            </div>

                            <!-- 评论6 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">2天前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">21則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    集運省下來的錢，剛好拿去買杯奶粉，爽！
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二组评论 (6个) -->
                    <div class="swiper-slide">
                        <div class="comment-grid">
                            <!-- 评论7 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">閔**</div>
                                            <div class="comment-time">1小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">8則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star"></span>
                                </div>
                                <div class="comment-content">
                                    原本以為沒差，結果每次運費都析，久了超有感！
                                </div>
                            </div>
                            
                            <!-- 复制其余评论 8-12 -->
                            <!-- 评论8 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">閔**</div>
                                            <div class="comment-time">1小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">8則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star"></span>
                                </div>
                                <div class="comment-content">
                                    原本以為沒差，結果每次運費都析，久了超有感！
                                </div>
                            </div>

                            <!-- 评论9 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">21小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">7則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    不愧是他家支付方式還是挺簡單，一推就搞定，操作就能完成文案，超級方便！
                                </div>
                            </div>

                            <!-- 评论10 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">2天前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">21則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    集運省下來的錢，剛好拿去買杯奶粉，爽！
                                </div>
                            </div>

                            <!-- 评论11 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">閔**</div>
                                            <div class="comment-time">1小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">8則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star"></span>
                                </div>
                                <div class="comment-content">
                                    原本以為沒差，結果每次運費都析，久了超有感！
                                </div>
                            </div>

                            <!-- 评论12 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">21小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">7則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    不愧是他家支付方式還是挺簡單，一推就搞定，操作就能完成文案，超級方便！
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 可以继续添加更多组 -->
                    <!-- 第三组评论 -->
                    <div class="swiper-slide">
                        <div class="comment-grid">
                            <!-- 评论13-18 -->
                            <!-- 复制之前的评论结构... -->
                            <!-- 评论13 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">閔**</div>
                                            <div class="comment-time">1小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">8則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star"></span>
                                </div>
                                <div class="comment-content">
                                    原本以為沒差，結果每次運費都析，久了超有感！
                                </div>
                            </div>

                            <!-- 评论14 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">閔**</div>
                                            <div class="comment-time">1小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">8則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star"></span>
                                </div>
                                <div class="comment-content">
                                    原本以為沒差，結果每次運費都析，久了超有感！
                                </div>
                            </div>

                            <!-- 评论15 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">21小時前</div>
                                        </div>
                                        
                                    </div>
                                    <div class="comment-replies">7則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    不愧是他家支付方式還是挺簡單，一推就搞定，操作就能完成文案，超級方便！
                                </div>
                            </div>

                            <!-- 评论16 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">21小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">7則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    不愧是他家支付方式還是挺簡單，一推就搞定，操作就能完成文案，超級方便！
                                </div>
                            </div>

                            <!-- 评论17 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">2天前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">21則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    集運省下來的錢，剛好拿去買杯奶粉，爽！
                                </div>
                            </div>

                            <!-- 评论18 -->
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-user">
                                        <div class="user-avatar">
                                            <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="用戶頭像">
                                        </div>
                                        <div class="user-info">
                                            <div class="username">李**</div>
                                            <div class="comment-time">21小時前</div>
                                        </div>
                                    </div>
                                    <div class="comment-replies">7則評論</div>
                                </div>
                                <div class="comment-rating">
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                    <span class="star star-filled"></span>
                                </div>
                                <div class="comment-content">
                                    不愧是他家支付方式還是挺簡單，一推就搞定，操作就能完成文案，超級方便！
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="swiper-button swiper-button-next"></div>
        </div>
    </div>
    <div class="jiyun-show">
        <div class="jiyun-show-title">為甚麼選擇EZ集運通？</div>
        <div class="jiyun-show-list">
            <div class="jiyun-show-item">
                <div class="jiyun-icon">
                    <img src="__CDN__/assets/img/pc/login/jiyun01.png" alt="貨運穩定迅速">
                </div>
                <h3 class="jiyun-item-title">貨運穩定迅速</h3>
                <div class="jiyun-item-divider"></div>
                <p class="jiyun-item-desc">海外貨件每週固定航班出庫，部份倉庫每週出庫高達五次</p>
            </div>
    
            <div class="jiyun-show-item">
                <div class="jiyun-icon">
                    <img src="__CDN__/assets/img/pc/login/jiyun02.png" alt="收費清晰透明">
                </div>
                <h3 class="jiyun-item-title">收費清晰透明</h3>
                <div class="jiyun-item-divider"></div>
                <p class="jiyun-item-desc">代購服務費全免，國際運費另外以貨件實重計算</p>
            </div>
    
            <div class="jiyun-show-item">
                <div class="jiyun-icon">
                    <img src="__CDN__/assets/img/pc/login/jiyun03.png" alt="多個貨件合併">
                </div>
                <h3 class="jiyun-item-title">多個貨件合併</h3>
                <div class="jiyun-item-divider"></div>
                <p class="jiyun-item-desc">會員可多包裹一起打包集運，節省額外運費</p>
            </div>
    
            <div class="jiyun-show-item">
                <div class="jiyun-icon">
                    <img src="__CDN__/assets/img/pc/login/jiyun04.png" alt="專業客服團隊">
                </div>
                <h3 class="jiyun-item-title">專業客服團隊</h3>
                <div class="jiyun-item-divider"></div>
                <p class="jiyun-item-desc">設有專業的客服團隊，線上為你解答集運疑難</p>
            </div>
        </div>
    </div>
    <div v-if="!lineLogin" class="user-section login-section" style="position: absolute;top: -404px; right: 0; width: 340px; height: 360px; z-index:1000">
        <div class="login-title mt24"
            style="text-align:center;font-size:24px;font-weight:600;color:#222;margin-bottom:24px;">{:__('User login')}
        </div>
        <div class="login-main">
            <form name="form" id="login-form" class="form-vertical" method="POST" action="login/login">
                <input type="hidden" name="url" value="{$url|htmlentities}" />
                {:token()}

                <!-- 手机号输入框 -->
                <div class="form-group">
                    <el-input v-model="form.mobile" placeholder="{:__('Placeholder mobile')}" prefix-icon="el-icon-user"
                        name="account" clearable @input="onMobileInput">
                    </el-input>
                </div>

                <!-- 密碼输入框 -->
                <!-- <div class="form-group">
                    <el-input v-model="form.password" placeholder="{:__('Placeholder mobile')}" prefix-icon="el-icon-user"
                        name="account" clearable>
                    </el-input>
                </div> -->

                <!-- 验证码输入框和按钮 -->
                <div class="form-group" style="margin-bottom: 48px">
                    <div class="verification-group">
                        <el-input v-model="form.captcha" placeholder="簡訊驗證碼" prefix-icon="el-icon-mobile-phone"
                            name="captcha" @input="onCaptchaInput" @keyup.enter.native="submitLogin" clearable>
                        </el-input>
                        <button type="button" :disabled="countDown > 0" @click="sendVerification" class="ez-send-code-btn" style="height: 32px;line-height: 10px;">
                            {{ countDown > 0 ? `重發(${countDown}s)` : '發送驗證碼' }}
                        </button>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <div class="form-group">
                    <el-button type="danger" class="login-btn" @click="submitLogin" :loading="loading">
                        {:__('Login')}
                    </el-button>
                </div>

                <!-- LINE登录按钮 -->
                <div class="form-group">
                    <el-button class="line-login-btn" type="success" @click="submitLineLogin">LINE登錄</el-button>
                </div>

                <!-- 没有账号去注册 -->
                <div class="form-group no-account">
                        <span>沒有賬號</span>
<!--                        <a href="{:url('login/register')}?url={$url|urlencode|htmlentities}" class="register-link">去注冊</a>-->
                        <a href="{:url('login/register')}" class="register-link">去注冊</a>
                    </div>
            </form>
        </div>
    </div>
</div>
{include file="common/footer" /}

<script>
    var swiper;
    var commentSwiper;
    
    // 初始化轮播并存储实例
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化banner轮播
        swiper = new Swiper(".mySwiper", {
            loop: true,
            autoplay: {
                delay: 6000,
                disableOnInteraction: false,
            },
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
                renderBullet: function (index, className) {
                    return '<span class="' + className + '"></span>';
                },
            },
        });
        
        // 初始化评论轮播
        commentSwiper = new Swiper(".commentSwiper", {
            slidesPerView: 1,
            slidesPerGroup: 1,
            spaceBetween: 30,
            loop: true,
            loopAdditionalSlides: 3,
            speed: 800,
            observer: true,
            observeParents: true,
            autoplay: {
                delay: 3500,
                disableOnInteraction: false,
            },
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            }
        });
    });
    
    // 页面卸载时清除轮播定时器
    window.addEventListener('beforeunload', function() {
        // 销毁swiper实例，清除定时器
        if (swiper && swiper.destroy) {
            swiper.autoplay.stop();
            swiper.destroy(true, true);
        }
        
        if (commentSwiper && commentSwiper.destroy) {
            commentSwiper.autoplay.stop();
            commentSwiper.destroy(true, true);
        }
    });
</script>

<script>
    const app = new Vue({
        el: '#content-container',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data() {
            return {
                form: {
                    mobile: '',
                    captcha: '',
                    password: ''
                },
                countDown: 0,
                timer: null,
                loading: false,
                lineUrl:'',
                lineLogin:false,
                activeProcessTab: 0, // 控制当前显示的流程选项卡
            }
        },
        mounted(){            
        },
        beforeDestroy() {
            clearInterval(this.timer); // 组件销毁时清除定时器
            // 也在这里清除轮播定时器，以防Vue组件卸载
            if (window.swiper && window.swiper.destroy) {
                window.swiper.autoplay.stop();
                window.swiper.destroy(true, true);
            }
            
            if (window.commentSwiper && window.commentSwiper.destroy) {
                window.commentSwiper.autoplay.stop();
                window.commentSwiper.destroy(true, true);
            }
        },
        methods: {
            // 流程选项卡切换方法
            switchProcessTab(tab) {
                this.activeProcessTab = tab;
            },
            
            // 表单相关方法
            onMobileInput(e) {
                // 只允许输入数字，且最大长度为10
                let val = e.toString().replace(/[^\d]/g, '').slice(0, 10);
                this.form.mobile = val;
            },
            
            onCaptchaInput(e) {
                let val = e.toString().replace(/[^\d]/g, '').slice(0, 6);
                this.form.captcha = val;
            },
            
            // 提交登录
            submitLogin: _.debounce(async function() {
                try {
                    
                    // 验证手机号和验证码
                    if (!this.form.mobile) {
                        this.$message.error('請輸入手機號碼');
                        return;
                    }
                    if (!/^09\d{8}$/.test(this.form.mobile)) {
                        this.$message.error('手機號碼格式不正確');
                        return;
                    }
                    if (!this.form.captcha) {
                        this.$message.error('請輸入驗證碼');
                        return;
                    }
                    // if (!this.form.password) {
                    //     this.$message.error('請輸入密碼');
                    //     return;
                    // }
                    
                    this.loading = true;
                    
                    const data = {
                        mobile: this.form.mobile.trim(),
                        code: this.form.captcha.trim(), // 使用原来的password字段传递验证码
                        // code:this.form.password.trim()
                    };
                    
                    let res = await axios.post('login', data, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    
                    this.loading = false;
                    
                    if (res.data.code === 1) {
                        this.$message.success(res.data.msg);
                        // 设置sessionStorage标记，表示是从登录页面过来的
                        sessionStorage.setItem('fromLogin', 'true');
                        setTimeout(() => {
                            window.location.href = res.data.url;
                        }, 1000);
                    } else {
                        this.$message.error(res.data.msg);
                    }
                } catch (err) {
                    this.loading = false;
                    this.$message.error('登錄失敗，請稍後再試！');
                }
            }, 800),
            sendVerification: _.debounce(async function() {
                if (!this.form.mobile) {
                    this.$message.error('請輸入手機號碼');
                    return;
                }

                if (!/^09\d{8}$/.test(this.form.mobile)) {
                    this.$message.error('手機號碼格式不正確');
                    return;
                }

                try {
                    // 调用发送验证码接口
                    let res = await axios.post('getCode', {
                        mobile: this.form.mobile,
                        type:1
                    }, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });

                    if (res.data.code === 1) {
                        this.$message.success('驗證碼已發送');
                        this.startCountDown();
                    } else {
                        this.$message.error(res.data.msg || '發送驗證碼失敗');
                    }

                } catch (err) {
                    this.$message.error('發送驗證碼失敗，請稍後再試');
                }
            },200),
           
            startCountDown() {
                this.countDown = 180; // 180秒倒计时
                clearInterval(this.timer);
                this.timer = setInterval(() => {
                    if (this.countDown > 0) {
                        this.countDown--;
                    } else {
                        clearInterval(this.timer);
                    }
                }, 1000);
            },
            formatDateTime(timestamp) {
                if (!timestamp) return '';
                const date = new Date(timestamp * 1000); // 转换为毫秒
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            },

            // line登录
            submitLineLogin: _.debounce(function(){
                // 设置sessionStorage标记，表示是从登录页面过来的
                sessionStorage.setItem('fromLogin', 'true');
                axios.get('/index/third_party/line_user_login',{
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    }).then(res=>{
                    window.location.href = res.data.url;
                })
            }, 200),
            // 調用公共JS中的openLine函數
            openLine(){
                window.openLine();
            },
            openTool(type){
                window.open(`/index/tool/${type}`)
            },
        }
    });
</script>

<style>
    .login-menu {
        width: 100%;
        height: 204px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .login-menu-item {
        width: 280px;
        height: 108px;
        position: relative;
        padding: 18px;
        display: flex;
        justify-content: flex-start;
        gap: 36px;
        cursor: pointer;
        border-radius: 8px;
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
        overflow: hidden;
        /* 确保背景不会超出边界 */
        background-color: #fff;
        /* 确保底色为白色 */
        border: 1px solid #F4F4F4;
        /* 添加边框效果 */
    }

    /* 添加淡化背景图效果 */
    .login-menu-item::after {
    content: "";
    position: absolute;
    top: -24px;
    right: -24px;
    width: 130px; /* 背景图只占右半部分 */
    height: 150px;
    background-image: url('__CDN__/assets/img/pc/login/menu_bg.png');
    transform: rotate(-10.87deg);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: right center;
    opacity: 0.08; /* 非常淡的透明度 */
    z-index: 1;
    pointer-events: none; /* 不影响鼠标事件 */
}

    .login-menu-item:hover {
        transform: scale(1.05);
        border: 1px solid #EF436D;
    }

    .login-menu-item span {
        color: #3D3D3D;
        font-size: 24px;
        line-height: 72px;
        font-weight: 500;
        position: relative;
        z-index: 2;
    }

    .login-menu-item img {
        width: 72px;
        height: 72px;
        object-fit: contain;
        position: relative;
        z-index: 2;
    }

    /*轮播图*/
    .mySwiper {
        width: 100%;
        height: 450px;
    }

    .mySwiper .swiper-slide {
        text-align: center;
        font-size: 18px;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .mySwiper .swiper-slide img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .mySwiper .swiper-pagination-bullet {
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        color: #000;
        opacity: 1;
        background: rgba(0, 0, 0, 0.2);
    }

    .mySwiper .swiper-pagination-bullet-active {
        color: #fff;
        background: #007aff;
    }

    .swiper_point {
        text-align: left;
        padding-left: 19%;
    }

    .swiper_point .swiper-pagination-bullet {
        background: #cc93a2;
        transform: scale(0.5);
    }

    .swiper_point .swiper-pagination-bullet.swiper-pagination-bullet-active {
        background: url('__CDN__/assets/img/pc/new_index/next.png');
        transform: scale(1);
        background-size: 100%;
    }

    /* Element UI 自定义样式 */
    .login-main {
        padding: 0 20px;
    }
    
    .login-main .el-input__inner {
        height: 40px;
        border-radius: 4px;
    }
    
    .login-main .el-input__prefix {
        left: 12px;
    }
    
    .login-main .el-input__inner {
        padding-left: 40px;
    }
    
    .verification-group {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .verification-group .el-input {
        flex: 1;
    }
    
    .verification-btn {
        border: 1px solid #EF436D;
        color: #EF436D;
        background-color: transparent;
        height: 42px;
        padding: 0 15px;
        white-space: nowrap;
        border-radius: 4px;
    }
    
    .verification-btn:hover:not(:disabled) {
        background-color: #FFF1F4;
    }
    
    .verification-btn:disabled {
        border-color: #dcdfe6;
        color: #c0c4cc;
        background-color: #fff;
    }
    
    .login-btn, .line-login-btn {
        width: 100%;
        height: 40px;
        font-size: 16px;
        border-radius: 4px;
    }
    
    .login-btn.el-button--danger {
        background-color: #EF436D;
        border-color: #EF436D;
    }
    
    .line-login-btn.el-button--success {
        background-color: #22B573;
        border-color: #22B573;
    }
    
    .no-account {
        text-align: center;
        font-size: 14px;
        color: #666;
        margin-top: 16px;
    }
    
    .register-link {
        color: #EF436D;
        margin-left: 5px;
        text-decoration: underline;
    }

    /* 评论轮播相关样式 */
.comment-banner {
    width: 100%; /* 改为100%适应父容器 */
    min-height: 360px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden; /* 确保内容不溢出 */
}

.comment-banner-title {
    position: relative;
    font-size: 36px;
    width: 100%;
    max-width: 1200px;
    color: #333333;
    text-align: start;
    margin: 48px 0 12px 0;
}

.comment-banner-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 70px;
    height: 4px;
    background-color: #EF436D;
    border-radius: 4px;
}

.comment-swiper {
    width: 100%;
    max-width: 1200px;
    position: relative;

}

.commentSwiper { 
    width: 1100px;
    height: auto;
    margin: 0 auto;
    padding: 20px;
    user-select: none;
}

/* 评论网格布局 */
.comment-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 20px;
    width: 100%;
    height: 340px;
}

/* 评论项样式 */
.comment-item {
    background-color: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 16px;
    width: 100%;
    height: 144px;
    display: flex;
    flex-direction: column;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.comment-user {
    display: flex;
    align-items: flex-start;
}

.user-avatar {
    width: 27px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.username {
    font-size: 12px;
    color: #333;
    margin-bottom: 4px;
}

.comment-time {
    font-size: 12px;
    color: #333333;
    margin-bottom: 4px;
}

.comment-rating {
    margin-bottom: 21px;
    display: flex;
    align-items: center;
}

.star {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 4px;
    margin-top: -1px;
    background-image: url('__CDN__/assets/img/pc/new_index/star-empty.svg');
    background-size: cover;
}

.star-filled {
    background-image: url('__CDN__/assets/img/pc/new_index/star-filled.svg');
}

.comment-replies {
    font-size: 12px;
    color: #333;
    text-align: right;
}

.comment-content {
    font-size: 12px;
    color: #333;
    line-height: 1.2;
    flex: 1;
    overflow: hidden;
}

/* Swiper grid specific styles */
.swiper-wrapper {
    height: auto;
}

.swiper-slide {
    height: auto;
}

/* 轮播导航按钮样式 */
.swiper-button-next,
.swiper-button-prev {
    width: 32px;
    height: 32px;
    background-image: url('__CDN__/assets/img/pc/new_index/snext.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-top: 0;
    opacity: 0.5;
    transition: 0.3s;
    z-index: 10;
}

.swiper-button-prev {
    left: 10px;
    transform: translateY(-50%) rotate(180deg);
}

.swiper-button-next {
    right: 10px;
}

.comment-swiper {
    position: relative;
    padding: 0 50px;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    opacity: 1;
}

/* 隐藏原生箭头图标 */
.swiper-button-next:after,
.swiper-button-prev:after {
    display: none;
}




</style>