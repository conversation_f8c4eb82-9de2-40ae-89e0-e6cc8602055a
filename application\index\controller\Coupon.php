<?php


namespace app\index\controller;

use app\common\controller\Frontend;
use think\Db;
use think\Cookie;
use think\Hook;

class Coupon extends Frontend
{

    protected $noNeedLogin = '';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
        if(!$this->auth->id){
            $this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
        }
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
    }


    public function index()
    {
        if( $this->request->isPost() ){
            /*0未使用 1已使用 2过期*/
            $use = $this->request->post('use');
            if($use == 2){
                // $rows = Db::name('user_coupon')->where('user_id', $this->auth->id)->where('end_time', "<=", time())->select();
                $rows = $this->query_records('user_coupon', array('user_id'=>$this->auth->id, 'end_time'=>array('<=', time())));
                
            }else{
                $rows = $this->query_records('user_coupon', array('user_id'=>$this->auth->id, 'is_use'=>$use));
            }
            return json_encode(array('code'=>'0', 'data'=>$rows), JSON_UNESCAPED_UNICODE);
        }

        $rows = $this->query_records('user_coupon', array('user_id'=>$this->auth->id));
        $this->view->assign('rows', $rows);
        return $this->view->fetch('coupon_ls');
    }

}






