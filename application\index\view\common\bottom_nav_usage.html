{include file="common/meta" /}
{load href="__CDN__/assets/css/element-ui-index.css,__CDN__/assets/css/login.css" /}
{load
href="__CDN__/assets/js/vue.js,
__CDN__/assets/js/axios.min.js,
__CDN__/assets/js/lodash.min.js,
__CDN__/assets/js/bottom_nav.js,
__CDN__/assets/js/element-ui-index.js"
/}

<div id="content-container" class="container" style="position: relative">
    {/* 引入底部导航组件 */}
    {include file="common/bottom_nav" /}
    
    <div class="usage-guide" style="padding: 40px; max-width: 800px; margin: 0 auto;">
        <h1>底部导航栏组件使用说明</h1>
        
        <h2>1. 引入必要文件</h2>
        <pre>
{load href="__CDN__/assets/css/login.css" /}
{load href="__CDN__/assets/js/vue.js,__CDN__/assets/js/bottom_nav.js" /}
        </pre>
        
        <h2>2. 包含模板文件</h2>
        <pre>
{include file="common/bottom_nav" /}
        </pre>
        
        <h2>3. 在Vue实例中使用mixin</h2>
        <pre>
const app = new Vue({
    el: '#your-container',
    mixins: [bottomNavMixin],  // 引入底部导航栏的功能
    data() {
        return {
            // 你的其他数据
        }
    },
    methods: {
        // 你的其他方法
    }
});
        </pre>
        
        <h2>4. 自定义配置选项</h2>
        <p>如果需要自定义一些配置，可以在data中覆盖mixin中的默认值：</p>
        <pre>
data() {
    return {
        num: 10,  // 覆盖默认通知数量
        // 其他数据
    }
}
        </pre>
        
        <h2>5. 自定义事件处理</h2>
        <p>如果需要自定义事件处理，可以在methods中覆盖mixin中的方法：</p>
        <pre>
methods: {
    noticeMsg() {
        // 自定义通知消息点击事件
        console.log('自定义通知消息处理');
        // 跳转到通知页面
        window.location.href = '/index/notice';
    }
}
        </pre>
    </div>
</div>

<script>
    const app = new Vue({
        el: '#content-container',
        mixins: [bottomNavMixin],  // 引入底部导航栏的功能
        data() {
            return {
                // 自定义数据
            }
        }
    });
</script>

<style>
    .usage-guide {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
    }
    
    .usage-guide h1 {
        color: #333;
        font-size: 28px;
        margin-bottom: 24px;
    }
    
    .usage-guide h2 {
        color: #555;
        font-size: 20px;
        margin: 24px 0 12px 0;
    }
    
    .usage-guide pre {
        background-color: #f5f5f5;
        padding: 12px 16px;
        border-radius: 4px;
        overflow-x: auto;
    }
</style> 