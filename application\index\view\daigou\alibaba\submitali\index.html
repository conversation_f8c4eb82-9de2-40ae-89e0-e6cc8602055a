
{include file="common/resources" /}
{include file="common/daigouresourses" /}
<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box" id="Submit">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <!-- 订单创建成功 -->
    <div id="app" class="max-w-screen-2xl mx-auto p-8">
        <!-- 收货地址 -->
        <div class="bg-white rounded  p-6 mb-4">
          <div class="flex items-center mb-2">
            <span class="font-bold text-lg">收貨地址</span>
          </div>
          <div class="text-gray-700 flex items-center">
            <i class="el-icon-location text-pink-500 mr-2"></i>
            <span v-if="isValidAddress(selectedAddress)">送至：{{formatAddress(selectedAddress)}}</span>
            <span v-else class="text-red-500">请选择收货地址</span>
            <el-button size="mini" class="ml-auto" @click="showAddressModal = true">選擇地址</el-button>
          </div>
        </div>
        <!-- 店铺与商品信息 -->
        <div class="bg-white rounded  p-6 mb-4" v-for="(shop, shopIndex) in settlelist" :key="shopIndex">
          <div class="flex items-center border-b pb-2 mb-4">
            <i class="el-icon-shopping-bag-2 text-red-500 mr-2"></i>
            <span class="font-bold">{{shop.subject}}</span>
          </div>
          <div class="flex items-center mb-2">
            <img :src="getImageProxyUrl(shop.images)" class="w-20 h-20 object-cover rounded border mr-4" />
            <div class="flex-1">
              <div class="font-semibold text-base mb-1">{{shop.subject}}</div>
              <div class="text-gray-500 text-sm" v-for="(spec, specIndex) in shop.spec" :key="specIndex"> {{ spec.cargoNumber || spec.skuAttributes[0].value }} * {{ spec.num }} </div>
            </div>
            <div class="">
              <div class="text-pink-600 font-bold text-lg ml-4">￥{{getShopTotalPrice(shop).toFixed(2)}}</div>
              <!-- <div class="text-gray-500 " style="text-decoration: line-through;">原价37.6%</div> -->
            </div>
           
          </div>
          <!-- 确认库存 -->
        <div class="bg-white rounded mt-4">
            <div class="font-bold ">確認庫存</div>
            <div class="flex items-center mb-2">
              <span class="text-gray-700 flex-1"></span>
              <span class="text-gray-500 text-sm">請複製以下消息詢問賣家庫存是否充足？</span>
            </div>
            <div class="bg-gray-100 rounded p-2 text-sm mb-2 pr">
              <el-button size="mini" class="ml-auto" style="position: relative;left: 95%;top:2px">复制</el-button>
                <div class="text-gray-700 flex-1">我想订购以下商品，麻烦确认下是否有货</div>
                <div class="text-gray-700 flex-1 mt-4" >
                  {{shop.subject}}
                  <div v-for="(spec, specIndex) in shop.spec" :key="specIndex" class="ml-4">
                    {{ spec.cargoNumber || spec.skuAttributes[0].value }} * {{spec.num}}
                  </div>
                </div>
             
            </div>
            <el-checkbox v-model="checked" class="mt-2">賣家已確認庫存充足</el-checkbox>
          </div>
        </div>
        
        <!-- 总计 -->
        <div class="bg-white rounded p-6 mb-4">
          <div class="font-bold mb-2 text-lg flex items-center justify-between">
            <span>總計：</span>
            <span class="text-sm text-gray-500">货品种类：<span class="text-pink-600 font-bold">{{productTypes}}</span> &nbsp; 数量总计：<span class="text-pink-600 font-bold">{{totalQuantity}}</span></span>
          </div>
          <div class="bg-gray-100 rounded p-6 flex flex-row mb-4">
            <div class="flex-1 space-y-3 text-gray-600 text-base">   
              <div>貨品總價：</div>
              <div>運費：</div>
              <div>訂單金額：</div>
              <div>訂單金額(台幣)：1%服務費(台幣)</div>
              <div>1%服务费(台币)：</div>
              <div class="font-bold"> 合計台幣：</div>
            </div>
            <div class="flex-1 space-y-3 text-right text-base">
              <div>￥{{formatPrice(totalProductPrice)}}</div>
              <div>￥{{formatPrice(shippingFee)}}</div>
              <div>
                <span class="text-pink-600 font-bold">￥{{formatPrice(totalOrderAmount)}}</span>
                <span class="text-xs text-gray-400">（计费匯率 {{ehg}}）</span><br>
                <span class="text-xs text-gray-400">*如汇率已改动，需 <a href="#" class="text-pink-500 underline">更新价格</a></span>
                <!-- <span class="ml-2 text-gray-400 line-through">原价37.60</span> -->
              </div>
              <div>{{formatTWDPrice(totalOrderAmountTWD)}}元</div>
              <div>{{formatTWDPrice(serviceFeeTWD)}}元</div>
              <div class="font-bold">{{formatTWDPrice(totalAmountTWD)}}元</div>
            </div>
          </div>
         
        </div>
        <!-- 协议与提交 -->
        <div class="flex items-center justify-between bg-pink-50 border-t border-pink-200 p-4 rounded  mt-8">
          <div class="flex items-center text-sm text-pink-600">
            <el-checkbox v-model="agree" style="--el-checkbox-checked-bg-color: #ef436d; --el-checkbox-checked-border-color: #ef436d;" class="mr-2"></el-checkbox>
            <span>我已詳閱並同意</span>
            <a href="#" class="underline mx-1">《淘寶代購服務（免國際運費包裹）用戶協議》</a>
          </div>
          <div class="flex items-center text-lg font-bold text-pink-600 justify-end">
            應付總額（含運費）：<span class="text-2xl mr-6">￥{{formatPrice(totalOrderAmount)}}</span>
          <el-button type="danger" size="large" :disabled="!canSubmitOrder"  @click="checkout">提交订单</el-button>
  
          </div>
        </div>
         <!-- 弹框放在#app内，避免页面加载即弹框 -->
         <div v-if="showRechargeDialog"
         class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
         <div class="bg-white rounded-lg shadow-lg p-8 w-[600px] max-w-full text-center relative">
             <div class="text-pink-500 text-xl font-bold mb-6 tracking-wider">收银臺6174910963399594</div>
             <div class="text-left mb-6">
                 <div class="text-pink-500 font-bold mb-2 flex items-center">
                     <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>费用结算
                 </div>
                 <div class="bg-gray-50 rounded p-4 mb-4">
                     <div class="flex justify-between text-sm mb-2"><span>货品总价：</span><span>￥{{formatPrice(totalProductPrice)}}</span></div>
                     <div class="flex justify-between text-sm mb-2"><span>运费：</span><span>￥{{formatPrice(shippingFee)}}</span></div>
                     <div class="flex justify-between text-sm mb-2"><span>订单金额：</span><span
                             class="text-pink-500 font-bold">￥{{formatPrice(totalOrderAmount)}}元（计费匯率 {{ehg}}）</span></div>
                     <div class="flex justify-between text-sm mb-2"><span>订单金额(台币)：</span><span>{{formatTWDPrice(totalOrderAmountTWD)}}元</span></div>
                     <div class="flex justify-between text-sm mb-2"><span>1%服务费(台币)：</span><span>{{formatTWDPrice(serviceFeeTWD)}}元</span></div>
                     <div class="flex justify-between text-base font-bold"><span>合计台币：</span><span
                             class="text-pink-500">{{formatTWDPrice(totalAmountTWD)}}元</span></div>
                 </div>
             </div>
             <div class="text-left mb-6">
                 <div class="text-pink-500 font-bold mb-2 flex items-center">
                     <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>您的付款银行账户
                 </div>
                 <div class="flex space-x-4 mb-2">
                     <div class="bg-white border rounded px-6 py-2 flex items-center text-gray-700">摩芮喵 002222</div>
                     <div class="bg-white border rounded px-6 py-2 flex items-center text-gray-700">摩芮喵 002222</div>
                     <div class="bg-white border rounded px-6 py-2 flex items-center text-gray-700">摩芮喵 002222</div>
                 </div>
                 <div class="bg-yellow-50 text-yellow-700 p-2 rounded text-xs flex items-center mb-2">
                     <span class="mr-2">⚠️</span>请使用您已登记的银行卡号转账付款，非上述银行卡号转账入账无法完成交易！
                 </div>
             </div>
             <div class="text-left mb-6">
                 <div class="500 font-bold mb-2 flex items-center">
                     <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>網路ATM/ATM櫃員機
                 </div>
                 <div class="flex items-center mb-2">
                     <div class="rounded-xl px-8 py-6 flex flex-col items-center w-full max-w-md cursor-pointer"
                         @click="showBankModal = true">
                         <!-- <img src="../img/card.jpg" class="" /> -->
  
  
                     </div>
                 </div>
                 <div class="bg-yellow-50 text-yellow-700 p-2 rounded text-xs flex items-center mb-2">
                     1688代采服务商的付款公司全程服务，请务必对订单金额入账转账，如金额有误或误购入其他账户将无法即时入账。
                 </div>
             </div>
             <div class="flex items-center justify-center mt-6">
                 <button class="bg-pink-500 text-white px-12 py-2 rounded text-lg font-bold"
                     @click="showRechargeDialog = false">我已匯款</button>
             </div>
         </div>
     </div>
     
      </div>
    </div>
    
<!-- 地址选择弹框 -->
<div v-if="showAddressModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
    <div class="bg-white rounded-xl shadow-xl p-8 w-full max-w-2xl relative">
        <div class="text-center text-pink-500 text-2xl font-bold mb-6">選擇收貨地址</div>
        
        <!-- 地址列表 -->
        <div class="max-h-96 overflow-y-auto">
            <div v-for="(address, index) in addlist" :key="index" 
                 class="border rounded-lg p-4 mb-3 cursor-pointer hover:bg-gray-50 transition-colors"
                 :class="{'border-pink-500 bg-pink-50': selectedAddress.addressId === address.addressId}"
                 @click="selectAddress(address)">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <span class="font-bold text-gray-800">{{address.addressCodeText}}</span>
                            <span v-if="address.isDefault" class="ml-2 px-2 py-1 bg-pink-500 text-white text-xs rounded">默认</span>
                        </div>
                        <div class="text-gray-600 text-sm mb-1">{{address.address}}</div>
                        <div class="text-gray-500 text-xs">
                            联系人：{{address.fullName}} | 电话：{{address.mobilePhone
}}
                        </div>
                    </div>
                    <div class="ml-4">
                        <el-radio v-model="selectedAddress.addressId" :label="address.addressId" class="text-pink-500"></el-radio>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-4 mt-6 pt-4 border-t">
            <button class="px-6 py-2 rounded border text-gray-500 bg-white hover:bg-gray-100"
                @click="showAddressModal = false">取消</button>
            <button class="px-6 py-2 rounded bg-pink-500 text-white font-bold hover:bg-pink-600"
                @click="confirmAddress">確定</button>
        </div>
    </div>
</div>
</div>


<script>
    
    const app = new Vue({
        el: '#Submit',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            checked: true,
                agree: false,
                showRechargeDialog: false,
                showBankModal: false,
                showTipDialog: false,
                settlelist: <?php echo json_encode($list); ?>, 
                addlist: <?php echo json_encode($addr); ?>, 
                ehg: <?php echo json_encode($ehg); ?>, 
                showAddressModal: false, // 新增地址选择弹框
                selectedAddress: {} // 新增选中的地址
        },
        mounted(){
            
            // 解析数据
            if (typeof this.settlelist === 'string') {
                try {
                    // 解码HTML实体
                    const decodedString = this.decodeHtmlEntities(this.settlelist);
                    // 解析JSON
                    this.settlelist = JSON.parse(decodedString);
                    console.log('解析后的数据:', this.settlelist);
                } catch (error) {
                    console.error('数据解析失败:', error);
                }
            }
            console.log(this.settlelist,123123)
            
            // 如果数据是对象格式，直接使用
            if (this.settlelist && this.settlelist.data) {
                this.settlelist = this.settlelist.data;
            }

            // 默认选中第一个地址
            if (this.addlist && this.addlist.length > 0) {
                this.selectedAddress = this.addlist[0];
            }
        },
        computed: {
            // 计算商品总价
            totalProductPrice() {
                if (!this.settlelist || !this.settlelist.length) return 0;
                let total = 0;
                this.settlelist.forEach(item => {
                    if (item.spec && item.spec.length) {
                        item.spec.forEach(spec => {
                            total += spec.price * spec.num;
                        });
                    }
                });
                return total;
            },
            // 计算运费
            shippingFee() {
                return 0; // 免运费
            },
            // 计算订单总金额（人民币）
            totalOrderAmount() {
                return this.totalProductPrice + this.shippingFee;
            },
            // 计算订单金额（台币）
            totalOrderAmountTWD() {
                return this.totalOrderAmount * this.ehg;
            },
            // 计算服务费（台币）
            serviceFeeTWD() {
                return Math.ceil(this.totalOrderAmountTWD * 0.01);
            },
            // 计算合计台币
            totalAmountTWD() {
                return this.totalOrderAmountTWD + this.serviceFeeTWD;
            },
            // 计算总数量
            totalQuantity() {
                if (!this.settlelist || !this.settlelist.length) return 0;
                let total = 0;
                this.settlelist.forEach(item => {
                    if (item.spec && item.spec.length) {
                        item.spec.forEach(spec => {
                            total += spec.num;
                        });
                    }
                });
                return total;
            },
            // 计算商品种类
            productTypes() {
                if (!this.settlelist || !this.settlelist.length) return 0;
                return this.settlelist.length;
            },
            // 检查是否可以提交订单
            canSubmitOrder() {
                return this.isValidAddress(this.selectedAddress) && this.agree;
            }
        },
        methods: {
            getImageProxyUrl(originalUrl) {
                if (!originalUrl) return '';
                // 使用图片代理
                return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
            },
                decodeHtmlEntities(text) {
                    const textarea = document.createElement('textarea');
                    textarea.innerHTML = text;
                    return textarea.value;
                },
                showRechargeDialogtrue() {
                    this.showTipDialog = false
                    this.showRechargeDialog = true
                },
                startCountdown() {
                    this.countdownTimer = setInterval(() => {
                        if (this.countdown > 0) {
                            this.countdown--;
                        } else {
                            this.cancelDisabled = true;
                            clearInterval(this.countdownTimer);
                        }
                    }, 1000);
                },
                cancelOrder() {
                    this.showCancelDialog = true;
                    setTimeout(() => {
                        this.showCancelDialog = false;
                    }, 1200);
                },
                formatPrice(price) {
                    return parseFloat(price).toFixed(2);
                },
                // 格式化台币价格显示
                formatTWDPrice(price) {
                    return Math.round(price);
                },
                async checkout(){
                    // 验证是否选择了地址
                    if (!this.isValidAddress(this.selectedAddress)) {
                        this.$message.error('请先选择收货地址');
                        this.showAddressModal = true;
                        return;
                    }
                    
                    // 验证是否同意协议
                    if (!this.agree) {
                        this.$message.error('请先同意用户协议');
                        return;
                    }
                    
                    let params = {addr:this.selectedAddress.addressId,data:this.settlelist}
                    console.log(params,123)
                    try {
                        this.$message.warning('订单创建中~~~');
                        let res = await axios.post('aliOrderCreate',params);
                        console.log('订单创建结果:', res.data);
                        
                        if (res.data.code == '0') {
                            this.$message.success('订单创建成功');
                            // 可以跳转到订单详情页面
                            // 创建表单并提交数据
                            window.location.href = `daigouAliOrderConfirm?no=${res.data.id}`;

                        } else {
                            this.$message.error(res.data.msg || '订单创建失败');
                        }
                    } catch (error) {
                        console.error('订单创建错误:', error);
                        this.$message.error('订单创建失败，请重试');
                    }
                },
                // 选择地址
                selectAddress(address) {
                    this.selectedAddress = address;
                },
                // 确认地址选择
                confirmAddress() {
                    this.showAddressModal = false;
                },
                // 格式化地址显示
                formatAddress(address) {
                    if (!address) return '';
                    return `${address.addressCodeText || ''} ${address.address || ''}`.trim();
                },
                // 检查地址是否有效
                isValidAddress(address) {
                    return address && address.addressId && address.address;
                },
                // 计算单个店铺的总价
                getShopTotalPrice(shop) {
                    if (!shop.spec || !shop.spec.length) return 0;
                    let total = 0;
                    shop.spec.forEach(spec => {
                        total += spec.price * spec.num;
                    });
                    return total;
                }
            }
    })
</script>