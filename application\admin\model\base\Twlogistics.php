<?php

namespace app\admin\model\base;

use think\Model;


class Twlogistics extends Model
{

    

    

    // 表名
    protected $name = 'tw_logistics';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function dict()
    {
        return $this->belongsTo('app\admin\model\Dict', 'type', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
