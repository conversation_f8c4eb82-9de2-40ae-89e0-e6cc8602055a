<?php
namespace app\common\taglib;

use think\template\TagLib;

class <PERSON><PERSON> extends TagLib
{
    // 定义标签
    protected $tags = [
        // 标签定义： attr 属性列表 close 是否闭合（0 或者1 默认1） alias 标签别名 level 嵌套层次
        'assets' => ['attr' => 'type,extra', 'close' => 0], // 自定义资源加载标签
    ];

    /**
     * 自定义资源加载标签
     * @param array $tag 标签属性
     * @param string $content 标签内容
     * @return string
     */
    public function tagAssets($tag, $content)
    {
        $type = isset($tag['type']) ? $tag['type'] : 'all';
        $extra = isset($tag['extra']) ? $tag['extra'] : '';
        
        $common_css = [
            '__CDN__/assets/css/element-ui-index.css',
            '__CDN__/assets/css/transportation.css',
            '__CDN__/assets/css/login.css'
        ];
        
        $common_js = [
            '__CDN__/assets/js/vue.js',
            '__CDN__/assets/js/element-ui-index.js',
            '__CDN__/assets/js/axios.min.js',
            '__CDN__/assets/js/lodash.min.js',
            '__CDN__/assets/js/utils.js',
            '__CDN__/assets/js/logout.js',
            '__CDN__/assets/js/bottom_nav.js'
        ];
        
        // 解析额外资源
        $extra_assets = !empty($extra) ? explode(',', $extra) : [];
        
        $parseStr = '';
        
        if ($type == 'all' || $type == 'css') {
            $css_list = $common_css;
            foreach ($extra_assets as $asset) {
                if (strpos($asset, '.css') !== false) {
                    $css_list[] = '__CDN__/assets/' . ltrim($asset, '/');
                }
            }
            $parseStr .= '{load href="' . implode(',', $css_list) . '" /}';
        }
        
        if ($type == 'all' || $type == 'js') {
            $js_list = $common_js;
            foreach ($extra_assets as $asset) {
                if (strpos($asset, '.js') !== false) {
                    $js_list[] = '__CDN__/assets/' . ltrim($asset, '/');
                }
            }
            $parseStr .= '{load href="' . implode(',', $js_list) . '" /}';
        }
        
        return $parseStr;
    }
} 