<?php

use fast\Random;


if (!function_exists('orderNo')) {

    /**
     * 生成下拉列表          P 包裹编号   S额外服务单号   D详情单号    Y 集运订单编号   E 保险编号   C 充值编号      A阿里编号
     *                      G 代购       J订单额外支付
     * @access  public
     * @param string $name
     * @param mixed  $options
     * @param mixed  $selected
     * @param mixed  $attr
     * @return string
     */
    function orderNo($type="")
    {
        $data = array(
			'1'     => 'P',
            '2'     => 'S',
            '3'     => 'D',
            '4'     => 'E',
            '5'     => 'C',
			'10'    => 'Y',
			'100'   => 'A',
            '101'   => 'G',
            '102'   => 'J',
        );
        if(strlen($type)){
            return $data[$type].Random::numeric(16);
        }else{
            return 'D'.Random::numeric(16);
        }
    }
}



if (!function_exists('timeToTimestring')) {

    /**
     * 生成下拉列表          P 包裹编号   S额外服务单号   D详情单号    Y 集运订单编号   E 保险编号   C 充值编号      A阿里编号
     *                      G 代购
     * @access  public
     * @param string $name
     * @param mixed  $options
     * @param mixed  $selected
     * @param mixed  $attr
     * @return string
     */
    function timeToTimestring($dateStr="")
    {
        $dateStr = substr($dateStr, 0, 14);
        $formatted = date('Y-m-d H:i:s', strtotime($dateStr));
        return $formatted;

    }
}


if (!function_exists('array_contain_arrayvalue')) {

    /**
     * 生成下拉列表          P 包裹编号   S额外服务单号   D详情单号    Y 集运订单编号   E 保险编号   C 充值编号      A阿里编号
     *                      G 代购
     * @access  public
     * @param string $name
     * @param mixed  $options
     * @param mixed  $selected
     * @param mixed  $attr
     * @return string
     */
    function array_contain_arrayvalue($array1, $array2)
    {
        for($i=0;$i<count($array1);$i++){
            if( in_array( $array1[$i], $array2) ){
                return true;
            }
        }
        return false;
    }
}
 