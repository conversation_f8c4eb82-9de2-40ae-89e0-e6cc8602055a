{include file="common/meta" /}
{css href="__CDN__/assets/css/transportation.css" /}
<!-- <input type="hidden" id="nouse" value='{:json_encode($user["nouse"])}'> -->
<div class="top">
    <div style="font-size: 18px; font-weight: 500">{:__('My shipping currency')}</div>
    <div style="display: flex; align-items: center; gap: 4px; margin-top: 2px;">
        <a href="{:url('recharge/history')}" class="jump-tags">{:__('Recharge history')}</a>
        <a href="{:url('recharge/details')}" class="jump-tags">{:__('Account details')}</a>
    </div>
</div>
<div class="middle">
    <span><?=floor($user['money']);?></span>
    <a href="{:url('recharge/index')}" class="jump-tags" style="color: #ff9500;">{:__('Recharge')}</a>
</div>
<div class="bottom"  onclick="window.location.href='/index/user/setPass'">
    <span class="pointer" style="color: #3D3D3D; font-size: 13px;">{:__('Set wallet password')}</span>
    <!-- <span style="color: #3D3D3D; font-size: 13px;">{:__('Wallet SMS verification')}</span> -->
    <!-- <el-popconfirm v-if="typeof walletSmsEnabled !== 'undefined'"
        :title="walletSmsEnabled ? '關閉驗證?(24小時內僅修改1次)' : '開啟驗證?(24小時內僅修改1次)'" content="24小時內僅修改1次"
        @confirm="confirmWalletSmsChange" @cancel="cancelWalletSmsChange">
        <el-switch slot="reference" v-model="walletSmsEnabled" @change="handleWalletSmsSwitch" active-color="#ef436d"
            inactive-color="#aaaaaa">
        </el-switch>
    </el-popconfirm> -->
    <div style="display: flex; justify-content: center; align-items: center; width: 20px; height: 20px; cursor: pointer;"><i class="el-icon-arrow-right fs13" ></i></div>
</div>