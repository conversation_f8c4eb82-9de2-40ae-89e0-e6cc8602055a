<?php

namespace app\index\controller;

use app\common\controller\Api;
use app\common\library\Log;
use app\common\controller\Frontend;
use fast\Random;
use GuzzleHttp\Exception\GuzzleException;
use think\Config;
use think\Db;
use app\common\line\Api as LineApi;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\exception\PDOException;
use think\Cookie;
use think\Hook;

class ThirdParty extends Frontend
{
    const REDIRECT_URI = 'https://www.ezfiow.com/index/third_party';
    const LINE_AUTHORIZE_URI = 'https://access.line.me/oauth2/v2.1/authorize';

    protected $noNeedLogin = ['line_callback', 'line_user_login'];

    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }

        Hook::add('user_login_successed', function ($user) use ($auth) {
            $expire = input('post.keeplogin') ? 30 * 86400 : 0;
            Cookie::set('uid', $user->id, $expire);
            Cookie::set('token', $auth->getToken(), $expire);
        });
        Hook::add('user_register_successed', function ($user) use ($auth) {
            Cookie::set('uid', $user->id);
            Cookie::set('token', $auth->getToken());
        });
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });

    }

    /**
     * 用戶登錄注冊
     * @return void
     */
    public function line_user_login()
    {
        $type = 'register';
        $this->getAuthorizeUri($type);
    }

    public function line_user_bind()
    {
        $type = sprintf('userBind_%d', $this->auth->id);
        $this->getAuthorizeUri($type);
    }

    /**
     * @param string $type
     * @return void
     */
    private function getAuthorizeUri(string $type)
    {
        $config = config('site.line_config');
        $redirectUri = $this->getRedirectUrl(sprintf('line_callback?type=%s', $type));
        $state = Random::alnum(10);
        // $url = sprintf('%s?response_type=code&client_id=%s&redirect_uri=%s&state=%s&scope=%s&nonce=09876xyz&bot_prompt=normal&prompt=consent&initial_amr_display=lineqr&switch_amr=false',
        //     self::LINE_AUTHORIZE_URI, $config['appid'], $redirectUri, $state, 'profile%20openid'
        // );

        $url = sprintf('%s?response_type=code&client_id=%s&redirect_uri=%s&state=%s&scope=%s&nonce=09876xyz&bot_prompt=normal&initial_amr_display=lineqr&switch_amr=false&ui_locales=zh-Hant',
            self::LINE_AUTHORIZE_URI, $config['appid'], $redirectUri, $state, 'profile%20openid'
        );

        $this->success('獲取成功', $url);
    }



    private function getRedirectUrl(string $path): string
    {
        return sprintf('%s/%s', self::REDIRECT_URI, $path);
    }

    // public function line_callback()
    // {
    //     $type = $this->request->param('type', 'register');
    //     $type_array = explode('_', $type);
    //     [$method, $userId] = array_pad($type_array, 2, 0);
    //     $code = $this->request->get('code');
    //     $verify_return = [];
    //     try {
    //         $lineApi = new LineApi();
    //         $tokenData = $lineApi->token($code);
    //         $id_token = $tokenData['id_token'];
    //         $verify_return = $lineApi->verifyIdToken($id_token);
    //     } catch (Exception|GuzzleException $exception) {
    //         $this->error('獲取授權失敗');
    //     }
    //     if (empty($verify_return)) {
    //         $this->error('獲取授權失敗');
    //     }
    //     switch ($method) {
    //         case 'register': // 用戶登錄注冊
    //             call_user_func_array([$this, 'userRegister'], ['verify_return' => $verify_return]);
    //             break;
    //         case 'userBind': // 用戶綁定
    //             call_user_func_array([$this, 'userBind'], ['user_id' => $userId, 'verify_return' => $verify_return]);
    //             break;
    //     }
    // }

    // public function line_callback()
    // {

    //     $type = $this->request->param('type', 'register');
    //     $type_array = explode('_', $type);
    //     [$method, $userId] = array_pad($type_array, 2, 0);
    //     $code = $this->request->get('code');
    //     $verify_return = [];
    //     try {
    //         $lineApi = new LineApi();
    //         $tokenData = $lineApi->token($code);
    //         $id_token = $tokenData['id_token'];
    //         $verify_return = $lineApi->verifyIdToken($id_token);
    //     } catch (Exception|GuzzleException $exception) {
    //         $this->error('獲取授權失敗');
    //     }
    //     if (empty($verify_return)) {
    //         $this->error('獲取授權失敗');
    //     }
        
    //     switch ($method) {
    //         case 'register': // 用戶登錄注冊
    //             $avatar = $verify_return['picture'] ?? '';
    //             $user = Db::name('user')->where('lineid', $verify_return['sub'])->find();
                
    //             Db::name('debugging')->insert(['msg'=>json_encode($user, JSON_UNESCAPED_UNICODE),'title'=>'line_callback','createtime'=>time()]);

    //             if ($user) {
    //                 Db::name('user')->where('id', $user['id'])->update([
    //                     'lineid' => $verify_return['sub'],
    //                     'line_username' => $verify_return['name'],
    //                     'avatar' => $avatar,
    //                     'username' => $verify_return['name']
    //                 ]);

    //                 if( $user['mobile'] == '' ){
    //                     return redirect('index/login/register', array('lineid'=>$verify_return['sub'])); 
    //                 }else{
    //                     $res = $this->auth->direct($user['id']);
    //                     if( $res ){
    //                         return $this->success('登录成功',url('index/user/index'), '', 1);
    //                     }
    //                     return redirect('index/login/login'); 
    //                 }
    //             } else {
    //                 $data = [
    //                     'lineid' => $verify_return['sub'],
    //                     'line_username' => $verify_return['name'],
    //                     'avatar' => $avatar,
    //                     'source' => '1',
    //                     'username' => $verify_return['name']
    //                 ];
    //                 // $res = $this->auth->third_register($data);
    //                 $res = Db::name('user')->insert($data);
    //                 if($res)
    //                 {
    //                     return redirect('index/login/register', array('lineid'=>$verify_return['sub'])); 
    //                 }
    //             }
    //             break;
    //         case 'userBind': // 用戶綁定
    //             $user = Db::name('user')->where('lineid', $verify_return['sub'])->find();
    //             if ($user) {
    //                 $this->error('綁定失敗·注意：此LINE賬號已綁定其他賬號');
    //             }
    //             $user_data = [
    //                 'lineid' => $verify_return['sub'],
    //                 'line_username' => $verify_return['name'],
    //                 'avatar' => $verify_return['picture'],
    //                 'username' => $verify_return['name']
    //             ];
    //             $result = Db::name('user')->where('id', $userId)->update($user_data);
    //             // $this->success('ok', $result);

    //             break;
    //     }
    // }


    public function line_callback()
    {
        $type = $this->request->param('type', '');
        $type_array = explode('_', $type);
        [$method, $userId] = array_pad($type_array, 2, 0);
        $code = $this->request->get('code');

        $verify_return = [];
        try {
            $lineApi = new LineApi();
            $tokenData = $lineApi->token($code, $type);
            $id_token = $tokenData['id_token'];
            $verify_return = $lineApi->verifyIdToken($id_token);

        } catch (Exception|GuzzleException $exception) {
            return $this->error('獲取授權失敗');
        }
        if (empty($verify_return)) {
            return $this->error('獲取授權失敗');
        }
        
        switch ($method) {
            case 'register': // 用戶登錄注冊
                $avatar = $verify_return['picture'] ?? '';
                $user = Db::name('user')->where('lineid', $verify_return['sub'])->find();

                if ($user) {
                    Db::name('user')->where('id', $user['id'])->update([
                        'lineid' => $verify_return['sub'],
                        'line_username' => $verify_return['name'],
                        'avatar' => $avatar,
                        'username' => $verify_return['name']
                    ]);

                    if( $user['mobile'] == '' ){
                        return redirect('index/login/register', array('lineid'=>$verify_return['sub'])); 
                    }else{
                        $res = $this->auth->direct($user['id']);
                        if( $res ){
                            return $this->success('登錄成功',url('index/user/index'), '', 1);
                        }
                        return $this->success('登錄失敗',url('index/login/login'), '', 1);
                    }
                } else {
                    $data = [
                        'lineid' => $verify_return['sub'],
                        'line_username' => $verify_return['name'],
                        'avatar' => $avatar,
                        'source' => '1',
                        'username' => $verify_return['name']
                    ];
                    $res = Db::name('user')->insert($data);
                    if($res)
                    {
                        return redirect('index/login/register', array('lineid'=>$verify_return['sub'])); 
                    }
                }
                break;
            case 'userBind': // 用戶綁定
                $user = Db::name('user')->where('lineid', $verify_return['sub'])->find();
                if ($user) {
                    return $this->error('綁定失敗·注意：此LINE賬號已綁定其他賬號');
                }
                $user_data = [
                    'lineid' => $verify_return['sub'],
                    'line_username' => $verify_return['name'],
                    'avatar' => $verify_return['picture'],
                    'username' => $verify_return['name']
                ];
                $result = Db::name('user')->where('id', $userId)->update($user_data);
                $this->success('綁定成功', url('index/user/index'), '', 1);
                break;
        }
    }
    


}