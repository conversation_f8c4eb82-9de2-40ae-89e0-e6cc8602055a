<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="1">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Entrust_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-entrust_no" data-rule="required" class="form-control" name="row[entrust_no]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Waybill')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-waybill" data-rule="required" class="form-control" name="row[waybill]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wbill_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wbill_name" data-rule="required" class="form-control" name="row[wbill_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goods_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-goods_name" data-rule="required" class="form-control" name="row[goods_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goods_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-goods_url" data-rule="required" class="form-control" name="row[goods_url]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-num" data-rule="required" class="form-control" name="row[num]" type="number" value="1">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Scale')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-scale" data-rule="required" class="form-control" step="0.01" name="row[scale]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Volume')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-volume" data-rule="required" class="form-control" step="0.01" name="row[volume]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Unit_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unit_price" data-rule="required" class="form-control" step="0.01" name="row[unit_price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wh_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wh_id" data-rule="required" data-source="warehouse/index" class="form-control selectpage" name="row[wh_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Transport')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transport" data-rule="required" class="form-control" name="row[transport]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Addser_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-addser_id" data-rule="required" data-source="addservice/index" class="form-control selectpage" name="row[addser_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goodstype_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-goodstype_id" data-rule="required" data-source="goodstype/index" class="form-control selectpage" name="row[goodstype_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Uremarks')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-uremarks" data-rule="required" class="form-control" name="row[uremarks]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Oremarks')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-oremarks" data-rule="required" class="form-control" name="row[oremarks]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Arrivetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-arrivetime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[arrivetime]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refuse')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refuse" data-rule="required" class="form-control" name="row[refuse]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Twe_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-twe_id" data-rule="required" data-source="twlogistics/index" class="form-control selectpage" name="row[twe_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bill_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bill_no" data-rule="required" class="form-control" name="row[bill_no]" type="text">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
