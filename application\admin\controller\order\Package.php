<?php

namespace app\admin\controller\order;

use app\common\controller\Backend;
use think\Config;
use think\Db;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Package extends Backend
{

    /**
     * Package模型对象
     * @var \app\admin\model\order\Package
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\order\Package;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user','warehouse','addservice','goodstype','twlogistics'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('user')->visible(['username']);
				$row->getRelation('warehouse')->visible(['title']);
				$row->getRelation('addservice')->visible(['order_no','addser_type_id','tb_money','bal_money','actual_money','order_status']);
				$row->getRelation('goodstype')->visible(['title']);
				$row->getRelation('twlogistics')->visible(['name','type']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
 

    public function get_warehouse(){
        if($this->request->request("keyValue")){
            $rows = Db::name("warehouse")->where('id', $this->request->request("keyValue"))->select();
        }else{
            $rows = Db::name("warehouse")->select();
        }

        $data = [];
		foreach($rows as $ls){
			$data[] = array(
				'id' => $ls['id'],
				'name' =>$ls['title'],
			);
		}
		return json(['list' => $data]);
    }

    public function get_transport(){
        $value = $this->request->request('keyValue');
        if($value){
            $rows = Db::name("dict")->where('id', $value)->select();
        }else{
            $rows = Db::name("dict")->where('type', 'transport')->select();
        }

        $data = [];
		foreach($rows as $ls){
			$data[] = array(
				'id' => $ls['id'],
				'name' =>$ls['name'],
			);
		}
		return json(['list' => $data]);


    }

    public function get_goodstype(){
        $value = $this->request->request('keyValue');
        if($value){
            $rows = Db::name("goodstype")->where('id', $value)->select();
        }else{
            $rows = Db::name("goodstype")->select();
        }

        $data = [];
		foreach($rows as $ls){
			$data[] = array(
				'id' => $ls['id'],
				'name' =>$ls['title'],
			);
		}
		return json(['list' => $data]);
    }

    public function get_twexp(){
        $value = $this->request->request('keyValue');
        if($value){
            $rows = Db::name("tw_logistics")->where('id', $value)->select();
        }else{
            $rows = Db::name("tw_logistics")->select();
        }

        $data = [];
		foreach($rows as $ls){
			$data[] = array(
				'id' => $ls['id'],
                'name' =>$ls['name'],
				// 'name' =>$ls['name'] . '-' . ($ls['type']==20?'宅配':'超商取货'),
			);
		}
		return json(['list' => $data]);
    }



    /*备注*/
    public function remarks($ids){
        if($this->request->isPost()){
            $remarks = $this->request->post('check_text');
            /*获取业务表*/
            if($this->model->where('id',$ids)->update(['oremarks' => $remarks])){
                return $this->success(__('Operation completed'), '', true);
            }else{
                return $this->error(__('Operation failed'), '', true);
            }
        }
        return $this->view->fetch('common/remarks');
    }


    public function details($ids){
        $row = Db::name('package')->alias('p')
        ->join('user u','p.user_id=u.id', 'left')
        ->join('warehouse w','p.wh_id=w.id')
        ->join('addservice a','p.addser_id=a.id')
        ->join('goodstype g','p.goodstype_id=g.id')
        ->where('p.id', $ids)
        ->field(['p.*', 'u.username', 'u.mobile', 'w.title as wtitle', 'w.addr', 'a.order_no', 'a.addser_type_id'
        , 'a.tb_money', 'a.bal_money', 'a.actual_money', 'a.pay_type', 'a.order_status', 'a.remarks'
        , 'g.title as gtitle'])->find();

        if(!$row){
            return $this->error(__('详情记录获取失败'), '', true);
        }
        $ser_arr = Db::name('addservice_type')->where('id', "in", explode(",", $row['addser_type_id']))->select();
        $this->view->assign('serarr', $ser_arr);
        $this->view->assign('row', $row);

        return $this->view->fetch('order/package/detail');
    }

}
