<?php

namespace app\admin\model\order;

use think\Model;


class Orderinsure extends Model
{

    

    

    // 表名
    protected $name = 'order_insure';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function jyorder()
    {
        return $this->belongsTo('app\admin\model\Jyorder', 'jy_order', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function insure()
    {
        return $this->belongsTo('app\admin\model\Insure', 'insure_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
