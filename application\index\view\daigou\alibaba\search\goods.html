


{include file="common/resources" /}
{include file="common/daigouresourses" /}
<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box" id="Goods">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
         <!-- 流程步骤条 -->
    <div class="max-w-screen-2xl mx-auto mt-6 flex items-center px-2">

        <div class="text-xl font-bold mr-4">1688代採購</div>
    </div>

    <div class="max-w-screen-2xl mx-auto mt-6 flex items-center px-2">

        <div class="flex items-center flex-1">
            <span class="text-pink-500 font-bold flex items-center"><svg class="w-5 h-5 mr-1" fill="none"
                    stroke="#ec4899" stroke-width="2" viewBox="0 0 24 24">
                    <path d="M9 12h6M12 9v6" stroke-linecap="round" stroke-linejoin="round" />
                    <rect x="3" y="3" width="18" height="18" rx="2" stroke="#ec4899" />
                </svg>创建代购订单</span>
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
        </div>
        <div class="flex items-center flex-1 justify-center">
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
            <span class="text-gray-400 flex items-center"><svg class="w-5 h-5 mr-1" fill="none" stroke="#aaa"
                    stroke-width="2" viewBox="0 0 24 24">
                    <path d="M6 6h15M6 12h15M6 18h15M3 6h.01M3 12h.01M3 18h.01" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>付款到EZ集运通</span>
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
        </div>
        <div class="flex items-center flex-1 justify-end">
            <span class="flex-1 border-t border-gray-200 mx-2"></span>
            <span class="text-gray-400 flex items-center"><svg class="w-5 h-5 mr-1" fill="none" stroke="#aaa"
                    stroke-width="2" viewBox="0 0 24 24">
                    <rect x="3" y="7" width="18" height="13" rx="2" stroke="#aaa" />
                    <path d="M16 3v4M8 3v4" stroke-linecap="round" />
                </svg>采购成功</span>
        </div>
    </div>
    <!-- 商品详情页 -->
    <div id="app" class="flex max-w-screen-2xl mx-auto mt-8 bg-white rounded-lg  p-8">
        <div class="w-full">
            <!-- 顶部商品主图和信息 -->
            <div class="flex">
                <!-- 左侧图片区 -->
                <div class="w-1/4 flex flex-col items-center">
                    <img :src="getImageProxyUrl(productInfo.productImage.images[0])" alt="商品主图"
                        class="w-56 h-56 rounded-lg shadow mb-4 object-cover" />
                    <!-- <div class="bg-white px-3 py-1 rounded shadow text-gray-700 text-sm">夜起亮灯 不影响室友</div> -->
                </div>
                <!-- 右侧信息区 -->
                <div class="w-3/4 pl-10">
                    <!-- 标题 -->
                    <div class="text-xl font-bold mb-2 mt-2">{{productInfo.subject}}</div>
                    <!-- 选项卡 -->
                    <div class="flex items-center border-b mb-2">
                        <div class="px-6 py-2 text-pink-600 font-bold border-b-2 border-pink-500 cursor-pointer">批发
                        </div>
                        <div class="px-6 py-2 text-gray-400 cursor-pointer">代发</div>
                    </div>
                    <!-- 会员价提示 -->
                    <div class="text-xs text-pink-500 mb-2">已享受PLUS会员价</div>
                    <!-- 价格阶梯 -->
                    <div class="flex items-end space-x-8 mb-2">
                        <div v-for="(item, idx) in productInfo.productSaleInfo.priceRangeList" :key="idx">
                            <div :class="['text-pink-600', idx === 0 ? 'text-2xl' : idx === 1 ? 'text-xl' : 'text-lg', 'font-bold']">
                                ￥{{ item.price }} <span class="text-base font-normal text-gray-500">≥{{ item.startQuantity }}件</span>
                            </div>
                        </div>
                    </div>
                    <!-- 参数 -->
                    <div class="mb-2 text-gray-700 text-sm"></div>
                    <!-- 规格表格 -->
                    <div class="mt-4">
                        <div class="font-semibold mb-2">增选规格：</div>
                        <table class="w-full text-sm border rounded overflow-hidden">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="py-2 px-2 text-left font-normal text-gray-500">图片</th>
                                    <th class="py-2 px-2 text-left font-normal text-gray-500">名称</th>
                                    <th class="py-2 px-2 text-center font-normal text-gray-500">价格</th>
                                    <th class="py-2 px-2 text-center font-normal text-gray-500">库存</th>
                                    <th class="py-2 px-2 text-center font-normal text-gray-500">数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(sku, idx) in productInfo.productSkuInfos" :key="sku.skuId" class="border-b">
                                    <td class="py-2 px-2">
                                        <img :src="getImageProxyUrl(sku.skuAttributes && sku.skuAttributes[0] && sku.skuAttributes[0].skuImageUrl ? sku.skuAttributes[0].skuImageUrl : '')" class="w-10 h-10 object-cover rounded" />
                                    </td>
                                    <td class="py-2 px-2">
                                        <span >
                                            {{ sku.cargoNumber }} {{ sku.skuAttributes[0].value }}
                                        </span>
                                    </td>
                                    <td class="py-2 px-2 text-center text-pink-600 font-bold">
                                        {{ sku.fenxiaoPriceInfo && sku.fenxiaoPriceInfo.offerPrice ? sku.fenxiaoPriceInfo.offerPrice : sku.consignPrice }}
                                    </td>
                                    <td class="py-2 px-2 text-center text-gray-600">{{ sku.amountOnSale }}</td>
                                    <td class="py-2 px-2 text-center">
                                        <button class="w-7 h-7 border rounded text-lg text-gray-500" @click="changeSkuCount(idx, -1)" :disabled="sku.count <= 0">-</button>
                                        <input type="text" v-model.number="sku.count" class="w-10 h-7 border rounded text-center mx-1" @change="onSkuInputChange(sku)" />
                                        <button class="w-7 h-7 border rounded text-lg text-pink-500" @click="changeSkuCount(idx, 1)" :disabled="sku.count >= sku.amountOnSale">+</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- 总价展示区域 -->
            <div class="flex justify-end items-center mt-4 mb-2 pr-8">
                <span class="text-gray-700 mr-8">已选数量：<span class="text-pink-600 font-bold">{{ totalNum }}</span> 件</span>
                <span class="text-gray-700">总价：<span class="text-pink-600 text-2xl font-bold">￥{{ totalPrice.toFixed(2) }}</span></span>
            </div>
            <!-- 底部操作栏 -->
            <div class="flex justify-center mt-8">
                <button class="bg-pink-500 hover:bg-pink-600 text-white px-10 py-2 rounded font-bold mx-2"
                    @click="handleAddToCart">加采购单</button>
                <button class="bg-pink-500 hover:bg-pink-600 text-white px-10 py-2 rounded font-bold mx-2"
                    @click="handleAddOrder">立即订购</button>
                <button
                    class="bg-white border border-pink-500 text-pink-500 px-10 py-2 rounded font-bold mx-2" @click="handleCollect">收藏</button>
            </div>
        </div>
        <!-- 订购弹框 -->
        <div v-if="showOrderDialog" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
            <div class="bg-white rounded-lg shadow-xl w-[700px] max-w-full p-8 relative">
                <button class="absolute right-4 top-4 text-2xl text-gray-400 hover:text-gray-600"
                    @click="showOrderDialog = false">×</button>
                <div class="text-center text-pink-500 text-2xl font-bold mb-8">確認訂購</div>
                <!-- 收货地址 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">收货地址</div>
                    <div class="flex-1 text-gray-700 flex items-center">
                        <i class="el-icon-location text-pink-500 mr-2"></i>仓库：深圳仓
                        <a href="#" class="ml-4 text-pink-400 text-sm hover:underline">更改收货地址</a>
                    </div>
                </div>
                <!-- 确认库存 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">确认库存</div>
                    <div class="flex-1">
                        <div class="text-gray-400 text-sm mb-1">请複製以下消息询问卖家库存是否充足？</div>
                        <div class="bg-gray-50 rounded p-3 text-gray-700 text-sm flex items-start relative">
                            <div class="flex-1">
                                我要订购以下商品，麻烦确认下是否都有货<br>
                                {{productInfo.subject}}
                                <div v-for="(item, idx) in showproduct" :key="item.sellerId" class="border-b">
                                    <span v-for="(i, idx) in item.spec" :key="item.specId">
                                        {{ i.cargoNumber }} * {{ i.num }} ---
                                    </span>
                                </div>
                            </div>
                            <a href="javascript:;" class="text-pink-400 text-sm ml-4 absolute right-4 top-3 hover:underline" @click="copyOrderInfo">複製</a>
                        </div>
                        <div class="mt-2 flex items-center">
                            <input type="checkbox" id="stockChecked" v-model="stockChecked"
                                class="mr-2 w-4 h-4 accent-pink-500">
                            <label for="stockChecked" class="text-blue-600 text-base select-none">賣家已確認庫存充足</label>
                        </div>
                    </div>
                </div>
                <!-- 卖家留言 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24 text-right pr-2 text-pink-500 font-bold">卖家留言</div>
                    <div class="flex-1">
                        <input type="text" class="w-full border rounded px-3 py-2 text-sm"
                            placeholder="如果需要，您可以填写给卖家的留言" v-model="buyerMsg">
                        <div class="flex items-center mt-2 space-x-6">
                            <label class="flex items-center text-sm"><input type="radio" name="msgType" checked
                                    class="mr-1">以简體字发送</label>
                            <label class="flex items-center text-sm"><input type="radio" name="msgType"
                                    class="mr-1">以簡體字发送</label>
                        </div>
                    </div>
                </div>
                <!-- 协议勾选 -->
                <div class="mb-6 flex items-start">
                    <div class="w-24"></div>
                    <div class="flex-1 flex items-center">
                        <input type="checkbox" id="agree" v-model="agree" class="mr-2 w-4 h-4 accent-pink-500">
                        <label for="agree" class="text-pink-500 text-sm select-none">我已阅读并同意 <a href="#"
                                class="underline">《淘宝代购服务（免国际运费包裹）用户协议》</a></label>
                    </div>
                </div>
                <!-- 按钮 -->
                <div class="flex justify-end space-x-4 mt-6">
                    <button class="px-8 py-2 rounded bg-gray-100 text-gray-600 font-bold"
                        @click="showOrderDialog = false">取消</button>
                    <button class="px-8 py-2 rounded bg-pink-500 text-white font-bold" @click="checkout">订購</button>
                </div>
            </div>
        </div>

        <!-- 已添加至采购车弹框 -->
        <div v-if="showCartDialog" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
            <div class="bg-white rounded-lg shadow-xl w-[340px] max-w-full p-8 relative text-center">
                <svg class="mx-auto mb-2" width="32" height="32" fill="none" viewBox="0 0 32 32">
                    <circle cx="16" cy="16" r="16" fill="#F9A8D4" />
                    <path d="M10 17l4 4 8-8" stroke="#fff" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
                <div class="text-pink-500 text-lg font-bold mb-2 flex items-center justify-center"><span
                        class="ml-2">已添加至采购車</span></div>
                <div class="text-gray-700 mb-6">当前采购车共{{ totalNum }}种货品</div>
                <div class="flex justify-center space-x-4">
                    <button class="px-6 py-2 rounded bg-gray-100 text-gray-600 font-bold"
                        @click="showCartDialog = false">继续购物</button>
                    <button class="px-6 py-2 rounded bg-pink-500 text-white font-bold" @click="goshopcart">去结算</button>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>

<script>
    var data = <?php echo json_encode($data); ?>;
    new Vue({
        el: '#Goods',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data() {
            return {
                showCartDialog: false,
                showOrderDialog: false,
                orderData: '',
                productInfo: data.result.result,
                selectedSpecIdx: 0,
                count: 1,
                stockChecked: true,
                buyerMsg: '',
                invoiceType: '普通发票',
                agree: false,
                productImages: [
                  '../img/product.png',
                  '../img/product1.png',
                  '../img/product2.png',
                  '../img/product1.png',
                  '../img/product2.png',
                  '../img/product.png',
                  '../img/product1.png',
                ],
                activeImage: 0,
                thumbStart: 0,
                thumbShow: 4,
                showproduct:[]
            }
        },
        mounted() {
            // 初始化每个SKU的count为0，确保响应式
            if (this.productInfo && this.productInfo.productSkuInfos) {
                this.productInfo.productSkuInfos.forEach((sku, idx) => {
                    if (typeof sku.count !== 'number') this.$set(this.productInfo.productSkuInfos[idx], 'count', 0);
                });
            }
            console.log(this.productInfo);
        },
        computed: {
            // 计算已选总数量
            totalNum() {
                if (!this.productInfo || !this.productInfo.productSkuInfos) return 0;
                // 确保count为数字
                return this.productInfo.productSkuInfos.reduce((sum, sku) => sum + (Number(sku.count) > 0 ? Number(sku.count) : 0), 0);
            },
            // 计算阶梯单价
            ladderUnitPrice() {
                if (!this.productInfo || !this.productInfo.productSaleInfo || !this.productInfo.productSaleInfo.priceRangeList) return 0;
                let price = this.productInfo.productSaleInfo.priceRangeList[0].price;
                for (let i = 0; i < this.productInfo.productSaleInfo.priceRangeList.length; i++) {
                    if (this.totalNum >= this.productInfo.productSaleInfo.priceRangeList[i].startQuantity) {
                        price = this.productInfo.productSaleInfo.priceRangeList[i].price;
                    } else {
                        break;
                    }
                }
                return parseFloat(price);
            },
            // 计算总价
            totalPrice() {
                console.log(this.totalNum, this.ladderUnitPrice, 9999)
                return this.totalNum * this.ladderUnitPrice;
            }
        },
        methods: {
            // 图片代理方法，解决跨域问题
            getImageProxyUrl(originalUrl) {
                if (!originalUrl) return '';
                // 使用图片代理
                return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
            },
            // 收藏功能
            async handleCollect() {
                try {
                    const collectData = {
                        price: this.ladderUnitPrice,
                        images: this.productInfo.productImage && this.productInfo.productImage.images ? this.productInfo.productImage.images[0] : '',
                        offer: this.productInfo.offerId,
                        subject: this.productInfo.subject
                    };
                    
                    console.log('收藏参数:', collectData);
                    // 发送收藏请求到后端
                    const response = await axios.post('aliAddcollect', collectData);
                    
                    if (response.data && response.data.code === 1) {
                        this.$message && this.$message.success('收藏成功');
                    } else {
                        this.$message && this.$message.error(response.data.msg || '收藏失败');
                    }
                } catch (error) {
                    console.error('收藏失败:', error);
                    this.$message && this.$message.error('收藏失败，请稍后重试');
                }
            },
            // 组装选中数据并提交到后端
            async handleAddToCart() {
                const selectedSkus = this.productInfo.productSkuInfos.filter(sku => sku.count > 0);
                if (selectedSkus.length === 0) {
                    this.$message && this.$message.warning('请先选择商品数量');
                    return;
                }
                const spec = selectedSkus.map(sku => ({
                    specId: sku.specId,
                    cargoNumber: sku.cargoNumber,
                    skuAttributes: sku.skuAttributes,
                    price: this.ladderUnitPrice,
                    num: sku.count,
                    skuImageUrl: sku.skuAttributes && sku.skuAttributes[0] ? sku.skuAttributes[0].skuImageUrl : ''
                }));
                const payload = {
                    sellerOpenId: this.productInfo.sellerOpenId,
                    offer: this.productInfo.offerId,
                    img: this.productInfo.productImage.images[0],
                    subject: this.productInfo.subject,
                    spec,
                    totalNum: this.totalNum,
                    totalPrice: this.totalPrice
                };
                console.log(payload,789)
                let res = await axios.post(`aliAddCart`, payload);
                this.showCartDialog = true
                // 发送到后端
               
            },
            changeSkuCount(index, delta) {
                const sku = this.productInfo.productSkuInfos[index];
                let newCount = sku.count + delta;
                if (newCount < 0) newCount = 0;
                if (newCount > sku.amountOnSale) newCount = sku.amountOnSale;
                sku.count = newCount;
            },
            // 输入框手动输入数量时校验
            onSkuInputChange(sku) {
                if (!sku.count || sku.count < 0) sku.count = 0;
                if (sku.count > sku.amountOnSale) sku.count = sku.amountOnSale;
            },
            thumbScrollLeft() {
                if (this.thumbStart > 0) this.thumbStart--;
            },
            thumbScrollRight() {
                if (this.thumbStart + this.thumbShow < this.productImages.length) this.thumbStart++;
            },
            async checkout() {
                if(!this.agree){
                    this.$message.error('请勾选并阅读《淘宝代购服务（免国际运费包裹）用户协议》');
                    return
                }
                // 收集所有数量大于0的sku
                let selected = [];
                const selectedSkus = this.productInfo.productSkuInfos.filter(sku => sku.count > 0);
                if (selectedSkus.length === 0) {
                    this.$message && this.$message.warning('请先选择商品数量');
                    return;
                }
                selected.push({
                    shop: this.productInfo.sellerNick || (this.productInfo.seller && this.productInfo.seller.wangwangNick) || '',
                    subject: this.productInfo.subject,
                    id:0,
                    sellerId: this.productInfo.sellerOpenId,
                    offer: this.productInfo.offerId,
                    images: this.productInfo.productImage && this.productInfo.productImage.images ? this.productInfo.productImage.images[0] : '',
                    spec: selectedSkus.map(sku => ({
                        specId: sku.specId,
                        cargoNumber: sku.cargoNumber,
                        skuAttributes: sku.skuAttributes,
                        price: this.ladderUnitPrice,
                        num: sku.count,
                        skuImageUrl: sku.skuAttributes && sku.skuAttributes[0] ? sku.skuAttributes[0].skuImageUrl : ''
                    }))
                });
                // 提交到后端
                console.log('结算参数', selected);
                let res = await axios.post('aliSubmitOrder', {data:selected});


               // 创建表单并提交数据
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'aliSubmitOrder';

                // 创建隐藏的input字段来传递数据
                const dataInput = document.createElement('input');
                dataInput.type = 'hidden';
                dataInput.name = 'data';
                dataInput.value = JSON.stringify(selected);
                form.appendChild(dataInput);

                // 将表单添加到页面并提交
                document.body.appendChild(form);
                form.submit();



            },
            async goshopcart(){
                let res = await axios.get(`aliShoppCart`);
                window.location.href = `alishoppcart`;
            },
            handleAddOrder(){
                const selectedSkus = this.productInfo.productSkuInfos.filter(sku => sku.count > 0);
                if (selectedSkus.length === 0) {
                    this.$message && this.$message.warning('请先选择商品数量');
                    return;
                }
                this.showOrderDialog = true
                this.showproduct.push({
                    shop: this.productInfo.sellerNick || (this.productInfo.seller && this.productInfo.seller.wangwangNick) || '',
                    subject: this.productInfo.subject,
                    id:0,
                    sellerId: this.productInfo.sellerOpenId,
                    offer: this.productInfo.offerId,
                    images: this.productInfo.productImage && this.productInfo.productImage.images ? this.productInfo.productImage.images[0] : '',
                    spec: selectedSkus.map(sku => ({
                        specId: sku.specId,
                        cargoNumber: sku.cargoNumber,
                        skuAttributes: sku.skuAttributes,
                        price: this.ladderUnitPrice,
                        num: sku.count,
                        skuImageUrl: sku.skuAttributes && sku.skuAttributes[0] ? sku.skuAttributes[0].skuImageUrl : ''
                    }))
                });
                // 提交到后端
                console.log('结算参数', this.showproduct);
            },
            copyOrderInfo() {
                let text = '';
                // 商品标题
                text += this.productInfo.subject + '\\n';
                // 商品明细
                this.showproduct.forEach(item => {
                    item.spec.forEach(i => {
                        text += `${i.cargoNumber} * ${i.num} ---\\n`;
                    });
                });
                // 复制到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.$message && this.$message.success('复制成功');
                    }, () => {
                        this.$message && this.$message.error('复制失败，请手动复制');
                    });
                } else {
                    // 兼容老浏览器
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    document.body.appendChild(textarea);
                    textarea.select();
                    try {
                        document.execCommand('copy');
                        this.$message && this.$message.success('复制成功');
                    } catch (err) {
                        this.$message && this.$message.error('复制失败，请手动复制');
                    }
                    document.body.removeChild(textarea);
                }
            }
        }
    })
</script>

