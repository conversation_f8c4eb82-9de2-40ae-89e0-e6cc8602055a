{include file="common/resources" /}

<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="app">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container" style="width: 100%;">
        <div v-if="!payWd" class="set-pass-container">
            <!-- 头部标题 -->
            <div class="header-section">
                <div class="back-button" >
                    <span @click="goBack">
                        <i class="el-icon-arrow-left fs16"></i>
                        <span class="c666 fs16">返回</span>
                    </span>
                    <span class="c3d3 fs16 fw">修改錢包密碼</span>
                </div>
            </div>

            <!-- 验证手机号部分 -->
            <div class="form-section">
                <div class="form-item" style="margin-bottom: 24px;">
                    <label class="form-label">
                        <span class="required">*</span>
                        <span fs14 c333>驗證手機號碼</span>
                    </label>
                    <div class="phone-section">
                        <div class="phone-display">{{maskMobile(mobile)}}</div>
                        <div class="verification-row">
                            <el-input
                                v-model="verificationCode"
                                placeholder="請輸入4位數驗證碼"
                                class="verification-input"
                                maxlength="4"
                                prefix-icon="el-icon-mobile-phone"
                                @input="onVerificationCodeInput">
                            </el-input>
                            <el-button
                                type="danger"
                                class="send-code-btn"
                                :disabled="isCountingDown"
                                @click="sendVerificationCode">
                                {{isCountingDown ? `等待${countdown}s...` : '發送驗證碼'}}
                            </el-button>
                        </div>
                    </div>
                </div>

                <div class="line"></div>

                <!-- 输入6位钱包密码 -->
                <div class="form-item mt24">
                    <label class="form-label">
                        <span class="required">*</span>
                        <span class="c333 fs14">輸入6位數錢包密碼</span>
                        <span class="c666 fs14">（密码为6位数字组合）</span>   
                    </label>
                    <div class="password-inputs">
                        <input
                            v-for="(digit, index) in 6"
                            :key="index"
                            v-model="password[index]"
                            type="text"
                            maxlength="1"
                            class="password-input"
                            @input="onPasswordInput(index, $event)"
                            @keydown.backspace="onPasswordBackspace(index, $event)"
                            :ref="`passwordInput${index}`">
                    </div>
                </div>

                <!-- 再次确认密码 -->
                <div class="form-item">
                    <label class="form-label">
                        <span class="required">*</span>
                        <span class="c333 fs14">再次確認密碼</span>
                    </label>
                    <div class="password-inputs">
                        <input
                            v-for="(digit, index) in 6"
                            :key="index"
                            v-model="confirmPassword[index]"
                            type="text"
                            maxlength="1"
                            class="password-input"
                            @input="onConfirmPasswordInput(index, $event)"
                            @keydown.backspace="onConfirmPasswordBackspace(index, $event)"
                            :ref="`confirmPasswordInput${index}`">
                    </div>
                </div>
            </div> 

            <!-- 底部按钮 -->
            <div class="button-section">
                <el-button class="cancel-btn" @click="cancel">取 消</el-button>
                <el-button type="danger" class="confirm-btn" @click="confirm">確 定</el-button>
            </div>
        </div>
        <div v-else class="set-pass-container">
            <!-- 钱包密码设置页面 -->
            <div class="wallet-settings">
                <div class="mb12 back-button">
                    <span class="" @click="goBack">
                        <i class="el-icon-arrow-left fs16"></i>
                        <span class="c666 fs16 pointer">返回</span>
                    </span>
                    <span class="c3d3 fs16 fw">設置錢包密碼</span>
                </div>

                <!-- 修改钱包密码选项 -->
                <div class="setting-item" @click="goToModifyPassword">
                    <div class="setting-content">
                        <span class="setting-label">修改錢包密碼</span>
                    </div>
                    <div class="setting-action">
                        <i class="el-icon-arrow-right"></i>
                    </div>
                </div>

                <!-- 开启钱包密码选项 -->
                <div class="setting-item">
                    <div class="setting-content">
                        <span class="setting-label">開啟錢包密碼</span>
                    </div>
                    <div class="setting-action">
                        <el-popconfirm
                            :title="walletPasswordEnabled ? '關閉支付密碼？(一天僅修改一次)' : '開啟錢包密碼？(一天僅修改一次)'"
                            confirm-button-text="確定"
                            cancel-button-text="取消"
                            @confirm="confirmWalletPasswordChange"
                            @cancel="cancelWalletPasswordChange">
                            <el-switch
                                slot="reference"
                                v-model="walletPasswordEnabled"
                                active-color="#22b573"
                                inactive-color="#eaecf0"
                                @change="handleWalletPasswordSwitch">
                            </el-switch>
                        </el-popconfirm>
                    </div>
                </div>

                <!-- 提示文字 -->
                <div class="setting-tip mt12">* 開啟後，付款將驗證錢包密碼</div>
            </div>
        </div>
    </div>
</div>
<script>
    let user = {:json_encode($user)};
    console.log(user,'user');

    const app = new Vue({
        el: '#app',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            mobile: user.mobile, // 脱敏手机号
            errCode:'', // 验证码错误信息
            verificationCode: '', // 验证码
            password: ['', '', '', '', '', ''], // 6位密码
            confirmPassword: ['', '', '', '', '', ''], // 确认密码
            isCountingDown: false, // 是否正在倒计时
            countdown: 60, // 倒计时秒数
            showPhoneError: false, // 是否显示手机验证错误
            walletPasswordEnabled: user.nouse === 0, // 钱包密码开关状态，0表示需要密码(开启)，1表示不需要密码(关闭)
            pendingWalletPasswordState: null, // 待确认的开关状态
            payWd: user.paypwd,
            nouse:user.nouse,  // 是否需要密码支付
        },
        watch: {
            password:{
                handler(val){
                    console.log('輸入密碼',val);
                }
            },
            confirmPassword:{
                handler(val){
                    console.log('確認密碼',val);
                }
            }
        },
        computed:{
        },
        methods:{
            // 返回上一页
            goBack() {
                window.history.back();
            },

            maskMobile(mobile) {
                return utils.maskMobile(mobile)
            },

            // 发送验证码
            async sendVerificationCode() {
                if (this.isCountingDown) return;

                try {
                    // 这里调用发送验证码的接口
                    const response = await axios.get('/index/user/getCode?type=' + 2,{
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    console.log(response,'response');
                    if(response.data.code == 1){
                        this.$message.success('验证码已发送');
                        this.startCountdown();
                    }
                } catch (error) {
                    this.$message.error('发送验证码失败，请稍后重试');
                }
            },

            // 开始倒计时
            startCountdown() {
                this.isCountingDown = true;
                this.countdown = 115;

                const timer = setInterval(() => {
                    this.countdown--;
                    if (this.countdown <= 0) {
                        this.isCountingDown = false;
                        this.countdown = 115;
                        clearInterval(timer);
                    }
                }, 1000);
            },

            // 验证码输入处理
            onVerificationCodeInput(value) {
                // 只允许输入数字，最多4位
                const numericValue = value.replace(/\D/g, '').slice(0, 4);
                this.verificationCode = numericValue;
            },

            // 密码输入处理
            onPasswordInput(index, event) {
                const value = event.target.value.replace(/\D/g, ''); // 只允许数字
                this.$set(this.password, index, value);

                if (value && index < 5) {
                    // 自动跳到下一个输入框
                    this.$nextTick(() => {
                        this.$refs[`passwordInput${index + 1}`][0].focus();
                    });
                }
            },

            // 密码退格处理
            onPasswordBackspace(index, event) {
                if (!this.password[index] && index > 0) {
                    this.$nextTick(() => {
                        this.$refs[`passwordInput${index - 1}`][0].focus();
                    });
                }
            },

            // 确认密码输入处理
            onConfirmPasswordInput(index, event) {
                const value = event.target.value.replace(/\D/g, ''); // 只允许数字
                this.$set(this.confirmPassword, index, value);

                if (value && index < 5) {
                    // 自动跳到下一个输入框
                    this.$nextTick(() => {
                        this.$refs[`confirmPasswordInput${index + 1}`][0].focus();
                    });
                }
            },

            // 确认密码退格处理
            onConfirmPasswordBackspace(index, event) {
                if (!this.confirmPassword[index] && index > 0) {
                    this.$nextTick(() => {
                        this.$refs[`confirmPasswordInput${index - 1}`][0].focus();
                    });
                }
            },

            // 取消
            cancel() {
                this.goBack();
            },

            // 确定
            async confirm() {
                if (!/^\d{4}$/.test(this.verificationCode)) {
                    this.$message.error('請輸入4位數的驗證碼');
                    return;
                }

                // 验证密码
                const passwordStr = this.password.join('');
                const confirmPasswordStr = this.confirmPassword.join('');

                if (passwordStr.length !== 6) {
                    this.$message.error('请输入6位数字密码');
                    return;
                }

                if (confirmPasswordStr.length !== 6) {
                    this.$message.error('请确认6位数字密码');
                    return;
                }

                if (passwordStr !== confirmPasswordStr) {
                    this.$message.error('两次输入的密码不一致');
                    return;
                }

                try {
                    const params = {
                        code: this.verificationCode,
                        mobile: this.mobile,
                        pwd: passwordStr,
                        pwdt: confirmPasswordStr,
                    }
                    const res = await axios.post('/index/user/payModify',params);
                    console.log(res,'res');

                    if(res.data.code == 0) {
                        this.$message.success(res.data.msg);
                        this.goBack();
                    }
                } catch (error) {
                    this.$message.error('设置失败，请稍后重试');
                }
            },

            // 跳转到修改密码页面
            goToModifyPassword() {
                // 这里可以跳转到修改密码页面或者切换到修改密码模式
                this.payWd = ''; // 切换到修改密码界面
            },

            // 处理钱包密码开关点击（阻止直接改变状态，触发确认弹窗）
            handleWalletPasswordSwitch(value) {
                console.log('钱包密码开关被点击，新状态:', value);

                // 保存待确认的状态
                this.pendingWalletPasswordState = value;

                // 先回滚到原状态，等用户确认后再改变
                this.$nextTick(() => {
                    this.walletPasswordEnabled = !value;
                });
            },

            // 确认钱包密码开关变更
            async confirmWalletPasswordChange() {
                console.log('用户确认钱包密码开关变更');

                if (this.pendingWalletPasswordState === null) return;

                try {
                    // 根据开关状态设置 type 值
                    // true(开启) -> type: 0 (需要支付密码)
                    // false(关闭) -> type: 1 (不需要支付密码)
                    const type = this.pendingWalletPasswordState ? 0 : 1;

                    console.log('发送请求，type:', type);

                    const response = await axios.post('/index/user/noused', {
                        type: type
                    });

                    console.log('接口响应:', response);

                    if (response.data.code == 0) {
                        // 成功后更新状态
                        this.walletPasswordEnabled = this.pendingWalletPasswordState;
                        this.nouse = type;
                        this.$message.success(response.data.msg)
                    } else {
                        this.$message.error(response.data.msg || '操作失败');
                    }
                } catch (error) {
                    console.error('钱包密码开关操作失败:', error);
                    this.$message.error('操作失败，请稍后重试');
                } finally {
                    this.pendingWalletPasswordState = null;
                }
            },

            // 取消钱包密码开关变更
            cancelWalletPasswordChange() {
                console.log('用户取消钱包密码开关变更');
                this.pendingWalletPasswordState = null;
                // 状态已经在 handleWalletPasswordSwitch 中回滚了，这里不需要额外操作
            }
        },
    })
</script>
<style>
.set-pass-container {
    width: 100%;
    padding-left: 4px;
    background: #fff;
    border-radius: 8px;
}

/* 头部样式 */
.header-section {
    margin-bottom: 40px;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    cursor: pointer;
    margin-bottom: 20px;
    font-size: 14px;
}

.back-button:hover {
    color: #EF436D;
}

/* 表单样式 */
.form-section {
    margin-bottom: 60px;
}

.form-item {
    margin-bottom: 40px;
}

.form-label {
    display: block;
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
    font-weight: 500;
}

.required {
    color: #EE0000;
    margin-right: 4px;
}

/* 手机号验证部分 */
.phone-display {
    width: 514px;
    height: 48px;
    font-size: 13px;
    line-height: 48px;
    color: #333;
    margin-bottom: 16px;
    padding: 0 16px;
    background: #F7F7F7;
    border-radius: 4px;
    display: inline-block;
}

.verification-row {
    width: 514px;
    height: 48px;
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 12px;
}

.verification-input {
    width: 408px;
    height: 48px;
}

.verification-input .el-input__inner {
    height: 48px !important;
    line-height: 48px !important;
    padding: 0 15px 0 40px !important; /* 为前缀图标留出空间 */
    box-sizing: border-box !important;
}

.verification-input .el-input__prefix .el-input__icon {
    line-height: 48px !important;
    font-size: 16px !important;
}

/* 确保整个输入框容器没有额外的边距和偏移 */
.verification-input .el-input {
    margin: 0 !important;
    vertical-align: top !important;
}

/* 修复可能的基线对齐问题 */
.verification-row {
    align-items: flex-start !important;
}

.send-code-btn {
    background: #EF436D;
    border-color: #EF436D;
    height: 100% !important;
}

.send-code-btn:disabled {
    background: #ccc;
    border-color: #ccc;
}

.send-code-btn:disabled:hover {
    background: #ccc;
    border-color: #ccc;
}

/* 密码输入框样式 */
.password-inputs {
    display: flex;
    gap: 16px;
    align-items: center;
}

.password-input {
    width: 48px;
    height: 48px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    outline: none;
    transition: border-color 0.3s;
}

.password-input:focus {
    border-color: #EF436D;
    box-shadow: 0 0 0 2px rgba(239, 67, 109, 0.1);
}

.line {
    width: 100%;
    height: 2px;
    background-color: #F8F8F8;
}

/* 按钮样式 */
.button-section {
    width: 514px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.cancel-btn {
    min-width: 98px;
    height: 40px;
    border: 1px solid #ddd;
    color: #666;
    background: #fff;
}

.cancel-btn:hover {
    border-color: #999999;
    color: #666666;
}

.confirm-btn {
    min-width: 98px;
    height: 40px;
    background: #EF436D;
    border-color: #EF436D;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .set-pass-container {
        padding: 20px 16px;
    }

    .password-inputs {
        gap: 8px;
    }

    .password-input {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .verification-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .send-code-btn {
        width: 100%;
    }

    .button-section {
        flex-direction: column;
        gap: 16px;
    }

    .cancel-btn,
    .confirm-btn {
        width: 100%;
    }
}

/* 钱包设置页面样式 */
.wallet-settings {
    max-width: 600px;
}

.settings-title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin: 0 0 40px 0;
}

.setting-item {
    height: 40px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #F8F8F8;
    align-items: center;
    cursor: pointer;
}

.setting-item:hover {
    background-color: #f9f9f9;
}

.setting-item:last-of-type {
    border-bottom: none;
}

.setting-content {
    flex: 1;
}

.setting-label {
    font-size: 14px;
    color: #333;
    font-weight: 400;
}

.setting-action {
    display: flex;
    align-items: center;
    color: #999;
}

.setting-action .el-icon-arrow-right {
    font-size: 14px;
    color: #ccc;
}

.setting-tip {
    font-size: 13px;
    color: #EE0000;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wallet-settings {
        padding: 20px 16px;
    }

    .settings-title {
        font-size: 20px;
        margin-bottom: 30px;
    }

    .setting-item {
        padding: 16px 0;
    }

    .setting-label {
        font-size: 15px;
    }
}
</style>
