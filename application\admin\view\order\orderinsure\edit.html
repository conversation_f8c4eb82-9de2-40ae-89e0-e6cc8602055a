<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Jy_order')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-jy_order" data-rule="required" class="form-control" name="row[jy_order]" type="number" value="{$row.jy_order|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Insure_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-insure_id" data-rule="required" data-source="insure/index" class="form-control selectpage" name="row[insure_id]" type="text" value="{$row.insure_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('No')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-no" data-rule="required" class="form-control" name="row[no]" type="text" value="{$row.no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bj_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bj_price" data-rule="required" class="form-control" name="row[bj_price]" type="number" value="{$row.bj_price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Goods_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-goods_price" data-rule="required" class="form-control" name="row[goods_price]" type="number" value="{$row.goods_price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ag_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ag_price" data-rule="required" class="form-control" name="row[ag_price]" type="number" value="{$row.ag_price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Base_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-base_price" data-rule="required" class="form-control" name="row[base_price]" type="number" value="{$row.base_price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_price" data-rule="required" class="form-control" name="row[total_price]" type="number" value="{$row.total_price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Collect_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-collect_price" data-rule="required" class="form-control" name="row[collect_price]" type="number" value="{$row.collect_price|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
