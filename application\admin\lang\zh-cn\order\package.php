<?php

return [
    'Id'                        => 'id',
    'Status'                    => '仓库状态',
    'Status1'                    => '未入仓',
    'Status2'                    => '已入仓',
    'Status3'                    => '待出货',
    'Status4'                    => '已出货',
    'Status5'                    => '已完成',
    'User_id'                   => '用户ID',
    'Entrust_no'                => '委托单号',
    'Waybill'                   => '快递单号',
    'Wbill_name'                => '快递名称',
    'Goods_name'                => '商品名称',
    'Goods_url'                 => '商品网址',
    'Num'                       => '商品数量',
    'Scale'                     => '重量',
    'Volume'                    => '材积',
    'Length'                    => '长度',
    'Width'                     => '宽度',
    'Height'                    => '高度',
    'Unit_price'                => '单价',
    'Wh_id'                     => '仓库',
    'Transport'                 => '运输方式',
    'Transport0'                 => '无',
    'Transport1'                 => '海快',
    'Transport2'                 => '空运',
    'Transport3'                 => '海运',
    'Addser_id'                 => '额外服务ID',
    'Addser_no'                 => '额外服务单号',
    'Goodstype'              => '货物类型',
    'Uremarks'                  => '用户备注',
    'Oremarks'                  => '订单备注',
    'Arrivetime'                => '入仓库时间',
    'Refuse'                    => '拒收',
    'Refuse0'                    => '否',
    'Refuse1'                    => '是',
    'Twe_id'                    => '台湾快递',
    'Bill_no'                   => '物流单号',
    'Createtime'                => '记录时间',
    'Updatetime'                => '更新时间',
    'User.username'             => '用户名',
    'Warehouse.title'           => '名称',
    'Addservice.addser_type_id' => 'addservice_type表ID',
    'Addservice.tb_money'       => '总金额',
    'Addservice.bal_money'      => '抵扣金',
    'Addservice.actual_money'   => '实际需支付',
    'Addservice.order_status'   => '额外服务状态',
    'Goodstype.title'           => '名称',
    'Twlogistics.title'           => '名称',
    'Twlogistics.type'            => '0宅配1超商取货',
    'Package details'           => '包裹详情',




];
