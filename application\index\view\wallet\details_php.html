{include file="common/meta" /}
{load href="__CDN__/assets/css/element-ui-index.css,__CDN__/assets/css/transportation.css" /}
{load href="__CDN__/assets/js/vue.js,__CDN__/assets/js/element-ui-index.js,__CDN__/assets/js/axios.min.js" /}
<div class="transport-box" id="app">
    <div class="left_d">
        <div class="user">
            {include file="user/index" /}
        </div>
        <div class="my-Wallet">
            {include file="wallet/index" /}
        </div>
        <div class="my-coupons">
            {include file="coupon/index" /}
        </div>
        <div class="menu-list">
            <div class="title">
                {:__('Transport center')}
            </div>
            <div class="list_d">
                {include file="common/nav" /}
            </div>
        </div>
        <div class="send">
            {include file="common/send" /}
            <i class="el-icon-arrow-right" style="font-weight: 600"></i>
        </div>
        <div class="calc">
            {include file="common/calc" /}
            <i class="el-icon-arrow-right" style="font-weight: 600"></i>
        </div>
    </div>
    <div class="right-container" >
        <div class="wallet-page">
            <div class="title">賬戶明細</div>
            <div class="form-box">
                <div style="display: flex; gap: 32px">
                    <div style="display: flex; align-items: center">
                        <div class="title-filter">狀態：</div>
                        <el-select placeholder="全部/儲值/支出/退款" v-model="filterStatus">
                            <el-option :value="null" label="全部"></el-option>
                            <el-option :key="index"
                                       :label="item"
                                       :value="item"
                                       v-for="(item,index) in uniqueTypes"></el-option>
                        </el-select>
                    </div>
                    <div style="display: flex; align-items: center">
                        <div class="title-filter">時間：</div>
                        <div>
                            <el-date-picker
                                    end-placeholder="结束日期"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    type="daterange"
                                    v-model="filterDateRange">
                            </el-date-picker>
                        </div>
                    </div>
                </div>
                <div>
                    <el-button @click="applyFilters"
                               icon="el-icon-search"
                               style="background: #EF436D; margin-right: 16px"
                               type="danger">搜索
                    </el-button>
                    <el-button @click="resetFilters"
                               icon="el-icon-refresh"
                               style="background: #FFFFFF">重置
                    </el-button>
                </div>
            </div>

            <style>
                .native-table {
                    width: 1446px;
                    border-collapse: collapse;
                }
                .native-table th, .native-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                .native-table th {
                    background-color: #f2f2f2;
                }
                .native-table tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
            </style>

            <!-- Native HTML table as placeholder -->
            <table class="native-table">
                <thead>
                <tr>
                    <th>分類</th>
                    <th>訂單編號</th>
                    <th>創建時間</th>
                    <th>收入</th>
                    <th>餘額</th>
                    <th>收支類型</th>
                    <th>備註</th>
                </tr>
                </thead>
                <tbody>

                {volist name="list" id="ls"  }
                <tr v-for="(item, index) in pageAccountData" :key="index">
                    <td><?=$site['category'][$ls['order_table']]?></td>
                    <td>{$ls.order_no}</td>
                    <td>{$ls.createtime|date='Y-m-d H:i:s',###}</td>
                    <td><?=$ls['rae']?'+':'-'?>{$ls.money}</td>
                    <td>{$ls.surplus_money}</td>
                    <td><?=$site['wallettype'][$ls['type']]?></td>
                    <td>{$ls.msg}</td>
                </tr>
                {/volist}
                </tbody>
            </table>

            {$list->render()}

        </div>
    </div>
</div>
