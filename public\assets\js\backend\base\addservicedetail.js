define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'base/addservicedetail/index' + location.search,
                    add_url: 'base/addservicedetail/add',
                    edit_url: 'base/addservicedetail/edit',
                    del_url: 'base/addservicedetail/del',
                    multi_url: 'base/addservicedetail/multi',
                    import_url: 'base/addservicedetail/import',
                    table: 'addservice_detail',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'no', title: __('No'), operate: 'LIKE'},
                        {field: 'package.entrust_no', title: __('Package.entrust_no'), operate: 'LIKE'},
                        {field: 'addservice.order_no', title: __('Addservice.order_no'), operate: 'LIKE'},
                        {field: 'type.title', title: __('Type.title'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'type.amount', title: __('Type.amount')},
                        // {field: 'pid', title: __('Pid')},
                        // {field: 'aid', title: __('Aid')},
                        // {field: 'at_id', title: __('At_id')},
                        {field: 'status', title: __('Status')},
                        // {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
