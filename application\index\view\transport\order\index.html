{include file="common/resources" /}
<script src="//at.alicdn.com/t/c/font_4960088_jne4eo87lz9.js"></script>
<!-- 添加CSRF token -->
{:token()}
<style>
    @font-face {
        font-family: 'Source Han Sans CN';
        src: url('__CDN__/assets/css/font_family/SourceHanSans/SourceHanSansTW-Regular.otf') format('opentype');
        font-style: normal;
        font-display: swap;
    }
    
    /* Order filter styles */
    .order-filter {
        background-color: #fff;
        padding: 20px;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        overflow: hidden; /* 防止内容溢出 */
    }
    
    .filter-row {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        width: 100%;
    }
    
    .filter-row:last-child {
        margin-bottom: 0;
    }
    
    .filter-row.equal-width {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap; /* 允许元素在必要时换行 */
    }
    
    .filter-row.equal-width .filter-item {
        width: 28%;
        margin-right: 0;
        margin-bottom: 2px; /* 添加底部间距，防止换行元素之间挤压 */
    }
    
    .filter-item {
        display: flex;
        align-items: center;
        /* margin-right: 24px; */
    }
    
    .filter-input-container {
        flex: 1;
        min-width: 0; /* 允许容器缩小 */
    }
    
    .filter-input-container .el-select,
    .filter-input-container .el-input {
        width: 100%; /* 使输入框宽度自适应容器 */
        max-width: 350px; /* 限制最大宽度 */
    }
    
    .filter-label {
        font-size: 14px;
        color: #333;
        margin-right: 8px;
        width: 90px;
        text-align: right;
        white-space: nowrap; /* 防止标签文字换行 */
    }
    
    .filter-row.second-row {
        justify-content: space-between;
        flex-wrap: wrap; /* 允许元素在必要时换行 */
    }
    
    .filter-row.second-row .date-item {
        flex: 0 0 auto;
        min-width: auto; /* 移除最小宽度限制 */
        width: 28%; /* 与第一行筛选项保持一致的宽度 */
        margin-bottom: 16px;
    }
    
    .el-date-editor.el-input, 
    .el-date-editor.el-input__inner {
        width: 100%; /* 使日期选择器宽度自适应容器 */
        max-width: 350px; /* 与第一行下拉框保持一致的最大宽度 */
    }
    
    /* 响应式布局 */
    @media (max-width: 1200px) {
        .filter-row.equal-width .filter-item {
            width: 48%;
        }
        
        .filter-row.second-row .date-item {
            width: 60%;
        }
    }
    
    @media (max-width: 992px) {
        .filter-row.equal-width .filter-item {
            width: 100%;
        }
        
        .filter-row.second-row .date-item {
            width: 100%;
        }
        
        .filter-row.second-row {
            flex-direction: column;
            align-items: stretch;
        }
        
        .filter-btns {
            margin-left: 0;
            margin-top: 16px;
            justify-content: flex-end;
            width: 100%;
        }
    }
    
    @media (max-width: 768px) {
        .filter-item {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .filter-label {
            width: 100%;
            text-align: left;
            margin-bottom: 8px;
        }
    }

    /* 修复 el-popconfirm 焦点管理问题 */
    .el-popconfirm__main {
        outline: none;
    }

    .el-popconfirm .el-button:focus {
        outline: none;
    }

    /* 确保弹窗关闭时正确处理焦点 */
    .el-popconfirm[aria-hidden="true"] .el-button {
        pointer-events: none;
    }
</style>
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="order">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <!-- 标签导航 -->
        <div class="tab-header relative">
            <div class="absolute fw fs16" style="top: 0; left: 0; color: #3D3D3D">訂單列表</div>
            <div :class="{ 'active': activeIndex === index }" :key="index" @click="changeTabs(activeIndex = index)"
                class="tab-item pointer fs16" v-for="(item, index) in tabList">
                {{ item }}
            </div>
        </div>
        <div class="mb16 mt24">
            <div class="order-filter">
                <div class="filter-row equal-width">
                    <div class="filter-item">
                        <div class="filter-label">訂單編號：</div>
                        <div class="filter-input-container">
                            <el-input placeholder="請輸入訂單編號"
                                      v-model="tranForm.orderNum"
                                      @keyup.enter.native="searchParcel">
                            </el-input>
                        </div>
                    </div>
                    <div class="filter-item">
                        <div class="filter-label">訂單金額：</div>
                        <div class="filter-input-container">
                            <el-input placeholder="請輸入訂單金額"
                                      v-model="tranForm.orderPrice"
                                      @keyup.enter.native="searchParcel">
                            </el-input>
                        </div>
                    </div>
                    <div class="filter-item">
                        <div class="filter-label">選擇狀態：</div>
                        <div class="filter-input-container">
                            <el-select placeholder="全部/待支付/待簽收/已完成" 
                                      @change="orderStatusChange"
                                      v-model="tranForm.payStatus">
                                <el-option v-for="item in orderOptions"
                                          :key="item.value" 
                                          :label="item.label"
                                          :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
                <div class="filter-row second-row">
                    <div class="filter-item date-item">
                        <div class="filter-label">時間：</div>
                        <div class="filter-input-container">
                            <el-date-picker v-model="orderDate"
                                            type="daterange"
                                            align="center"
                                            unlink-panels
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            type="daterange"
                                            :picker-options="pickerOptions"
                                @change="onDateChange">
                            </el-date-picker>
                        </div>
                    </div>
                    <div class="filter-btns">
                        <el-button icon="el-icon-search" 
                                  type="danger" 
                                  style="background: #EF436D; color: #fff;" 
                                  @click="searchParcel">搜索
                        </el-button>
                        <!-- <el-button icon="el-icon-download" 
                                  type="success" 
                                  style="background: #22B573; color: #fff;">導出
                        </el-button> -->
                        <el-button icon="el-icon-refresh" 
                                  style="background: #FFFFFF;" 
                                  @click="resetInput">重置
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="activeIndex === 0">
            <div class="order-table">
                <div v-if="transportList.length === 0" style="width: 100%; min-height: 534px">
                    <el-empty description="暫無數據"></el-empty>
                </div>
                <el-table v-else :data="transportList" style="width: 100%; min-height: 534px;" class="custom-header ">
                    <el-table-column label="創建訂單時間" width="200px">
                        <template slot-scope="scope">
                            <span class="fs14 c333">{{ formatDate(scope.row.createtime, 1) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="order_no" label="訂單編號" width="200px"></el-table-column>
                    <el-table-column label="訂單金額" width="120px" >
                        <template slot-scope="scope">
                            <span class="fs14 c333">NT${{ scope.row.tb_money }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="	scale" label="重量" width="120px" >
                        <template slot-scope="scope">
                            <span class="fs14 c333">{{ scope.row.scale }}kg</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column prop="waybill" label="快遞單號" width="200px">-->
<!--                        <template slot-scope="scope">-->
<!--                            <div class="fs14 c333" v-for="item,index in scope.row.pno">{{ item }}</div>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="發貨時間" width="200px">
                        <template slot-scope="scope">
                            <span class="fs14 c333">{{ formatDate(scope.row.updatetime, 2) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="狀態" width="120px" >
                        <template slot-scope="scope">
                            <span class="fs14 pointer" :style="{ color: scope.row.order_status === 0 ? '#EF436D' : '#22B573' }">{{
                                scope.row.pay_status === 0 ? '未支付' :
                                    scope.row.pay_status === 1 ? '部分支付' :
                                        scope.row.pay_status === 2 ? '已支付' :
                                            scope.row.pay_status === 3 ? '多支付' : ''
                                }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="發貨人" width="120px" >
                        <template slot-scope="scope">
                            <span class="fs14 c333">{{ scope.row.sender }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <div class="fs14 red pointer" style="display: flex; align-items: center; gap: 8px">
                                <span @click="openOrderDetail(scope.row)">查看訂單詳情</span>
                                <el-popconfirm
                                    title="確認支付該訂單？"
                                    confirm-button-text="確定"
                                    cancel-button-text="取消"
                                    @confirm="handlePayConfirm(scope.row)"
                                    @cancel="handlePayCancel">
                                    <span v-if="scope.row.pay_status == 0" slot="reference">去支付</span>
                                </el-popconfirm>
                                
                                <span v-if="false">取消訂單</span>
                                <span v-if="scope.row.order_status == 2">確認收貨</span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="my-footer">
                <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]"
                               :total="transportList.length" @current-change="handleCurrentChange"
                               @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper">
                </el-pagination>
            </div>
        </div>
        <div v-if="activeIndex ===1">
            <div class="order-table">
                <div v-if="addServiceList.length === 0" style="width: 100%; min-height: 534px">
                    <el-empty description="暫無數據"></el-empty>
                </div>
                <el-table v-else :data="addServiceList" style="width: 100%; min-height: 534px" class="custom-header">
                    <el-table-column label="創建訂單時間">
                        <template slot-scope="scope">
                            <span class="fs14 c333">{{ formatDate(scope.row.createtime, 1) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="包裹/訂單編號" >
                        <template slot-scope="scope">
                           <span class="fs14 c333"> {{ scope.row.wbill_name}}{{ scope.row.waybill }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="服務內容（訂單號）">
                        <template slot-scope="scope">
                           <span class="fs14 c333"> {{ scope.row.title}}{{ scope.row.ser_no }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column class="fs14 c333" prop="amount" label="訂單金額"></el-table-column>
                    <el-table-column prop="status" label="狀態">
                        <template slot-scope="scope">
                            <span :style="{ color: scope.row.status === 0 ? '#EF436D' : '#22B573' }">{{scope.row.status === 0 ? '進行中' : '已完成' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <span style="color: #EF436D; cursor: pointer; margin-right: 8px;">查看訂單詳情</span>
                            <span v-if="scope.row.status === '待支付'" style="color: #EF436D; cursor: pointer; margin-right: 8px;">去支付</span>
                            <span v-if="scope.row.status === '待支付'" style="color: #EF436D; cursor: pointer;">取消訂單</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="my-footer">
                <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]"
                               :total="addServiceList.length" @current-change="handleCurrentChange"
                               @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper">
                </el-pagination>
            </div>
        </div>
        <!-- 集运订单详情弹窗 -->
        <el-dialog title="" center :close-on-click-modal="false" class="order-detail" :visible.sync="orDetailDialogVisible" width="980px" >
            <div class="packup-dialog-wrap" style="height: 100%; display: flex; align-items: stretch;">
                <div class="packup-dialog-left custom-scrollbar relative" style="overflow-y: auto; overflow-x: hidden;">
                    <div class="packup-order-status">
                        <span class="packup-status-icon">
                            <img :src="transportIcon[0]" alt="">
                        </span>
                        <span class="fs14 c333">海快</span>
                        <span class="packup-status-pay absolute">{{
                            orderInfo.pay_status == 0 ? '未支付' :
                                orderInfo.pay_status == 1 ? '部分支付' :
                                    orderInfo.pay_status == 2 ? '已支付' :
                                        orderInfo.pay_status == 3 ? '多支付' : ''
                            }}</span>
                    </div>
                    <div class="packup-order-info">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                            <span class="packup-status-icon">
                                <img src="__CDN__/assets/img/pc/new_index/parcel13.svg" alt="">
                            </span>
                            <span class="c333 fs14">倉庫：{{orderInfo.title}}</span>
                        </div>
                        <div class="packup-order-row c333">
                            <span class="fs14 fw">訂單編號：</span>
                            <span class="fs18">{{orderInfo.order_no}}</span>
                        </div>
                        <div class="packup-order-row c333 fs14">
                            <span>創建時間：</span>
                            <span>{{formatDate(orderInfo.createtime,1)}}</span>
                        </div>
                        <div class="packup-order-btns Source-Han-Sans">
                            <button v-if="false" class="packup-btn packup-btn-calc">取消訂單</button>
                            <button v-if="orderInfo.pay_status == 0" class="packup-btn packup-btn-pay" @click="openPayDialog(orderInfo)">去付款</button>
                        </div>
                    </div>
                    <div class="line4" style="height: 4px; background: #f4f4f4; width: calc(100% + 50px);margin-left: -24px; flex-shrink: 0;"></div>
                    <div class="packup-section">
                        <div class="packup-section-title">收貨地址</div>
                        <div class="packup-address">
                            <div class="addr-svg">
                                <img alt="" src="__CDN__/assets/img/pc/new_index/parcel09.svg">
                            </div>
                            <div class="packup-address-info">
                                <div>{{addrInfo.detail}}</div>
                                <div style="margin-top: 4px">{{addrInfo.name}}（{{addrInfo.mobile}}）</div>
                            </div>
                            <div class="packup-address-edit pointer" @click="editAddr">
                                <i class="el-icon-edit" style="font-size: 12px; margin-right: 8px"></i>
                                <span >我要修改收貨地址（{{orderInfo.waybill ? '需付費200元' : '免費'}}）
                                    <el-tooltip v-if="orderInfo.order_status ==2" popper-class="my-tooltip" effect="light" placement="bottom-start">
                                        <template slot="content">
                                            <div class="Source-Han-Sans" style="font-size: 9px; color: rgba(0, 0, 0, 0.6); line-height: 16px">
                                                已裝車後無法再查找包裹改地址，只能到台灣清關出來以後，逐件查找再拉回報關行重新列印貨運單。此時修改地址需收費200元，時效會耽誤1天。且因清關現場作業速度很快，可能攔截不到。
                                            </div>
                                            <div class="Source-Han-Sans" style="font-size: 9px;color: rgba(0, 0, 0, 0.6); line-height: 16px">若發生地址未修改成功的情況可聯絡客服申請退款。</div>
                                        </template>
                                        <i class="el-icon-question" style="color: #C7C7C7"></i>
                                    </el-tooltip>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="packup-section">
                        <div class="packup-section-title">報關方式</div>
                        <div class="packup-declare">
                            <div style="display: flex; gap: 12px; align-items: center">
                                <div class="my-tag">申報人</div>
                                <span>{{applInfo.username}}({{applInfo.card}})</span>
                            </div>
                            <div style="display: flex; gap: 12px; align-items: center">
                                <div class="my-tag">報關方式</div>
                                <span>{{customsType}}</span>
                            </div>
                        </div>
                    </div>
                    <div v-if="lostInfo.bj_price" class="packup-section">
                        <div class="packup-section-title">丟失保障</div>
                        <div class="packup-insurance">
                            <div class="packup-insurance-info">
                                <span><i class="el-icon-warning" style="color: #FF9200; font-size: 20px;"></i></span>
                                <span class="Source-Han-Sans fs12">如包裹全部丟失，將賠償商品價值0元，保價金額{{lostInfo.bj_price}}元，歉意金{{apologyFee}}元，以及運費{{orderInfo.base_price}}元，合計{{totalFee}}元</span>
                            </div>
                        </div>
                    </div>
                    <div class="packup-section">
                        <div class="fw14 c333 mb12"><span class="red">{{orderInfo.num}}</span> 個包裹</div>
                        <div class="packup-summary-list">
                            <div class="packup-summary-row">
                                <div class="fs13 c666">重量合計：</div>
                                <div style="display: flex; flex-direction: column; gap: 8px;text-align: right">
                                    <div>計算重量：{{calculateWeight(orderInfo.scale || 0)}}KG</div>
                                    <div>實際重量：{{Number(orderInfo.scale).toFixed(1)}}KG</div>
                                </div>
                            </div>
                            <div class="packup-summary-row">
                                <div class="fs13 c666">基礎運費：</div>
                                <div style="display: flex; flex-direction: column; gap: 8px">
                                    <div class="packup-summary-value">NT$ {{orderInfo.base_price}}</div>
                                    <div class="fs13 c666"></div>
                                </div>
                            </div>
                            <div class="packup-summary-row" v-if="orderInfo.transport == 15">
                                <div class="fs13 c666">{{shuiType}}</div>
                                <div style="display: flex; flex-direction: column; gap: 8px">
                                    <div class="packup-summary-value" style="text-align: right">NT$ {{shuiFee}}</div>
<!--                                    <div class="fs13 c666">(小於5KG固定收取50元)</div>-->
                                </div>
                            </div>
                            <div v-if="orderInfo.disp" class="packup-summary-row">
                                <div class="fs13 c666">派件費：</div>
                                <div class="packup-summary-value">NT$ {{orderInfo?.disp || 0}}</div>
                            </div>
                            <div v-if="orderInfo.super_disp" class="packup-summary-row">
                                <div class="fs13 c666">超大派件費：</div>
                                <div class="packup-summary-value">NT$ {{orderInfo?.super_disp || 0}}</div>
                            </div>
                            <div v-if="timeoutAmount" class="packup-summary-row">
                                <div class="fs13 c666">超時險：</div>
                                <div class="packup-summary-value">NT$ {{timeoutAmount}}</div>
                            </div>
                            <div v-if="lostAmount" class="packup-summary-row">
                                <div class="fs13 c666">丟失保價：</div>
                                <div class="packup-summary-value">NT$ {{lostAmount}}</div>
                            </div>
                            <div class="packup-summary-row">
                                <div class="fs18 fw c3d3">合計：</div>
                                <div class="packup-summary-total-value"> <span v-if="orderInfo.actual_money">NT$</span>  {{ orderInfo.actual_money || '暫無'}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-line"></div>
                <div class="packup-dialog-right custom-scrollbar" style="overflow-y: auto;">
                    <div class="packup-goods-list" v-for="(item,index) in goodsList" :key="index">
                        <div class="packup-goods-item">
                            <div class="packup-goods-header">
                                <div class="fs13" style="display: flex; align-items: center; gap: 9px">
                                    <div style="width: 14px;height: 14px;">
                                        <svg class="icon fs14" aria-hidden="true">
                                            <use :xlink:href="item.icon"></use>
                                        </svg>
                                    </div>
                                    <div >{{item.sname}}{{item.waybill || '快遞單號異常'}}</div>
                                </div>
                                <div class="fw" style="display: flex; align-items: center">包裹總重量：
                                    <div class="red mr12">{{item.scale}}KG</div>
                                    <div style="width: 20px; height: 20px;" class="pointer" v-if="false">
                                        <img style="width: 100%; height: 100%" src="__CDN__/assets/img/pc/new_index/img.svg" alt="">
                                    </div>
                                </div>
                            </div>
                            <div class="packup-goods-body">
                                <div class="packup-goods-img">
                                    <img :src="item.goods_url || '__CDN__/assets/img/pc/new_index/no_package.png'" alt="">
                                </div>
                                <div class="packup-goods-info">
                                    <div class="red fs14">[{{item.goods_name}}]</div>
                                    <div class="c333 fs13">暫無商品詳情信息</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
        <!--        支付彈窗-->
        <el-dialog :visible.sync="payDialogVisible" width="514px" class="custom-dialog" :show-close="true"
                   :close-on-click-modal="false" :close-on-press-escape="false" title="支付訂單" center>
            <!-- 支付金额盒子 -->
            <div class="pay-box">
                <div class="pay-amount-title">支付金額</div>
                <div class="pay-amount">NT$ <span>{{ orderInfo.actual_money || orderRow.tb_money }}</span></div>
            </div>
            <!-- 支付方式 -->
            <!--            <div class="pay-method-title" style="margin-bottom: 16px">支付方式</div>-->
            <div class="sub-title relative" style="margin: 16px 0 16px 14px; width: 94%">
                <div class="sub-line"></div>
                <div style="margin-left: -4px">支付方式</div>
                <div class="pay-method-info absolute" style="top: 0; right: 0">
                    T幣（剩餘：{{uMoney}}）
                </div>
            </div>
            <!-- 密码输入 -->
            <div v-if="Number(orderInfo.actual_money || orderRow.tb_money || 0) > Number(uMoney)" class="flex-between" style="margin: 12px 24px">
                <span class="fs13 red">* <span style="color:#ff9200">T幣餘額不足</span></span>
                <a href="/index/recharge/index" class="red fs14 underline">立即儲值</a>
            </div>
            <div  v-else>
                <div class="pay-password-box">
                    <span style="color:#ff4d7a;font-size:14px;">* 請輸入6位錢包密碼</span>
                </div>
                <div class="pay-password-inputs">
                    <input v-for="(num, idx) in 6" :key="idx" class="pay-password-input" type="password" maxlength="1"
                           inputmode="numeric" pattern="[0-9]*" v-model="password[idx]" :disabled="idx !== currentInput"
                           @focus="onFocus(idx)" @input="onInput(idx, $event)" @keydown.backspace="onBackspace(idx, $event)"
                           ref="inputs" autocomplete="off" />
                </div>
            </div>
           
        </el-dialog>
        <!-- 收貨地址 -->
        <el-dialog :close-on-click-modal="false" :visible.sync="addrlsDialogVisible" right class="forward" color="red"
                   title="選擇收貨地址" width="514">
            <div>
<!--                <div class="address-item mb16" style="position:relative; background: #FFF7F9">-->
<!--                    <el-avatar src="" size="medium" style="margin-right: 16px;">{{applInfo.username}}</el-avatar>-->
<!--                    <div class="applicant-info">-->
<!--                        <div style="display: flex; align-items: center; gap: 8px;">-->
<!--                            <span class="c333 fs13">{{ applInfo.username }} {{ applInfo.mobile }}</span>-->
<!--                        </div>-->
<!--                        <div class="fs13 c333" style="margin-top: 2px;">{{ applInfo.card }}</div>-->
<!--                    </div>-->
<!--                    <span class="red fs13 underline pointer" style="position: absolute; top: 12px; right: 16px" @click="openApplicant">重新選擇</span>-->
<!--                </div>-->
                <div class="input-serach mb16">
                    <el-input width="100%" placeholder="搜索姓名/手機號/身份證號" v-model="searchValue" :loading="searchLoading"
                              @keyup.enter.native="searchApplicant">
                        <template #suffix>
                            <el-button icon="el-icon-search" style="color: #333333;" @click="searchApplicant"
                                       type="text"></el-button>
                        </template>
                    </el-input>
                </div>
                <div style="display: flex; flex-direction: column; gap: 4px;">
                    <div v-for="item in addressList" :key="item.id">
                        <div @click="selectAddress(item)" class="address-item" :data-id="item.id" style="flex-direction: column; align-items: flex-start;">
                            <span class="c333 fs13 fw">{{ item.detail }}
                                <el-tag class="ml8" v-if="item.is_def" type="danger" style="font-size: 10px; font-weight: normal;" size="mini">默認地址</el-tag>
                            </span>
                            <span class="c333 fs13">{{ item.name }} ({{ item.mobile }})
                                <el-tag class="ml8"
                                        :type="item.lname === '本人' ? 'warning' : 'info' " size="mini" effect="plain"
                                        :style="item.lname === '本人' ? {background: '#FFF1E9', color: '#E37318'} : {background: '#F4F4F4', color: '#666666'}"
                                >{{item.lname}}</el-tag>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <span class="dialog-footer" slot="footer">
                <el-button @click="openAddAddress" size="mini" type="success" icon="el-icon-circle-plus-outline"
                           style="background: #22B573; border: none;">{{activeChange === '1' ? '新增個人申報人' :
                    '新增公司申報人'}}</el-button>
                <el-button size="mini" type="danger" style="background: #EF436D"  icon="el-icon-circle-plus-outline" @click="addrlsDialogVisible=false">下一步</el-button>
            </span>
        </el-dialog>
        <el-dialog :close-on-click-modal="false" :visible.sync="addAddressDialogVisible" right class="forward" color="red"
                   title="新增收件人" width="1008">
            <div style="padding: 0 40px 0 40px;">
                <el-form :model="addAddressForm" ref="addAddressForm" label-width="100px" :rules="rules" style="display: flex">
                    <div>
                        <el-form-item label="所在區域" prop="region" required style="margin-bottom: 18px;">
                            <el-cascader
                                    v-model="addAddressForm.region"
                                    :options="areaList"
                                    placeholder="請選擇"
                                    @change="changeArea"></el-cascader>
                        </el-form-item>
                        <el-form-item label="姓名" prop="name" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.name" placeholder="請輸入收件人姓名" />
                        </el-form-item>
                        <el-form-item label="身份證號" prop="idCard" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.idCard" placeholder="請輸入收件人身份證號" />
                        </el-form-item>
                        <el-form-item label="固定號碼" prop="fixedNumber">
                            <el-input v-model="addAddressForm.fixedNumber" placeholder="請輸入" />
                        </el-form-item>
                    </div>
                    <div style="flex: 1;">
                        <el-form-item label="詳細地址" prop="address" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.address" placeholder="請輸入詳細地址例如**街**號" />
                        </el-form-item>
                        <el-form-item label="手機號碼" prop="mobile" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.mobile" placeholder="請輸入手機號碼" />
                        </el-form-item>
                        <el-form-item label="郵政編碼" prop="postalCode" required style="margin-bottom: 18px;">
                            <el-input v-model="addAddressForm.postalCode" placeholder="請輸入郵政編碼" />
                        </el-form-item>
                        <el-form-item label="設定標籤" prop="label" class="tags-item" label-width="auto">
                            <el-radio-group @input="handleRadioInput" v-model="addAddressForm.label" class="tags-radio">
                                <el-radio
                                        v-for="item in identityList" :key="item.label" :label="item.label">{{ item.name }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </div>
                </el-form>
                <div style="display: flex; align-items: center; margin-top: 32px;">
                    <el-switch v-model="addAddressForm.isDef" active-text="預設寄送地址" style="margin-right: 16px;" />
                </div>
                <div style="display: flex; justify-content: flex-end; gap: 24px; margin-top: 32px;">
                    <el-button  size="mini" @click="closeAddAddress">取消</el-button>
                    <el-button size="mini" type="danger" style="background: #EF436D;" @click="submitAddAddress">確定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</div>

<script>

    const user = {:json_encode($user)};
    console.log(user, 'user');
    console.log('用户 nouse 状态:', user.nouse, '类型:', typeof user.nouse);

    var app = new Vue({
        el: '#order',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            uMoney: user.money,
            uNouse: user.nouse,
            password: ['', '', '', '', '', ''], // 密码数组
            uPassword: '', // 用户输入的密码
            currentInput: 0,      // 当前可输入的input索引
            activeIndex: 0,
            tabList: ['集運訂單', '附加服務訂單'],
            tranForm: {
                orderNum:'',        // 订单号
                payStatus: '',      // 支付状态
                orderPrice: '',     // 订单价格
                begTime: '',        // 开始时间
                endTime: '',        // 结束时间
            },
            orderOptions: [
                { value: 0, label: '未支付' },
                { value: 1, label: '部分支付' },
                { value: 2, label: '已支付' },
                { value: 3, label: '多支付' },
            ],
            addressList: [],    // 收货地址列表
            applicantList: [], // 申报人列表
            areaList:[],    // 地区列表
            identityList: [],   // 身份证类型列表
            searchValue:'',
            activeChange: '1',
            addAddressForm: {
                region: '',
                name: '',
                idCard: '',
                fixedNumber: '',
                address: '',
                mobile: '',
                postalCode: '',
                label: 1,
                isDef: 0
            },
            rules: {
                userName: [{ required: true, message: '請输入姓名', trigger: 'blur' }],
                name: [{ required: true, message: '請輸入收件人姓名', trigger: 'blur' }],
                mobile: [
                    { required: true, message: '請輸入手機號碼', trigger: 'blur' },
                    { pattern: /^09\d{8}$/, message: '手機號碼必須為09開頭的10位數字', trigger: 'blur' }
                ],
                idCard: [
                    { required: true, message: '請輸入收件人身份證號', trigger: 'blur' },
                    { pattern: /^[A-Z][0-9]{9}$/, message: '身份證號必須為1位大寫字母+9位數字', trigger: 'blur' }
                ],
                companyCode: [
                    { required: true, message: '請輸入公司編碼', trigger: 'blur' },
                    { pattern: /^\d{8}$/, message: '公司編碼必須為8位數字', trigger: 'blur' }
                ],
                companyMobile: [
                    { required: true, message: '請輸入手機門號', trigger: 'blur' },
                    { pattern: /^02\d{8}$/, message: '手機門號必須為02開頭的10位數字', trigger: 'blur' }
                ],
                address: [{ required: true, message: '請输入地址', trigger: 'blur' }],
                pickUpCode: [{ required: true, message: '請输入取件碼', trigger: 'blur' }],
                region: [{ required: true, message: '請選擇區域', trigger: 'change' }],
                postalCode: [
                    { required: true, message: '請輸入郵政編碼', trigger: 'blur' },
                    { pattern: /^\d{5,6}$/, message: '郵遞區號只能是5位或6位數字', trigger: 'blur' }
                ],
                address: [{ required: true, message: '請輸入詳細地址', trigger: 'blur' }],
                region: [{ required: true, message: '請選擇區域', trigger: 'change' }],
            },
            searchLoading: false,
            orDetailDialogVisible: false,
            payDialogVisible: false,
            addrlsDialogVisible: false, // 收货地址选择弹窗
            addAddressDialogVisible: false,     // 新增收件人弹窗
            applicantDialogVisible : false,
            applicantListDialogVisible : true,
            currentPage: 1,
            currentPageSize: 5,
            pageSize: [5, 10, 20, 50],
            transportIcon: [
                '__CDN__/assets/img/pc/transport01.svg',
                '__CDN__/assets/img/pc/transport02.svg',
                '__CDN__/assets/img/pc/transport03.svg',
            ],
            orderDate:'',
            pickerOptions: {
                shortcuts: [{
                    text: '近1個月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 1); // Subtract 1 month
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近3個月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 3); // Subtract 3 months
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近12個月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 12); // Subtract 12 months
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            transportList: [],
            addServiceList:[],
            // orderDetailGoodsList: [
            //     {
            //         img: '__CDN__/assets/img/pc/new_index/parcel.svg',
            //         type: '【手機架】',
            //         name: '懶人桌面手機支架支架桌面懶人直播看書直播懶得拿支架現貨...',
            //         color: '黑色可升降多功能支架',
            //         sn: 'SDK-F06C',
            //         price: '105',
            //         pay: '99.5',
            //         qty: '1',
            //         total: '99.5'
            //     }
            // ],
            orderInfo:{},   // 订单详情
            orderRow:{},     // 當前訂單详情数据
            addrInfo:{},    // 地址信息
            applInfo:{},    // 申报人信息
            goodsList:[],   // 商品列表
            ct:{},          // 運費計算
            timeoutAmount:null, // 超时險信息
            lostAmount:null,    // 丢失险信息
            timeoutInfo:{}, // 超时險信息
            lostInfo:{}, // 丟失保險信息
        },
        watch: {
            // 监听密码数 组，输入满6位自动触发
            password: {
                handler(val) {
                    if (val.every(v => v.length === 1 && /^\d$/.test(v))) {
                        // 输入满6位，自动支付
                        this.uPassword = this.password.join('');
                        // 使用 this.orderRow 作为参数，因为在弹窗中 orderRow 已经被设置
                        this.payOrder(this.orderRow);
                    }
                },
                deep: true
            },
        },
        computed: {
            showTranIcon() {
                switch (this.orderDetail.info.transport) {
                    case 15:
                        return this.transportIcon[0] // 海快
                    case 16:
                        return this.transportIcon[1] // 極兔
                    case 17:
                        return this.transportIcon[2] // 順豐
                    default:
                        return '';
                }
            },
            shuiType() {    // 税费类型
                if (Number(this.ct.style) === 1) {
                    return '不包稅';
                } else if (Number(this.ct.style) === 2) {
                    return '包頻繁稅';
                } else if (Number(this.ct.style) === 3) {
                    return '全包稅';
                } else {
                    return '';
                }
            },
            shuiFee() {   // 税费计算
                if (Number(this.ct.style) === 1) {
                    return 0;
                } else {
                    if (this.calculateWeight(this.orderInfo.scale) > 5) {
                        return (this.calculateWeight(this.orderInfo.scale) - 5) * this.ct?.tax + this.ct?.base_tax;
                    } else {
                        return this.ct?.base_tax;
                    }
                }
            },
            apologyFee() {  // 道歉金
                return this.lostInfo.bj_price * 0.1 + 15;
            },

            totalFee() {
                return this.lostInfo.bj_price +  this.apologyFee + this.orderInfo.base_price;
            },
            // 报关方式
            customsType() {
                if (this.orderInfo.transport === 15) {
                    switch (this.ct.style) {
                        case 1:
                            return 'X1 不包稅';
                        case 2:
                            return 'X2 包頻繁稅';
                        case 3:
                            return 'X3 全包稅';
                        default:
                            return '';
                    }
                }
                return 'X2 全包稅';
            }
        },
        mounted(){
            this.initData();
        },
        methods: {
            // 重量換算
            calculateWeight(weight) {
                return utils.calculateWeight(weight);
            },
            initData() {
                // this.transportList = transportData;
                // this.addServiceList= addServiceData;
                this.getOrderList();
            },

            /* 支付彈窗 */
            // 只允许输入数字，并自动跳到下一个输入框
            onInput(idx, e) {
                let val = e.target.value.replace(/\D/g, ''); // 只允许数字
                this.$set(this.password, idx, val);
                if (val && idx < 5) {
                    this.currentInput = idx + 1;
                    this.$nextTick(() => {
                        this.$refs.inputs[this.currentInput].focus();
                    });
                }
            },
            // 只允许聚焦到当前input
            onFocus(idx) {
                if (idx !== this.currentInput) {
                    this.$refs.inputs[this.currentInput].focus();
                }
            },
            // 退格时回到前一个输入框
            onBackspace(idx, e) {
                if (!this.password[idx] && idx > 0) {
                    this.currentInput = idx - 1;
                    this.$nextTick(() => {
                        this.$refs.inputs[this.currentInput].focus();
                    });
                }
            },
            formatDate(timestamp, type){
                return utils.formatDate(timestamp, type);
            },
            async openOrderDetail(row) {
                this.addrlsDialogVisible = false;
                console.log('當前訂單詳情',row)
                this.orderRow = row;
                this.timeoutAmount = this.orderRow.charge_json?.cs_insure || 0;
                this.lostAmount = this.orderRow.charge_json?.bj_insure || 0;
                this.orDetailDialogVisible = true;
                try {
                    let params = {
                        id: this.orderRow.id
                    }
                    let res = await axios.post('order_info',params);
                    console.log('res',res)
                    if(Number(res.data.code) === 0) {
                        this.orderInfo = res.data?.order || {};
                        this.addrInfo = res.data?.addr || this.addrInfo;
                        this.applInfo = res.data?.appl || {};
                        this.goodsList = res.data?.list || [];
                        this.ct = res.data?.ct || {};
                        if(res.data.oi) {
                            this.lostInfo = res.data.oi.find(item=> item.insure_id === 2);
                            this.timeoutInfo = res.data.oi.find(item=> item.insure_id == 1);
                        }
                        console.log(this.lostInfo,'丢失保价信息');
                    }else {
                        this.$message.error(res.data.msg || '获取订单详情失败');
                    }
                }catch(err) {
                    this.$message.error('获取订单详情失败，请稍后再试');
                }
            },
            openPayDialog(row) {
                console.log('openPayDialog - uNouse状态:', this.uNouse);
                console.log('openPayDialog - 订单信息:', row);
                console.log('openPayDialog - 支付金额:', row.actual_money || row.tb_money);
                console.log('openPayDialog - 用户余额:', this.uMoney);
                console.log('openPayDialog - 余额是否足够:', Number(row.actual_money || row.tb_money || 0) <= Number(this.uMoney));

                if(this.uNouse === 0){
                    // uNouse = 0: 需要输入密码，打开支付弹窗
                    console.log('需要密码支付，打开弹窗');
                    this.orderRow = row;
                    this.payDialogVisible = true;
                } else if(this.uNouse === 1) {
                    // uNouse = 1: 免密支付，直接调用支付接口
                    console.log('免密支付，直接支付');
                    this.payOrder(row);
                } else {
                    console.warn('未知的 uNouse 状态:', this.uNouse);
                    this.$message.error('支付状态异常，请刷新页面重试');
                }
            },

            // 通用焦点管理方法（解决 aria-hidden 警告）
            clearFocusForPopconfirm() {
                // 移除当前焦点
                if (document.activeElement && typeof document.activeElement.blur === 'function') {
                    document.activeElement.blur();
                }
                document.body.focus();
            },

            // 处理支付确认（解决 aria-hidden 警告）
            handlePayConfirm(row) {
                this.clearFocusForPopconfirm();

                // 使用 requestAnimationFrame 确保 DOM 更新完成
                requestAnimationFrame(() => {
                    // 调用 openPayDialog 来根据 uNouse 状态决定是否显示弹窗
                    this.openPayDialog(row);
                });
            },

            // 处理支付取消（解决 aria-hidden 警告）
            handlePayCancel() {
                this.clearFocusForPopconfirm();
            },

            // 支付订单
            async payOrder(row) {

                if (!row && !this.orderRow) {
                    this.$message.error('订单信息缺失，请重试');
                    return;
                }

                // 优先使用传入的 row，如果没有则使用 this.orderRow
                const orderData = row || this.orderRow;
                const orderId = orderData?.id;

                if (!orderId) {
                    console.error('payOrder - 无法获取订单ID');
                    this.$message.error('订单ID缺失，请重试');
                    return;
                }

                console.log('payOrder - 使用订单ID:', orderId);

                try {
                    let params = {};

                    if(this.uNouse === 0) {
                        // 需要密码支付
                        if (!this.uPassword) {
                            this.$message.error('请输入支付密码');
                            return;
                        }
                        params = {
                            id: orderId,
                            pwd: this.uPassword // 带上用户输入的密码
                        };
                    } else {
                        // 免密支付
                        params = {
                            id: orderId,
                        };
                        console.log('构建免密支付参数:', params);
                    }
                    
                    console.log('支付参数',params);
                    let res = await axios.post('order_pay', params);
                    if (Number(res.data.code) === 0) {
                        this.$message.success(res.data.msg);
                        this.password = ['', '', '', '', '', ''];
                        this.currentInput = 0;
                        this.uPassword = '';
                        this.payDialogVisible = false;
                        // 重新加载订单数据
                        this.initData();
                    } else {
                        this.$message.error(res.data.msg);
                        // 失败时也可以清空密码
                        this.password = ['', '', '', '', '', ''];
                        this.currentInput = 0;
                        this.uPassword = '';
                    }
                    
                } catch (err) {
                    this.$message.error('網絡錯誤，請稍後再試');
                    this.password = ['', '', '', '', '', ''];
                    this.currentInput = 0;
                    this.uPassword = '';
                }
            },

            openApplicant() {
                this.applicantDialogVisible = false;
                this.applicantListDialogVisible = true;
                this.getApplicantList();
            },

            async searchApplicant() {
                this.searchLoading = true;
                try {
                    // 如果搜索值为空，则获取全部数据
                    if (!this.searchValue.trim()) {
                        await this.getApplicantList();
                        return;
                    }

                    // 根据当前选中的申报人类型（个人/公司）调用不同的接口
                    const type = this.activeChange === '1' ? 0 : 1;

                    const res = await axios.post('searchApplicant', {
                        // keyword: this.searchValue.trim(),
                        // type: type
                    });

                    if (res.data.code === '0') {
                        // 根据类型更新对应的列表
                        if (type === 0) {
                            this.applicantList = res.data.data;
                        } else {
                            this.companyList = res.data.data;
                        }
                        this.searchLoading = false;
                    } else {
                        this.$message.error(res.data.msg || '查詢失敗');
                    }
                } catch (err) {
                    console.error('searchApplicant error:', err);
                    this.$message.error('網絡錯誤');
                }
            },

            selectAddress(item) {
                // 添加选中状态
                this.addrInfo = item;
                this.$nextTick(() => {
                    const items = document.querySelectorAll('.address-item');
                    items.forEach(el => {
                        if (el.dataset.id === item.id.toString()) {
                            el.classList.add('selected');
                        } else {
                            el.classList.remove('selected');
                        }
                    });
                });
                console.log(this.addrInfo, '地址信息');
            },

            openAddAddress() {
                this.addAddressDialogVisible = true;
                this.getRegionList();
                this.resetForm();
                // 只在表单初始化时重置验证状态
                this.$nextTick(() => {
                    if (this.$refs.addAddressForm) {
                        this.$refs.addAddressForm.resetFields();
                    }
                });
            },

            getOrderList() {
                let params = {
                    type: this.activeIndex,
                }
                axios.post('order', {
                    type: this.activeIndex,
                }).then(res => {
                    if(this.activeIndex === 0) {
                        this.transportList = res.data.data || [];
                    }else{
                        this.addServiceList = res.data.data || [];
                    }
                    console.log('res', res)
                })
            },

            getAddress() {
                // const applicantId = id ;
                axios.get('/index/user/addresses').then(res => {
                    if (res.data.code === '0') {
                        this.addressList = res.data.data.reverse();
                        console.log('地址列表', this.addressList);
                    } else {
                        this.$message.error(res.data.msg);
                    }
                }).catch(err => {
                    this.$message.error('網絡錯誤');
                });
            },

            async editAddr() {
                this.addrlsDialogVisible = true;
                await this.getAddress();
            },

            async submitAddAddress() {
                try {
                    // 先进行表单验证
                    const valid = await this.$refs.addAddressForm.validate();
                    if (valid) {
                        const selectedLabel = this.identityList.find(item => item.label === this.addAddressForm.label);
                        const identityLabel = selectedLabel ? selectedLabel.id : null;
                        const formAddress = {
                            type: 0,
                            isdef: this.addAddressForm.isDef,
                            city: this.addAddressForm.region[0],    // 取数组第一个值作为city
                            dist: this.addAddressForm.region[1], // 取数组第二个值作为district
                            detail: this.addAddressForm.address,
                            name: this.addAddressForm.name,
                            mobile: this.addAddressForm.mobile,
                            card: this.addAddressForm.idCard,
                            number: this.addAddressForm.fixedNumber,
                            postal: this.addAddressForm.postalCode,
                            label: identityLabel,
                            appl: this.userInfo.id,
                        }
                        console.log('formAddress', formAddress);
                        const res = await axios.post('/index/user/addAddress', formAddress);
                        if (res.data.code === '0') {
                            this.$message.success(res.data.msg);
                            await this.getAddress();
                            await this.resetForm();
                            this.addAddressDialogVisible = false;
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    }else{
                        return false;
                    }

                } catch (err) {
                    if (err && err.message && err.message.indexOf('validate') > -1) {
                        console.error(err.message, 'err.message');
                    } else {
                        this.$message.error('網絡錯誤');
                    }
                }
            },

            closeAddAddress() {
                this.addAddressDialogVisible = false;
                this.resetForm();
            },

            changeTabs(index) {
                this.activeIndex = index;
                this.resetInput();
                // this.getOrderList();
            },
            orderStatusChange() {
                console.log('Selected warehouse:', this.tranForm.payStatus);
                this.searchParcel();
            },
            async searchParcel() {
                let time = this.tranForm.begTime && this.tranForm.endTime
                    ? `${this.tranForm.begTime}-${this.tranForm.endTime}`
                    : '';
                let params = {
                    time,
                    status: this.tranForm.payStatus,
                    no: this.tranForm.orderNum,
                    price: this.tranForm.orderPrice,
                    type: this.activeIndex,
                };
                try {
                    let res = await axios.post('order',params);
                    if(Number(res.data.code === 0)){
                        if (this.activeIndex === 0) {
                            console.log('33333333333333');
                            
                            this.transportList = res.data.data || [];
                        } else {
                            this.addServiceList = res.data.data || [];
                        }
                    }else {
                        this.transportList = [];
                        this.addServiceList = [];
                        this.$message.error(res.data.msg || '搜索订单失败，请稍后再试');
                    }
                }catch(err) {
                    this.transportList = [];
                    this.addServiceList = [];
                    this.$message.error('搜索订单失败，请稍后再试');
                }
            },
            resetInput() {
                this.tranForm = {
                    orderNum: '',
                    payStatus: '',
                    orderPrice: '',
                    begTime: '',
                    endTime: ''
                };
                this.orderDate = '';
                this.searchParcel(); // Refresh the list with cleared filters
            },
            handleCurrentChange() {
                console.log('******')
            },
            handleSizeChange(){

            },
            changeArea() {},
            handleRadioInput(value) { },
            onDateChange() {
                console.log('orderDate',this.orderDate);
                if(this.orderDate && this.orderDate.length === 2) {

                    const startDate = new Date(this.orderDate[0]);
                    startDate.setHours(0, 0, 0, 0);
                    this.tranForm.begTime = startDate.getTime() / 1000;
                    
                    const endDate = new Date(this.orderDate[1]);
                    endDate.setHours(23, 59, 59, 0);
                    this.tranForm.endTime = endDate.getTime() / 1000;
                    
                    console.log('开始时间:', this.tranForm.begTime, '结束时间:', this.tranForm.endTime);
                    this.searchParcel();
                } else {
                    this.tranForm.begTime = '';
                    this.tranForm.endTime = '';
                }
            }

        }
    });
</script>