<?php



namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\sdk\Kerrytj;
use think\Config;
use think\Cookie;
use think\Hook;
use think\Validate;
use think\Db;
use app\common\sdk\Alibaba;
use app\common\sdk\KuaiDiHd;
use think\Exception;
use app\api\controller\LineWebhook;




/**
 * 会员中心
 */
class User extends Frontend
{
    protected $layout = '';
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];


    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
		if(!$this->auth->id){
			$this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
		}
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'), '/');
        }
        //监听注册登录退出的事件
        Hook::add('user_delete_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });

    }



    /**
     * 会员中心
     */
    public function index()
    {
        //$token = $this->request->server('HTTP_TOKEN', $this->request->request('token', \think\Cookie::get('token')));

        // // 生成订单
        // $order_id = "4612218842809424121";
        // $config = Config::get('site.alibaba');
        // $ali = new Alibaba($config);
        // $res = $ali->cancelOrder($order_id);
        // return $res;

        // $keyword = "帽子";
        // $page = "1";
        // $size = "10";


        // $config = Config::get('site.alibaba');
        // $ali = new Alibaba($config);
        // $res = $ali->keywordQuery($keyword, intval($page), intval($size));
        // return $res;

        

        // $id_list = [113,114];
        // $addservice_detail_rows = Db::name('addservice_detail')->where('pid', 'in', $id_list)->select();
        // $addservice_detail_rows2 = $this->query_records('addservice_detail', array('pid'=>array('in', $id_list)));
        // $addservice_detail_data = $this->list_to_group($id_list, $addservice_detail_rows, 'pid');



        // $orderid = 22;
        // $this->webhook_jy_order($orderid);


        // $lineid = "U6176584aa949da37b3185a174292b79f";
        // $line = new LineWebhook();
        // $res = $line->botIsFriend($lineid);
        // echo "</br>";
        // echo $res; 

        // $openid = "BBBMvkrT3sj-GEd1XRuJBIdvw";
        // $config = Config::get('site.alibaba');
        // $ali = new Alibaba($config);
        // $res = $ali->wangwangUrl($openid);
        // return $res;
        

        // $bln = ["***********"];
        // $kj = new Kerrytj();
        // $res = $kj->tracing($bln);
        // return $res;


        // $order = "4396458493631424121";
        // $config = Config::get('site.alibaba');
        // $ali = new Alibaba($config);
        // $res = $ali->getLogisticsTraceInfo($order);
        // return $res;


        return redirect('transport/myparcel');

    }

    public function addBank()  {
        if($this->request->isPost()){
            //  添加银行
            $user_id = $this->auth->id;
            $type = $this->request->post('type');
            $bank_id = $this->request->Post('bank');
            $bank_name = $this->request->Post('name');
            $bank_account = $this->request->Post('lastsix');

            $row = $this->query_record('user_bank', array('user_id'=>$user_id, 'account_six'=>$bank_account, 'is_state'=>'1'));
            if( $row ){
                $this->error('銀行卡已經存在');
            }

            $map = array('user_id'=>$user_id, 'is_state'=>1, 'status'=>'normal');
            $bank_count = $this->records_count('user_bank', $map);
            if( $bank_count < 3 ){
                $rule = [
                    'bank_id'       =>'require',
                    'account_name'     => 'require|length:1,20',
                    'account_six'     => 'number|length:6',
                ];
                $msg = [
                    'bank_id.require' => 'Bank required',
                    'account_name.require' => 'Bank card name required',
                    'account_name.length'  => 'Account name cannot exceed 20 characters',
                    'account_six.length'  => 'The last six digits of the bank card must be six',
                ];
                $data = [
                    'bank_id'       =>  $bank_id,
                    'account_name'  =>  $bank_name,
                    'account_six'   =>  $bank_account,
                    'user_id'       =>  $user_id,
                    'card_img'      =>  "",
                    'card_img_img'  =>  "",
                    'is_lock'       =>  '1',
                    'is_state'      =>  '1',
                    'createtime'    =>  time(),
                    'updatetime'    =>  time(),
                ];
                $validate = new Validate($rule, $msg);
                $result = $validate->check($data);
                if( !$result ){
                    return $this->error(__($validate->getError()));
                }
                Db::startTrans();
                $res = Db::name('user_bank')->insertGetId($data);
                if( !$res ){
                    Db::rollback();
                    return $this->error($this->auth->getError());
                }
                Db::commit();

                //// 后期需要添加提醒
                // $_content = $this->auth->username .'('. $this->auth->mobile .')'.'银行卡提交需要审核，请至后台进行审核';
				// 			$dinghorn = new \addons\dinghorn\Dinghorn();
				// 			$res      = $dinghorn->msgNotice('notice', ['content' => $_content], [
				// 				'dynamic_variable' => '111'
				// 			]);

                if($type){
                    return $this->success(__('Submit successfully'), url('daifu/select_bank'));
                }else{
                    return $this->success(__('Submit successfully'), url('account/index'));
                }
            }else{
                //return json_encode(array("code"=>'1', 'msg'=>'已到添加上限'), JSON_UNESCAPED_UNICODE);
                $this->error(__("已到添加上限"));
            }
        }

        $bankcategory = $this->query_records('bank', array());
        $bank = $this->list_to_tree($bankcategory);
        $type = $this->request->param("type");

        $this->view->assign('bank',$bank);
		$this->view->assign('type',$type);

        if( is_mobile() ){
            return $this->view->fetch();
        }else{
            return $this->view->fetch('add_bank');
        }
    }

    
    public function addAddress()  {
        if( $this->request->isPost() ){
            $id = $this->request->post('id');
            $address_name = $this->request->post('name');
			$address_mobile = $this->request->post('mobile');
			$type = $this->request->post('type');
			$city = $this->request->post('city');
            $dist = $this->request->post('dist');
			$detail = $this->request->post('detail');
            $card = $this->request->post('card');
            $number = $this->request->post('number');
            $postal = $this->request->post('postal');
            $label = $this->request->post('label');
            $isdef = $this->request->post('isdef');
            $appl_id = $this->request->post('appl');
            if( $isdef == 1 ){
                $this->update_record('address', array('user_id'=>$this->auth->id), array('is_def'=>0));
            }

            $data = array(
                'type'      =>$type,
                'city'      =>$city,
                'district'  =>$dist,
                'detail'    =>$detail,
                'name'      =>$address_name,
                'mobile'    =>$address_mobile,
                'card'      =>$card,
                'number'    =>$number,
                'postal'    =>$postal,
                'label'     =>$label,
                'user_id'   =>$this->auth->id,
                'appl_id'   =>$appl_id,
                'is_def'    =>$isdef,
                'createtime'  =>time(),
                'updatetime'  =>time()
            );

            $count = $this->records_count('address', array('user_id'=>$this->auth->id));
            if( $count >= 5 ){
                return json_encode(array("code"=>'1', 'msg'=>'地址最多只能添加5條'), JSON_UNESCAPED_UNICODE);
            }
            if( $id ){
                $res = $this->update_record('address', array('id'=>$id), $data);
            }else{
                $res = $this->getid_insert_record('address', $data);
            }
            if( $res ){
                return json_encode(array("code"=>'0', 'msg'=>'操作成功'), JSON_UNESCAPED_UNICODE);
            }else{
                return json_encode(array("code"=>'1', 'msg'=>'添加失敗'), JSON_UNESCAPED_UNICODE);
            }
        }

        $dict = $this->query_records('dict', array('status'=>0));

        $label_list = $this->list_to_list($dict, 'type', 'label');

        /*   pid==1,  代表只获取台湾区域       */
        $city_list = $this->query_records('city', array('pid'=>1));
        $dist_list = $this->query_records('district', array());

        $list = $this->list_to_group_byid($city_list, $dist_list, 'c_id');
        return json_encode(array("code"=>'0', 'label_list'=>$label_list, 'area_list'=>$list), JSON_UNESCAPED_UNICODE);

        // $this->view->assign('label_list',$label_list);
        // $this->view->assign('area_list',$list);
        // return $this->view->fetch('add_address');
    }

    
    /**
     * 显示所有收货人信息， 查询收货人信息
     * @return bool|string
     */
    public function addresses()  {
        if( $this->request->isPost() ){
            $sch =  $this->request->post('sch');
            $row = Db::name('address')
                            ->whereOr('name', $sch)
                            ->whereOr('mobile', $sch)
                            ->where('user_id', $this->auth->id)
                            ->find();
            
                            
            if( !$row ){
                return json_encode(array("code"=>'1', 'msg'=>'查詢失敗'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array("code"=>'0', 'data'=>$row), JSON_UNESCAPED_UNICODE);
        }

        $rows = Db::name('address')->alias('ar')
        ->join('city ct', 'ar.city=ct.id')
        ->join('district dt', 'ar.district=dt.id')
        ->join('dict di', 'ar.label=di.id')
        ->where("ar.user_id", $this->auth->id)
        ->field("ar.*, ct.name cname, dt.name dname, di.name lname")
        ->order("ar.id DESC")->select();

        return json_encode(array("code"=>'0', 'data'=>$rows), JSON_UNESCAPED_UNICODE);

        // echo "addr_list=" . json_encode($rows, JSON_UNESCAPED_UNICODE )  . "</br>";
        // return;
        //$this->view->assign('addr_list',$rows);
        //return $this->view->fetch('add_address');
    }


    /**
     * 独立网页    添加收件人
     * @return bool|string
     */
    public function addAddresses()  {
        $id = $this->request->param('id');
        if( $this->request->isPost() ){
            $data = array(
                'type'      =>$this->request->post('type'),
                'city'      =>$this->request->post('city'),
                'district'  =>$this->request->post('dist'),
                'detail'    =>$this->request->post('detail'),
                'name'      =>$this->request->post('name'),
                'mobile'    =>$this->request->post('mobile'),
                'card'      =>$this->request->post('card'),
                'number'    =>$this->request->post('number'),
                'postal'    =>$this->request->post('postal'),
                'label'     =>$this->request->post('label'),
                'user_id'   =>$this->auth->id,
                'appl_id'   =>$this->request->post('appl') ?? 0,
                'is_def'    =>$this->request->post('isdef'),
            );

            $count = $this->records_count('address', array('user_id'=>$this->auth->id));
            if( $count >= 5 ){
                return json_encode(array("code"=>'1', 'msg'=>'地址最多只能添加5條'), JSON_UNESCAPED_UNICODE);
            }
            if( $id ){
                $res = $this->update_record('address', array('id'=>$id), $data);
            }else{
                $row = $this->query_record('address', $data);
                if( $row ){
                    return json_encode(array("code"=>'1', 'msg'=>'添加重複數據'), JSON_UNESCAPED_UNICODE);
                }
                $data['createtime'] = time();
                $data['updatetime'] = time();
                $res = $this->getid_insert_record('address', $data);
            }
            if( $res ){
                return json_encode(array("code"=>'0', 'msg'=>'操作成功'), JSON_UNESCAPED_UNICODE);
            }else{
                return json_encode(array("code"=>'1', 'msg'=>'添加失敗'), JSON_UNESCAPED_UNICODE);
            }
        }

        $dict = $this->query_records('dict', array('status'=>0));
        $label_list = $this->list_to_list($dict, 'type', 'label');

        /*   pid==1,  代表只获取台湾区域       */
        $city_list = $this->query_records('city', array('pid'=>1));
        $dist_list = $this->query_records('district', array());

        $list = $this->list_to_group_byid($city_list, $dist_list, 'c_id');
        // return json_encode(array("code"=>'0', 'label_list'=>$label_list, 'area_list'=>$list), JSON_UNESCAPED_UNICODE);
        if( isset($id) ){
            $row = $this->query_record('address', array('id'=>$id));
            $this->view->assign('row',$row);
        }else{
            $this->view->assign('row',[]);
        }

        $this->view->assign('label_list',$label_list);
        $this->view->assign('area_list',$list);
        return $this->view->fetch('add_address');
    }



    public function deleteAddress(){
        if( $this->request->isPost() ){
            $param = $this->request->param();
            $add_ids = $param['ids'];
            $ids_str = implode(',', $add_ids);
            $url = $param['url'] ?? '/transport/recipient';
            $res_line = Db::name('address')->where('id', 'in', $ids_str)->delete();
            if($res_line > 0){
                $this->success('刪除成功', $url);
            }
            $this->error('刪除失敗');
        }
    }


    /**
     * 设置状态
     */
    public function noused(){
        if( $this->request->isPost() ){
            $type = $this->request->param('type');
            $res = $this->update_record('user', array('id'=>$this->auth->id), array('nouse'=>$type));
            if($res){
                return json_encode(array("code"=>'0', 'msg'=>'設置成功'), JSON_UNESCAPED_UNICODE);
            }
            return json_encode(array("code"=>'1', 'msg'=>'設置失敗'), JSON_UNESCAPED_UNICODE);
        }
    }


    public function payModify()  {
        if( $this->request->isPost() ){
            $code = $this->request->param('code');
            $mobile = $this->request->param('mobile');
            $pwd = $this->request->param('pwd');
            $pwdt = $this->request->param('pwdt');

            $rule = [
                'code'  => 'require|number|length:4',
                'pwd'  => 'require|number|length:6',
                'pwdt'  => 'require|number|length:6',
            ];

            $msg = [
                'code.require' => 'code can not be empty',
                'code.number' => 'code must be a number',
                'code.length'  => 'code must be 4 characters',
                'pwd.require' => 'code can not be empty',
                'pwd.number' => 'code must be a number',
                'pwd.length'  => 'code must be 6 characters',
                'pwdt.require' => 'code can not be empty',
                'pwdt.number' => 'code must be a number',
                'pwdt.length'  => 'code must be 6 characters',
            ];
            $data = [
                'code'  => $code,
                'pwd'  => $pwd,
                'pwdt'  => $pwdt,
            ];
            $validate = new Validate($rule, $msg);
            $result = $validate->check($data);
            if (!$result) {
                return $this->error(__($validate->getError()), null, ['token' => $this->request->token()]);
            }
            $res = $this->auth->payPwdSet($mobile, $code, $pwd, $pwdt);
            if($res){
                return json_encode(array("code"=>'0', 'msg'=>'設置成功'), JSON_UNESCAPED_UNICODE) ;
            }
            // return array("code"=>'1', 'msg'=>'設置失敗', 'err'=>$this->auth->getError());
            return json_encode(array("code"=>'1', 'msg'=>'設置失敗', 'err'=>$this->auth->getError()), JSON_UNESCAPED_UNICODE);
            // $this->error($this->auth->getError());
        }
    }

    public function getCode(){

		$type = $this->auth->getTypecode()?$this->auth->getTypecode():'2';
		$this->sendCode($type, 4);
	}


    public function setPass() {
            return $this->view->fetch('user/setPass');
    }



    public function get_all_address(){
        $list = Db::name('address')->alias('ad')
                ->join("city ct", "ad.city=ct.id")
                ->join("district dt","ad.district=dt.id")
                ->join("dict dc","ad.label=dc.id")
                ->where("ad.user_id", $this->auth->id)
                ->field("ad.id, ad.name, dc.name label, ad.mobile, ct.name city, dt.name district, ad.detail")
                ->order("ad.id desc")
                ->select();
        return array('code'=>0, 'list'=>$list);        
    }
    




}
