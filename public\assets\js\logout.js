// Logout functionality using axios and Element UI
(function() {
    'use strict';
    
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        // 监听logout按钮点击事件
        document.addEventListener('click', function(e) {
            if (e.target && e.target.id === 'logout-btn') {
                e.preventDefault();
                handleLogout(e.target);
            }
        });
    });
    
    // 处理logout逻辑
    function handleLogout(btn) {
        var url = btn.getAttribute('data-url');
        
        // 使用Element UI的MessageBox显示确认对话框
        if (typeof ELEMENT !== 'undefined' && ELEMENT.MessageBox) {
            ELEMENT.MessageBox.confirm(
                '确定要退出登录吗？', 
                '确认退出', 
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    center: true,
                    customClass: 'logout-confirm-dialog',
                    confirmButtonClass: 'el-button--danger'
                }
            ).then(function() {
                // 用户点击确定，执行退出操作
                performLogout(url);
            }).catch(function() {
                // 用户点击取消，不做任何操作
                console.log('用户取消退出');
            });
        } else {
            // 如果没有Element UI，使用原生confirm
            if (confirm('确定要退出登录吗？')) {
                performLogout(url);
            }
        }
    }
    
    // 执行退出操作
    function performLogout(url) {
        // 获取CSRF token
        var token = getCSRFToken();
        
        // 显示加载状态
        showLoading();
        
        // 使用axios发送请求
        if (typeof axios !== 'undefined') {
            // 创建表单数据
            var formData = new URLSearchParams();
            formData.append('__token__', token);
            
            axios.post(url, formData, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(function(response) {
                hideLoading();
                if (response.data.code === 1) {
                    // 成功退出
                    showSuccess(response.data.msg);
                    
                    // 延迟跳转
                    setTimeout(function() {
                        window.location.href = response.data.url || '/index/login/login';
                    }, 1500);
                } else {
                    // 退出失败
                    showError(response.data.msg || '退出失败');
                }
            })
            .catch(function(error) {
                hideLoading();
                console.error('Logout error:', error);
                showError('网络错误，请稍后重试');
            });
        } else {
            // 如果没有axios，使用fetch
            var formData = new URLSearchParams();
            formData.append('__token__', token);
            
            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                hideLoading();
                if (data.code === 1) {
                    showSuccess(data.msg);
                    setTimeout(function() {
                        window.location.href = data.url || '/index/login/login';
                    }, 1500);
                } else {
                    showError(data.msg || '退出失败');
                }
            })
            .catch(function(error) {
                hideLoading();
                console.error('Logout error:', error);
                showError('网络错误，请稍后重试');
            });
        }
    }
    
    // 显示加载状态
    function showLoading() {
        if (typeof ELEMENT !== 'undefined' && ELEMENT.Loading) {
            ELEMENT.Loading.service({
                lock: true,
                text: '正在退出登录...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
        }
    }
    
    // 隐藏加载状态
    function hideLoading() {
        if (typeof ELEMENT !== 'undefined' && ELEMENT.Loading) {
            ELEMENT.Loading.service().close();
        }
    }
    
    // 显示成功消息
    function showSuccess(message) {
        if (typeof ELEMENT !== 'undefined' && ELEMENT.Message) {
            ELEMENT.Message({
                message: message,
                type: 'success',
                duration: 2000,
                showClose: true,
                center: true
            });
        } else if (typeof Layer !== 'undefined') {
            Layer.msg(message, {icon: 1});
        } else {
            alert(message);
        }
    }
    
    // 显示错误消息
    function showError(message) {
        if (typeof ELEMENT !== 'undefined' && ELEMENT.Message) {
            ELEMENT.Message({
                message: message,
                type: 'error',
                duration: 3000,
                showClose: true,
                center: true
            });
        } else if (typeof Layer !== 'undefined') {
            Layer.msg(message, {icon: 2});
        } else {
            alert(message);
        }
    }
    
    // 获取CSRF token
    function getCSRFToken() {
        // 优先从隐藏的input字段获取（ThinkPHP的{:token()}生成）
        var inputToken = document.querySelector('input[name="__token__"]');
        if (inputToken && inputToken.value) {
            return inputToken.value;
        }
        
        // 尝试从meta标签获取
        var metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            return metaToken.getAttribute('content');
        }
        
        // 尝试从cookie获取
        var cookies = document.cookie.split(';');
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.indexOf('__token__=') === 0) {
                return cookie.substring('__token__='.length);
            }
        }
        
        // 如果都没有找到，尝试动态生成一个简单的token
        console.warn('CSRF token not found, using fallback');
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    
    // 暴露到全局
    window.LogoutHandler = {
        handleLogout: handleLogout,
        getCSRFToken: getCSRFToken,
        showSuccess: showSuccess,
        showError: showError
    };
})(); 