<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_title" data-rule="required" class="form-control" name="row[bank_title]" type="text" value="{$row.bank_title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account" data-rule="required" class="form-control" name="row[account]" type="text" value="{$row.account|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('In_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-in_account" data-rule="required" class="form-control" name="row[in_account]" type="text" value="{$row.in_account|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Version')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-version" data-rule="required" class="form-control" name="row[version]" type="text" value="{$row.version|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Date_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-date_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[date_time]" type="text" value="{$row.date_time}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Taken')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-taken" data-rule="required" class="form-control" step="0.01" name="row[taken]" type="number" value="{$row.taken|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Save')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-save" data-rule="required" class="form-control" step="0.01" name="row[save]" type="number" value="{$row.save|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-balance" data-rule="required" class="form-control" step="0.01" name="row[balance]" type="number" value="{$row.balance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Abstract')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-abstract" data-rule="required" class="form-control" name="row[abstract]" type="text" value="{$row.abstract|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" data-rule="required" class="form-control" name="row[remark]" type="text" value="{$row.remark|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remarks')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remarks" data-rule="required" class="form-control" name="row[remarks]" type="text" value="{$row.remarks|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Jorder_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-jorder_id" data-rule="required" data-source="jyorder/index" class="form-control selectpage" name="row[jorder_id]" type="text" value="{$row.jorder_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rorder_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rorder_id" data-rule="required" data-source="recharge/index" class="form-control selectpage" name="row[rorder_id]" type="text" value="{$row.rorder_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_normal')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_normal" data-rule="required" class="form-control" name="row[is_normal]" type="number" value="{$row.is_normal|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
