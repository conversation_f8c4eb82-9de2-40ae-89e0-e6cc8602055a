<?php



namespace app\index\controller;

use app\common\controller\Frontend;
use think\Config;
use app\api\controller\LineWebhook;
use think\Db;


/**
 * 工具类
 */
class Help extends Frontend
{
    protected $layout = '';
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    public function _initialize()
    {
        parent::_initialize();
        // $auth = $this->auth;
		// if(!$this->auth->id){
		// 	$this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
		// }
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'), '/');
        }
    }


    public  function index(){
        $type = $this->request->param('type','0');
        
        $list = $this->query_records('help', array('type'=>$type), '*', array('id'=>'desc'));
        $this->view->assign('list', $list);
        return $this->view->fetch();
    }


    public function details(){
		$id = request()->param('id');
		$map = array(
			'id' => $id,
		);
		$info = Db::name('help')->field('type,title,content')->where($map)->find();
		/*上一篇*/
		$up_info = Db::name('help')->field('id,type,title')
                                            ->where('id',$id-1)
                                            ->where('type', $info['type'])
                                            ->limit(1)
                                            ->find();
		/*下一篇*/
		$down_info = Db::name('help')->field('id,type,title')
                                            ->where('id',$id+1)
                                            ->where('type', $info['type'])
                                            ->limit(1)
                                            ->find();
		
        
        $this->view->assign('info',$info);
		$this->view->assign('up_info',$up_info);
		$this->view->assign('down_info',$down_info);
        return $this->view->fetch('content');
	}




    
}
