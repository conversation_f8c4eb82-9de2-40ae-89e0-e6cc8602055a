<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('No')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-no" data-rule="required" class="form-control" name="row[no]" type="text" value="{$row.no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pid" data-rule="required" class="form-control" name="row[pid]" type="number" value="{$row.pid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Aid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-aid" data-rule="required" class="form-control" name="row[aid]" type="number" value="{$row.aid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('At_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-at_id" data-rule="required" data-source="addservice/type/index" class="form-control selectpage" name="row[at_id]" type="text" value="{$row.at_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="{$row.status|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
