@charset "utf-8";
*{ box-sizing: border-box; }
input::-ms-clear,
input::-ms-reveal
{
display:none;
}
input::clear,
input::reveal
{
display:none;
}
div::-webkit-scrollbar {
    display: none;
}

div::scrollbar {
    display: none
}
body{font-family:"Microsoft YaHei","PingFang SC","Arial"; min-width:1200px; }
body.active{ overflow: hidden; }
body .container{ margin:0 auto; padding: 0; width: 1200px}
.col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9{
    padding: 0; margin: 0; border: 0;
}
.wrapper{ min-width:1200px;  }
.body{width: 100%;/* position: relative; */}

a:hover,.hover,a:focus{color:#00A1E9; text-decoration: none; outline: none}
.row{ margin: 0 !important; }
.btn,.form-control{border-radius:0;}
input{box-shadow:none !important;outline:none !important; -webkit-appearance: none;}
select{  -webkit-appearance: none; }
.no_border{border:none!important;}
ul,li,p,body,img{margin: 0;padding: 0;word-break: break-word;border: 0; }
html{  }
body{font-size: 14px;color: #333;position: relative;}
textarea{ resize: none; outline: none }
.transition5 {
    -moz-transition: all 0.5s ease;
    -webkit-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

.transition10 {
    -moz-transition: all 1s ease 0.2s;
    -webkit-transition: all 1s ease 0.2s;
    -o-transition: all 1s ease 0.2s;
    -ms-transition: all 1s ease 0.2s;
    transition: all 1s ease 0.2s;
}

.transition3 {
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    transition: all 0.3s ease;
}
.requird{ color: red!important }
/*字体大小*/
.f12{ font-size: 12px; }
.f13{ font-size: 13px; }
.f14{ font-size: 14px; }
.f16{ font-size: 16px; }
.f18{ font-size: 18px; }
.f20{ font-size: 20px; }
.f22{ font-size: 22px; }
.f24{ font-size: 24px; }

/*字体颜色*/
.color_fff{ color: #fff }
.color_333{ color: #333 }
.color_666{ color: #666 }
.color_999{ color: #999 }
.color_000{ color: #000 }
body .color_red{ color: #ef436d }


/*外边距*/
.margin_t10{ margin-top: 10px; }
.margin_t20{ margin-top: 20px; }
.margin_t30{ margin-top: 30px; }
.margin_t40{margin-top: 40px;}
.margin_b10{ margin-bottom: 10px; }
.margin_b20{ margin-bottom: 20px; }
.margin_b30{ margin-bottom: 30px; }
.margin_b40{ margin-bottom: 40px; }
.margin_l10{ margin-left: 10px; }
.margin_l20{ margin-left: 20px; }
.margin_l30{ margin-left: 30px; }
.margin_l40{ margin-left: 40px; }
.margin_r10{ margin-right: 10px; }
.margin_r20{ margin-right: 20px; }
.margin_r30{ margin-right: 30px; }
.margin_r40{ margin-right: 40px; }
.margin_lr10{ margin-right: 10px; margin-left: 10px; }
.margin_lr20{ margin-right: 20px; margin-left: 20px;}
.margin_lr30{ margin-right: 30px; margin-left: 30px;}
.margin_lr40{ margin-right: 40px; margin-left: 40px;}
.margin_tb40{ margin-top: 40px; margin-bottom: 40px;}
.margin_tb20{ margin-top: 20px; margin-bottom: 20px;}
/*内边距*/
.pad_t5{ padding-top: 5px; }
.pad_t10{ padding-top: 10px; }
.pad_t20{ padding-top: 20px; }
.pad_t30{ padding-top: 30px; }
.pad_t40{ padding-top: 40px; }

.pad_b10{ padding-bottom: 10px; }
.pad_b15{ padding-bottom: 15px; }
.pad_b20{ padding-bottom: 20px; }
.pad_b30{ padding-bottom: 30px; }
.pad_b40{ padding-bottom: 40px; }

.pad_l10{ padding-left: 10px; }
.pad_l20{ padding-left: 20px; }
.pad_l30{ padding-left: 30px; }
.pad_l40{ padding-left: 40px; }

.pad_r10{ padding-right: 10px; }
.pad_r20{ padding-right: 20px; }
.pad_r30{ padding-right: 30px; }
.pad_r40{ padding-right: 40px; }

.pad_lr10{ padding-right: 10px; padding-left: 10px; }
.pad_lr20{ padding-right: 20px; padding-left: 20px; }
.pad_lr30{ padding-right: 30px; padding-left: 30px; }
.pad_lr40{ padding-right: 40px; padding-left: 40px; }

.pad_tb10{ padding-top: 10px; padding-bottom: 10px; }
.pad_tb15{ padding-top: 15px; padding-bottom: 15px; }
.pad_tb20{ padding-top: 20px; padding-bottom: 20px; }
.pad_tb30{ padding-top: 30px; padding-bottom: 30px; }
.pad_tb40{ padding-top: 40px; padding-bottom: 40px; }

.bg_fff{ background: #fff; }
.ellipsis-2{overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; }
.ellipsis-3{ overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 3; }
.mark{ display: none; width: 100%; height: 100%; position: fixed; left: 0 ;top: 0; z-index: 150; background: rgba(0,0,0,0.3); cursor: pointer; }
.img_no{background: url(../../img/img_no.jpg); background-repeat: no-repeat; background-position: center center; background-size:cover;}
.img_src{ width: 100%; height: 100%; background-position: center center; background-size: cover }
.no_data{ text-align: center; padding: 30px 0;  font-size: 14px; color: #999}
.no_data img{ width:120px; margin-bottom: 10px; }

.over_hide{ overflow: hidden; }

.input_box{padding-top: 10px; position: relative; }
.input_box .input_tt{line-height: 40px;color: #333;font-size: 14px;float: left;width: 30%;padding-right: 15px;text-align: right;}
.input_box .input_rr{overflow: hidden;position: relative;}
.input_box .input_rr input,.input_box .input_rr select,.input_box .input_rr textarea{width: 100%;height: 50px;border-radius: 5px;outline: none;padding: 0 10px;border: 1px solid #ddd;background: #fff;}
.input_box .input_rr input.active,.input_box .input_rr select.active{ border-color: #419BFF; }
.input_box .input_rr input.error,.input_box .input_rr select.error{ border-color:#ef436d; }
.input_box .get_code,.input_box .get_code2{display: block;height:50px;color: #EF436D; position: absolute; line-height: 50px;font-size: 14px;z-index: 2;text-align: center; right: 20px;}
.input_box .get_code.disabled,.input_box .get_code2.disabled{  color: #666; }
.input_box.full_width .input_tt{ width: 100%; float: none; text-align: left; }
.input_box.full_width .input_rr{ width: 100%; }

body .input_box .input_rr input.date_blue{ background-image: url(../img/date_icon.png); background-repeat: no-repeat; background-position: 95% center }
.input_box .verifyimg{display: block;width:100px;height:40px;margin-left: -1px;z-index: 2; position: relative;}


.small_input .input_box .input_tt{ line-height:30px }
.small_input .input_box .input_rr input,.small_input .input_box .input_rr select,.small_input .input_box .input_rr textarea{ height: 34px }
.small_input .input_box .get_code,.small_input .input_box .get_code2{height:34px;line-height: 34px; font-size: 12px; right: 10px;}
.small_input .input_box .get_code.disabled,.small_input .input_box .get_code2.disabled{  color: #666; }
/*表格*/

.moni_check{ display: inline-block; width: 14px; height: 14px; background: url(../../img/pc/icon_select2.png); background-size: 100% 100%}
.moni_check.active{ background: url(../../img/pc/icon_select.png);  background-size: 100% 100%}
.moni_check input[type="checkbox"]{ width: 0; height: 0; opacity: 0}
.moni_check_label{ cursor: pointer; user-select: none }
.moni_check_label .moni_check{ position: relative; top: 2px; }
.moni_check_label .la_btn{ padding-left: 5px; }



.flex{ display: flex; }
.flex_ac_jc{ display: flex; align-items: center; justify-content: center; }
.flex_ac{ display: flex; align-items: center; }
.margin_a{ margin-left: auto }
.min_w{ min-width: 0}
.flex1{flex:1;}
.flex_d{flex-direction: column;}
.flex_s{flex-shrink: 0}
.flex_w{flex-wrap: wrap;}

/*页码*/

/*页码*/
.new_pages{ line-height: 28px; color: #333; font-size: 14px; padding: 30px 0 }
.new_pages input,.new_pages select{ outline: none; width: 54px; height: 30px; border: 1px solid #008bf7; border-radius: 3px; padding-left: 5px}
.new_pages a{ display: inline-block; margin: 0 3px; border: 1px solid #D3D3D3;background: #fff;line-height: 28px; border-radius: 2px; color: #6F6F6F; padding: 0 5px;min-width: 30px; text-align: center;}
.new_pages a.r_n{ border-color: #C9C9CA }
.new_pages a.go{ color: #fff; background: #008bf7 }
.new_pages a.s_e{ color: #999 }
.new_pages a.active{ border-color: #008bf7; background: #008bf7; color: #fff; }



.moni_select{ user-select: none; min-width:300px; height:40px;  border: 1px solid #ddd;   position: relative; z-index: 20  }
.moni_select.no_border{ border-color: rgba(0,0,0,0) }
.moni_select .moni_selected{height: 38px;line-height: 38px;color: #666;font-size: 14px;padding: 0 10px;cursor: pointer;}
.moni_select .moni_selected span{ display: block;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; }
.moni_select .moni_selected img.arr{float: right;margin-top: 17px;margin-left: 5px;}
.moni_select .moni_s_down{display: none;position: absolute;width: 100%;left: 0;top: 38px;padding: 10px 0;background: #fff;box-shadow: 0px 2px 3px rgba(0,0,0,0.1);max-height: 200px;overflow: auto;}
.moni_select .moni_s_down a{ display: block; padding: 0 10px; line-height: 30px; font-size: 13px; color: #656565; transition: 0.3s; -webkit-transition: 0.3s;}
.moni_select .moni_s_down a:hover{ background: #f3f3f3 }
.moni_select.active{ z-index: 30; }
.moni_select.active .moni_s_down{ display: block; }
.moni_select.active .moni_selected img.arr{ transform: rotate(180deg); -webkit-transform: rotate(180deg); }





body .html5_img .cnsr-button{ background: url(../img/up_load2.png) no-repeat; background-size: 100% 100%; }

body .html5_file .uploadify-button,body  .html5_file .cnsr-button,body  .html5_img .cnsr-button,body .upload-pre-item{ border: none;  overflow: hidden; }
body .delThis{ right: 0; top: 0; }
body .upload-pre-item{ height: 200px; }
body .html5_img.upload-area,body .html5_img .cnsr-button,body .upload-pre-item{ width: 200px; height: 200px; }
body .delThis{background: url(../img/del.png) no-repeat center center; background-size: 100% 100% }
body  .upload-pre-item{ background: #f9f9f9 }
body .html5_img .cnsr-button{ padding-top: 0 }
.up_img_divs{ border-radius: 10px; overflow: hidden; width: 200px; height: 200px; cursor: pointer; background: url(../img/up_load2.png) no-repeat; background-size: 100% 100%; margin-bottom: 10px; }
.up_img_divs img{ width: 100%; height: 100% }


.down_hover{ float: left; height: 34px; line-height: 34px; cursor: pointer; position: relative; }
.down_hover .down_hover_img{ margin-left: 5px;}
.down_hover.active{ z-index: 50 }
.down_hover.active .down_hover_img{ transform: rotate(180deg); -webkit-transform: rotate(180deg); }
.down_hover .down_hover_c{ display: none;  background: #fff; z-index: 30; position: absolute; box-shadow:0px 2px 25px rgba(0,0,0,0.1);  top: 64px; opacity: 0; transition: 0.3s; -webkit-transition: 0.3s; }
.down_hover .down_hover_c.active{ top: 34px; opacity: 1 }
.down_hover .down_hover_c.l{ left: 0; }
.down_hover .down_hover_c.r{ right: 0; }
.down_hover .down_hover_c.c{ width: 130px; left: 50%; margin-left: -65px;}


/*layer样式*/
/*body .layui-layer{ border-radius: 5px; }*/
body .layui-layer{ border-radius: 32px; }
body .layui-layer-dialog.my_confirm .layui-layer-content{ padding: 35px 20px;text-align: center;
    color: #333333;
    line-height: 24px;
    font-size: 22px;
  }
body .layui-layer-dialog.my_confirm .layui-layer-content p{ font-size:16px; color: #999999; padding: 0 30px; padding-top: 20px; line-height: 22px;  }
body .layui-layer-setwin .layui-layer-close2{ right: -5px; top: -5px; }
body .layui-layer-close2.layui-layer-ico{ background: url(../../img/pc/close2.png) center center no-repeat; }
body .layui-layer-close2.layui-layer-ico:hover{background: url(../../img/pc/close2.png) center center no-repeat;  }
body  .layui-layer-btn{ padding-bottom: 40px; }
body  .layui-layer-btn .layui-layer-btn0{display: inline-block;width: 176px;height: 50px;line-height: 50px;text-align: center;border-radius: 10px;color: #fff;font-size: 14px;background: #EF436D;border: none;margin-right: 20px;}

body  .layui-layer-btn .layui-layer-btn1{  display: inline-block; width: 176px; height: 50px;  line-height: 50px; text-align: center; border-radius: 10px; color: #666; font-size: 14px;
background: #f8f8f8; background-size: auto 100%;
border: none;
  }
body .win_class_no_hide{ max-height:85%!important;  }

.article_win.layui-layer-page .layui-layer-content{ padding: 0px 20px 0px 20px; }
.article_div .title{ font-size: 20px; line-height: 30px;  }
.article_div .content{ padding: 20px 0; color: #666; font-size: 14px; line-height: 24px; }
.article_div .content img{ max-width: 100%; height: auto!important }
.article_win .content{ max-height: 600px; overflow: auto }
.article_win .article_div{ padding-top: 20px; }

body .layui-layer-page .layui-layer-content  { overflow: visible; }
.my_close_win{height: 100%;overflow: auto;}
.my_close_win_title{ line-height: 50px; font-size: 16px; border-bottom: 1px solid #eee }
.my_close_win .title{/* padding:30px 0; */font-size: 18px;line-height: 70px;text-align: center;font-weight: bold;border-bottom: 1px solid #eee;}
.layui-layer-page .layui-layer-content .close_win_btn{display: block;width: 40px;height: 80px;text-align: center;line-height: 20px;position: absolute;left: 50%; margin-left: -20px; top:  -80px; z-index: 20;}


/*登录注册*/
.login_page{ width: 100%; height: 100%; position: fixed; left: 0; top: 0; }
.login_page .login_left{ width:29%; min-width: 400px; height: 100%; float: left; position: relative; background: url(../../img/pc/img_login_left.jpg) center center; background-size: cover  }
.login_page .login_left .login_logo img{ height: 40px; position: absolute; left: 20px; top: 20px;  }
.login_page .login_left .slogo{ width: 100%; position: absolute; left: 0; top: 50%; margin-top: -60px; }
.login_page .login_left .slogo .tt{  font-size: 36px; padding-bottom: 15px;}
.login_page .login_left .slogo .pp{ font-size: 18px; color: #fff; line-height: 24px;}
.login_page .login_left .f_slogo{ width: 100%; line-height: 54px; color: #fff; font-size: 14px; position: absolute; left: 0; bottom: 0 }
.login_page .login_right{height: 100%;position: relative;overflow: auto;}
.login_page .login_right .right_link{ line-height: 36px; position: absolute; right: 40px; top: 40px; }
.login_page .login_right .right_link a{ display: inline-block; width: 78px; border: 1px solid #EF436D; border-radius: 36px; text-align: center; margin-left: 20px; color: #EF436D }
.login_page .login_form{width: 500px;/* height: 410px; */position: absolute;left: 50%;margin-left: -250px;top: 50%;margin-top: -205px;}
.login_page .login_form .title{ font-size: 28px; padding-bottom: 30px }
.login_page .login_tips .forget{ color: #656565; transition: 0.3s; -webkit-transition: 0.3s; }
.login_page .login_tips .forget:hover{ color: #ef436d }

body .sub_btn{ display: inline-block; box-shadow: 1px 5px 8px rgba(0,0,0,0.06); min-width: 180px; padding: 0 20px; font-size: 18px; line-height: 64px; border-radius: 64px; color: #fff; background: #EF436D; text-align: center; }
body .sub_btn.disabled{ background: #ddd }
body .sub_btn.dark{ background: #ddd; color: #666; width: aut0; padding: 0 15px; }
body .sub_btn.small{ min-width: 100px; font-size: 14px; line-height: 34px; border-radius: 3px; }
/*注册*/
.login_page .login_form.login_form_re{ height: auto; position: relative; margin: 120px auto 100px auto; left: 0;  top: 0;  }

/*共用头部*/
.header{width: 100%;position: fixed;left: 0;background: #fff;top: 0;z-index: 50;}
.header .header_top{ padding-top: 8px; background: #fff; color: #333333; line-height: 28px; font-size: 12px; }
.header .header_top a{ color: #333; transition: 0.3s; -webkit-transition: 0.3s; margin-left: 40px; }
.header .header_top a:hover{ color: #EF436D }
.header .header_logo{ padding: 10px 0 10px 0; line-height: 40px; box-shadow: 0 3px 6px rgba(0,0,0,0.06) }
.header .header_logo .logo img{ height: 40px; float: left; margin-right: 50px; }
.header .header_logo .nav_link{display: block;color: #333333;position: relative;padding: 0 20px; margin-right: 20px; margin-top: 6px; line-height: 26px; border: 1px solid rgba(0,0,0,0); transition: 0.3s;-webkit-transition: 0.3s;float: left;font-size: 16px;}
.header .header_logo .nav_link.active{ color: #EF436D;border: 1px solid #EF436D ; background: rgba(239, 67, 109, 0.1); border-radius: 20px;}
.header .header_logo .nav_link:hover{color: #EF436D;}
.header .header_logo .nav_link.loginout_btn{ padding: 0; padding-left: 10px; font-size: 16px; float: right; }
.header .header_logo .u_img{ width: 40px; height: 40px; display: block; border-radius: 50%; overflow: hidden; float: right; }
.header .header_logo .u_img img{ width: 100%; height: 100%; float: left; }
.header .header_logo .nav_link_block{ padding: 0 17px; line-height: 28px; margin-right: 0; font-size: 16px; color: #fff;  border-radius: 36px; background: #EF436D; margin-left: 25px;  }
/* .header.no_bg{ background: none; }
.header.no_bg .header_logo{ box-shadow: none }
.header.no_bg .header_logo .nav_link{ color: #fff }
.header.no_bg .header_logo .nav_link_block{ color: #EF436D }
.header.no_bg  .header_logo .nav_link.active,.header.no_bg  .header_logo .nav_link:hover{ color: #EF436D  }
.header.no_bg .header_logo{background:url(../../img/pc/header_bg.png);} */
.header .header_logo .nav_link .quan_tip{display: none;position: absolute;left: 35px;top: 44px;background:#EF436D;font-size: 12px;color: #fff;/* height: 20px; */width: 150px;text-align: center;padding: 0 15px;border-radius: 20px;line-height: 20px;/* width: auto; */}
.header .header_logo .nav_link:hover .quan_tip{ display: block; }
.header .header_logo .nav_link .quan_tip img{ position: absolute; left: 18px; top: -10px; }
/*公用底部*/
.footer{height: 28px; background: #17191D; color: #fff; line-height: 28px; font-size: 12px; text-align: center;}


/*首页*/
.bottom_top_trans{
    transform: translateY(100px);
    -webkit-transform: translateY(100px);
    opacity: 0;
    transition: 2s;
    -webkit-transition: 2s;
}
.bottom_top_trans.active{
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
    opacity: 1;
}

.index_banner{height: 800px;position: relative;margin-top: -108px;}
.index_banner_mark{ background: rgba(0,0,0,0.7); height: 800px;color: #FFFFFF}
.index_banner_mark .title{ line-height: 80px; font-size: 56px; padding-top: 273px;}

.index_banner_mark .slogo_items{ padding-top: 54px; 
    transition: all 2s ease 0.3s;
    -webkit-transition: all 2s ease 0.3s;
 }
.index_banner_mark .slogo_items div{ display: inline-block; padding: 0 15px; line-height: 28px; color: #fff; font-size: 18px; }
.index_banner_mark .slogo_items div img{ margin-right: 5px; }
.index_banner .start_enter{ padding-top: 90px; transition: all 2s ease 0.6s;
    -webkit-transition: all 2s ease 0.6s;
    }
.pay_step{ margin-top:-40px;transform: scale(0.9) translateY(100px);
    -webkit-transform:scale(0.9) translateY(100px);
    opacity: 0;
    transition: 2s;
    -webkit-transition: 2s; }
.pay_step.active{ transform: scale(1) translateY(0px);
    -webkit-transform:scale(1) translateY(0px);
    opacity: 1; }
.pay_step .container{text-align: center; padding-bottom: 10px;  border-radius: 10px; background: #fff; overflow: hidden; position: relative; z-index: 10; box-shadow: 0 5px 8px rgba(0,0,0,0.07);}
.pay_step .title{ padding-top: 30px; line-height:80px; font-size:48px;  }
.pay_step .line{ width:100px; height: 4px; margin: 0 auto; background:  #EF436D }
.pay_step .pp{ padding: 15px 30px; line-height: 24px; }
.pay_step .items{ padding: 50px 30px }
.pay_step .items .item{ display:block; width:178px; float: left;}
.pay_step .items .item .tt{line-height: 24px;padding: 0 15px;padding-top:15px;height: 63px;}
.pay_step .items .step_arr{width:11px;float: left;margin-top: 33px;}

.charge_step{ margin-top: 120px; height: 600px; background: url(../../img/pc/index_banner_rolling.jpg) center center; background-size: cover; color: #fff  }
.charge_step .title{ padding-top: 60px; line-height:80px; font-size:48px;  }
.charge_step .line{ width:100px; height: 4px; margin: 0 auto; background:  #EF436D }
.charge_step .pp{ padding: 15px 30px; line-height: 24px; }
.charge_step .items{ padding-top: 160px }
.charge_step .item{ display: inline-block; color: #fff; font-size: 24px; }
.charge_step .item img{ margin: 0 25px; position: relative; top: -1px; }


.index_quanlist{ margin-top: 100px;  }
.index_quanlist .title{  line-height:80px; font-size:48px;  }

.quan_list{ margin-right: -40px; text-align: left }
.quan_list .box{ display: block; position: relative; padding: 20px; width: 580px; height: 220px; float: left; margin-right: 40px; border-radius: 10px; box-shadow: 0 5px 8px rgba(0,0,0,0.07); background: #fff; margin-bottom: 40px;}
.quan_list .box .img{width:180px;height: 180px;margin-right: 30px;display: block; position:relative;}
.quan_list .box .img .s_num{ line-height: 30px; background:rgba(0,0,0,0.7); color: #fff; padding: 0 15px; position: absolute; right: 0; bottom: 0; z-index: 5 }
.quan_list .box .text{ display: block; color: #333 }
.quan_list .box .time_d{ color: #999; font-size: 14px; padding-top: 10px; }
.quan_list .box .tt{ line-height: 30px; color: #333333; font-size: 18px;  }
.quan_list .box .price{ font-size: 32px; padding-top: 10px; }
.quan_list .box .o_price{ color: #999; text-decoration: line-through; font-size: 14px; font-weight: normal; }
.quan_list .box .price span{ font-size: 24px; }
.quan_list .info{width:231px;height: 71px;color: #fff;background:url(../../img/pc/quan_bg.png);background-size: 100% 100%;position: absolute;right: -10px;bottom: 20px;text-align: center;line-height: 80px;font-size: 18px;padding-left: 29px;}
.quan_list .buy_btns{position: absolute; right: 24px; bottom: 24px;}
.quan_list .buy_btns a{display: block; transition: 0.3s; width: 32px; height: 32px; overflow: hidden; background: rgba(239, 67, 109, 0.05); border-radius: 32px; margin-left: 16px; position: relative; float: left;}
.quan_list .bicon{width: 32px; height: 32px; position: absolute; text-align: center; line-height: 32px;}
.quan_list .bicon img{max-width: 18px; max-height: 18px; position: relative; top: -1px;}
.quan_list .bicon img.icon1{display: inline-block;}
.quan_list .bicon img.icon2{display:none;}
.quan_list .buy_btns a:hover .bicon img.icon1{display: none;}
.quan_list .buy_btns a:hover .bicon img.icon2{display: inline-block;}
.quan_list .h_text{width: 70px; height: 32px; line-height: 32px; color: #fff; position: absolute; left:  32px; top: 0 ;}
.quan_list .buy_btns a:hover{background: rgba(239, 67, 109, 1); width: 102px;}


.index_search{ padding:50px 0 100px 0  }
.index_search .s_input{ width: 600px; height: 64px; margin: 0 auto; position: relative; }
.index_search .s_input input{ width: 100%; border: 1px solid #EF436D; height: 62px; padding: 0 60px; border-radius: 62px; font-size: 18px; color:#EE436D; text-align: center; box-shadow: 0 5px 8px rgba(238,67,109,0.1)!important; }
.index_search .s_input input::-webkit-input-placeholder{
    color:#EF436D;
}
.index_search .s_input input::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color:#EF436D;
}
.index_search .s_input input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color:#EF436D;
}
.index_search .s_input input:-ms-input-placeholder{  /* Internet Explorer 10-11 */ 
    color:#EF436D;
}
.index_search .s_input a{ display: block; width: 60px; height: 60px; position: absolute; right: 8px; top: 2px; text-align: center; line-height: 60px;  }
.index_search .s_input a img{ position: relative; top: -1px; width: 28px; }
.index_foot{ background:#EF436D; color: #fff; padding:80px 0 100px 0; text-align: center;}
.index_foot .title{font-size: 48px;line-height: 64px;width: 766px;margin: 0 auto; padding-bottom: 40px;}
.index_foot .start_enter{ padding-top: 60px; }
.index_foot .start_enter .sub_btn{ background:#fff; color:#EF436D  }


/*优惠券*/
.coupon_search_banner{  width:800px; margin: 0 auto; padding-top: 100px; }
.coupon_search_banner input{ width:400px; height: 65px; border-radius: 65px; color: #333; padding: 0 15px;  text-align: center; background:#fff; border: none; margin-right: 10px; font-size: 18px;}
.coupon_search_banner a{ display: inline-block; width:180px; height: 65px; line-height: 65px; text-align: center; border-radius: 65px; background:#EF436D;  }

/*我的订单*/
.user_main{ background:#fff; border-radius: 3px; position: relative;}
.select_filter_div .filter_item{ float: left;padding: 0 13px; height: 38px; line-height: 38px; font-size: 16px; min-width: 10px; }
.select_filter_div .filter_item .moni_select{ padding: 0;  }
.select_filter_div .filter_item .moni_selected{font-size: 16px;  }

body .common_table tr th{ background:#F9F9F9; text-align: center; color: #333333; border-bottom: 1px solid #EDEDED; font-size: 14px; font-weight: normal; line-height: 34px; border-top: none }
body .common_table tr th.l{ text-align: left; }
body .common_table tr th.r{ text-align: right; }
body .common_table tr td{ border-top: none; border-bottom: 1px solid #EDEDED; line-height: 34px; }
.color_green{ color: #1BCBA9 }

.order_detail .order_title{ padding-top: 10px; line-height: 38px; font-size:16px; font-weight: bold; border-bottom: 2px solid #EF436D  }
body .common_table{border: 1px solid #EDEDED;overflow: hidden;}

/*公告*/
.user_art_left{ width: 227px; }
.user_main .user_main_line{ width: 1px; height: 100%; position: absolute; left: 227px; top: 0; background:#E9E9E9; }
.user_art_left .n_tt{ line-height: 64px; padding: 0 15px; cursor: pointer; }
.user_art_left .n_tt .icon{ float: left; margin-top: 22px; margin-right: 15px; }
.user_art_left .n_tt.active .arr{ transform: rotate(180deg); -webkit-transform: rotate(180deg);  }
.user_art_left .n_tt .arr{ margin-top: 29px; }
.user_art_left .n_tt_menu a{ display: block; padding: 0 20px 0 44px; color: #333; line-height: 50px;   }
.user_art_left .n_tt_menu a.active{ background: #FDEDF1; color: #EF436D;border-right: 2px solid #EF436D }
.user_main .user_art_title{ font-size: 16px; border-bottom: 1px solid #E9E9E9; line-height: 64px; padding: 0 30px; }
.user_art_list a{  display: block; border-bottom: 1px solid #E9E9E9; line-height: 50px; padding: 0 30px; color: #656565; transition: 0.3s; -webkit-transition: 0.3s; }
.user_art_list a:hover{ color: #EF436D }
.user_art_list a .tt img{ float: left; margin-right: 10px; margin-top: 18px }
/*我要申诉*/
.user_art_title .appeal_btn{ display: block; float: right; margin-top: 15px; line-height: 34px; background: #EF436D; color: #fff; font-size: 14px; border-radius: 3px; padding: 0 20px; }
.appeal_list .box{  display: block; border-bottom: 1px solid #E9E9E9; line-height: 24px; padding: 12px 30px; color: #666 }
.appeal_list .box .item{ padding: 2px 0 }
.appeal_list .box .item .img_btn{ display: inline-block; width: 24px; height: 24px; margin-right: 5px; text-align: center; line-height: 24px; }
.appeal_list .box .item .link_btn{ color: #666  }
.appeal_list .box .item .link_btn.color_red{ color:#EF436D  }
.appeal_list .box .item .link_btn:hover{ color:#EF436D  }
.appeal_list .box .item input{ width: 100%; border: none;  }



.moni_radio .radio_r{ display: inline-block; vertical-align: middle; position: relative; top: -1px; width: 18px; height: 18px; margin-right: 4px; background: url(../../img/pc/radio.png); background-size: 100% 100% }
.moni_radio.active .radio_r{ background: url(../../img/pc/radio_on.png); background-size: 100% 100% }
.appeal_form_div .moni_radio{ color: #333 }
.appeal_form_div .item{ line-height: 50px; }
.appeal_form_div .item textarea{ width: 100%; height: 80px; border: 1px solid #eee; border-radius: 3px; line-height: 24px; padding: 10px 15px; }
.appeal_form_div .item textarea.error{ border-color: #EF436D }
.appeal_form_div .sub_btn{ line-height: 44px; border-radius: 3px; }
/*会员中心首页*/
body .user_center_main{ width: 1720px; }
body .user_center_main .user_main{ background: none }
.border_box{ border-radius: 3px; background: #fff; }
.user_main_right{ width: 316px; margin-left: 20px; }
.user_info .avatar_div{ width: 64px; margin-right: 20px; cursor: pointer; }
.user_info .avatar{ width: 64px; height: 64px; border-radius: 50%; }
.user_info .avatar_div .vip{width: 53px;height: 16px;position: relative;line-height: 16px;font-weight: bold;color: #fff;z-index: 5;top: -8px;left: 5px;}
.user_info .avatar_div .vip img{ width: 100%; height: 100%; left: 0; top: 0 }
.user_info .avatar_div .vip span{ display: block; position: absolute; right: 4px; top: 0; z-index: 5; font-size: 12px; }
.user_info .nickname{ line-height: 36px; padding-top: 15px; font-size: 16px; color: #333333 }
.user_info .pro{ height: 30px; line-height: 30px; color: #999; font-size: 12px; }
.user_info .pro .pro_bar{ height: 4px; position: relative; background: #DDDDDD; margin-top: 13px }
.user_info .pro .pro_bar div{ height: 4px; background: #EF436D; }
.user_info .items .item{ line-height: 28px; }
.user_info .items .item .tt{ color: #999999; padding-right: 10px; }
.user_info .items .item.auth a{ display: inline-block; width: 28px; height: 28px; position: relative; top: -1px; vertical-align: middle; text-align: center; line-height: 28px; }



@media (max-width:1740px) {
    body .user_center_main{width:1500px;}
}
@media (max-width:1540px) {
    body .user_center_main{width:1300px;}
 }
@media (max-width:1340px) {
    body .user_center_main{width:1200px;}
 }

.user_info .items .item.auth a img.img1{ display: inline-block; }
.user_info .items .item.auth a img.img2{ display: none; }
.user_info .items .item.auth a.active img.img1{ display: none; }
.user_info .items .item.auth a.active img.img2{ display:inline-block ; }
.user_notice .title{line-height: 29px;font-size: 24px;color: #333333;}
.user_notice .title a{ color: #EE436D; font-size: 14px; float: right; }
.user_notice .u_notice a{ display: block; line-height: 32px; color: #333333; transition: 0.3s; -webkit-transition: 0.3s; }
.user_notice .u_notice a:hover{ color: #EE436D }

.user_notice  .moni_select{ border-radius: 3px; }
.u_gongneng{ margin-right: -20px; }
.u_gongneng a{ width: 128px; line-height: 38px; float: left; margin-top: 20px; margin-right: 20px; background: #FFF5F7; border-radius: 3px; text-align: center; color: #333; border: 1px solid #FFF5F7 }
.u_gongneng a.add{ border: 1px dashed #C8C8C8; background: none }
.u_head_notice{ line-height: 60px; }
.u_head_notice .more{ font-size:14px; color: #999999; transition: 0.3s; -webkit-transition: 0.3s; margin-left: 40px;  }
.u_head_notice .more:hover{ color:  #EE436D }
.u_head_notice .item{ width: 33.3%; float: left; max-width: 380px; font-size: 16px; color: #666;transition: 0.3s; -webkit-transition: 0.3s;  }
.u_head_notice .item:hover{ color: #EE436D }
.u_head_notice .item .icon{ background: #FCD9E2; border-radius: 3px; line-height: 20px; padding: 0 7px; color: #EE436D; font-size: 14px; margin-right: 6px;  }


.user_main_left_new{margin-right: 20px;}
.user_money .money_tt{ width: 100%  }
.user_money .money_chart{ height: 300px; }
.user_money .money_tt .tt{ font-size: 24px; color: #333333; line-height: 30px }
.user_money .money_tt .tt a{ font-size: 14px; color: #666; transition: 0.3s; -webkit-transition: 0.3s; }
.user_money .money_tt .tt a:hover{ color: #EE436D  }
.user_money .money_tt .item .tts{ padding-top: 10px; color: #656565; line-height: 24px; }
.user_money .money_tt .item .pp{ font-size: 24px; line-height: 32px; }
.user_money .money_tt .item .pp.color_red{ font-size: 32px; line-height: 42px; }
.user_money .money_tt .op_btns{ margin-right: -20px; }
.user_money .money_tt .op_btns a{ display: block; float: left; border-radius: 3px; box-shadow: 0 4px 8px rgba(239,67,109,0.2); width: 80px; line-height: 30px; background:#EE436D; color: #fff; text-align: center; margin-right:17px; }
.user_money .money_tt .mana_btn{ display: block; line-height: 42px; text-align: center; border: 1px solid #EF436D; border-radius: 3px; font-size: 16px; color: #EF436D }

.u_banner{ border-radius: 3px; overflow: hidden; }
/* banner -S */
.home_banner{position: relative;z-index: 0;width:100%;height:180px;margin:0 auto;overflow: hidden;}
.home_banner ul,.home_banner .banner_li{list-style:none;padding:0;}
.home_banner .imagelist{margin-bottom:0px; height: 100%; position: relative;}

.home_banner .imagelist ul,.home_banner .imagelist .banner_li{height:100%;}
.home_banner .imagelist>.banner_li{float:left;height:100%;overflow:hidden;width: 100%;}
.home_banner .imagelist .block{width:100%;height:180px;display:block;background:no-repeat center; background-size:cover; position: relative; }
.home_banner .imagelist .block video{width: 100%;height: 100%;position: absolute;left: 0;background: #000;top: 0;}
.home_banner .point{position: absolute;z-index: 40;width: 100%;bottom: 20px;left:0;text-align: right; padding-right: 20px }
.home_banner .point:hover{  }
.home_banner .point>li{ display: inline-block; *display: inline; *zoom:1; cursor: pointer;  border-radius: 4px; width:10px;height:4px;border:none;margin:0 2px; background:#fff; transition: 0.3s; -webkit-transition: 0.3s;}

.home_banner .point>li.on{ width: 20px; }
/*图片左右切换公共样式 -S*/
.home_banner .imageCheck span{position: absolute;z-index: 40;top:50%;display: block;width: 40px; opacity: 0; height:80px;margin-top:-40px;background-position: center;background-repeat:no-repeat; cursor: pointer;transition:0.3s;}

.home_banner .imageCheck .prev{left:0px;background-image: url(../img/banner_leftarrow.png); transition: 0.3s; -webkit-transition: 0.3s;}
.home_banner .imageCheck .next{right:0px;background-image: url(../img/banner_rightarrow.png);transition: 0.3s; -webkit-transition: 0.3s;}

.home_banner:hover .imageCheck .prev{ left: 30px; opacity: 1;   }
.home_banner:hover .imageCheck .next{ right: 30px; opacity: 1; }
.home_banner .imageCheck .prev:hover{ transform: scale(1,0.7); -webkit-transform: scale(1,0.7); }
.home_banner .imageCheck .next:hover{ transform: scale(1,0.7); -webkit-transform: scale(1,0.7); }


.user_nav .title{ font-size: 24px; color: #333333; line-height: 30px }
.user_nav a{ display: block; padding-top: 30px; color: #333; transition: 0.3s; -webkit-transition: 0.3s; width: 160px; height: 200px; text-align: center; float: left; margin: 0 32px 30px 32px; border-radius: 5px; box-shadow: 0 3px 7px rgba(0,0,0,0.1) }
.user_nav a .tt{ font-size: 18px; padding-top: 25px; }
.user_nav a:hover{  transform: translateY(-5px); -webkit-transform: translateY(-5px); }


/*认证*/
.edit_info_form .moni_select{ min-width: 10px; border-radius: 3px;  }
.edit_info_form .moni_select input{ width: 0; height: 0; opacity: 0; position: absolute; left: 0; top: 0 }
.id_card_img{  line-height: 135px; }
.id_card_img .item_s{ width: 50%; }
.id_card_img .tt{ line-height: 45px;  }
.id_card_img .img{width:280px;height: 200px; margin: 0 auto; border: 1px solid #eee;background: #F9F9F9;text-align: center;}
.id_card_img .img{ line-height: 1.5; color: #999999}
/* .id_card_img .img .up_icon{ margin-top: 35px; margin-bottom: 10px; } */
.id_card_img .img .up_icon{width: 100%;}
.id_card_img .img input{ width: 0; height: 0; opacity: 0 }
.id_card_img .img .img_src{ position: relative; }
.id_card_img .img .img_src a{display: block; width: 20px; height: 20px; position: absolute; right: 0; top: 0;}
.id_card_img .img .img_src a img{width: 100%;float: left;height: 100%;}
/*汇率*/
.win_rate_tab{ line-height: 50px; font-size: 16px; border-bottom: 1px solid #eee }
.win_rate_tab a{ color: #333333; display: inline-block; margin: 0 11px; }
.win_rate_tab a.active{ color: #EF436D }
.win_rate_tab a.active span{ width: 60px; height: 2px; background: #F8436F; display: block; margin:0 auto; margin-top: -2px; }
.rate_input{ line-height: 40px; }
.rate_input .tts{ width: 100px; text-align: center; background: #01A9EF; color: #fff; border-radius: 5px 0 0 5px; }
.rate_tt{ line-height: 32px; }
.rate_input .rate_input_r{ border: 1px solid #eee; border-left: none; border-radius: 0 5px 5px 0 }
.rate_input .rate_input_r input{ height: 38px; border: none; text-align: center; width: 100%; }
.rate_input.rate_input_red .tts{ background: #EF436D  }
.rate_change{ padding: 40px 0; text-align: center; }

/*分享*/
.share_list_win .share_ma{ width: 280px; }
.share_list_win .share_ma div{ color: #999; font-size: 16px; padding-top: 20px; line-height: 20px; }
.share_list_win .share_ma div a{ display: inline-block; margin-left: 10px;  padding: 0 10px; font-size: 12px; line-height: 20px; background:#EF436D ; color: #fff; border-radius: 3px;  }
.share_list_win .num_item{ width: 60%; float: left; color: #666; line-height: 60px; text-align: center; }
.share_list_win .num_item.num_item1{ width: 40% }
.share_list_win .num_item .color_red{  font-size: 24px; }
.share_list_win .common_table{ width: 100%; border: none }
.share_list_win .common_table tr td{ text-align: center; line-height: 36px; border-bottom: none }

/*账户管理*/
.account_mana .account_mana_r{ width: 320px; }
.account_mana .account_mana_r .border_box .title{ font-size: 16px; line-height: 32px; }
.account_mana .account_mana_r .rz_item{ line-height: 44px; }
.account_mana .account_mana_r .rz_item .icon{ float: left; width: 34px; height: 44px; line-height: 44px; text-align: center; margin-right: 5px;  }
.account_mana .account_mana_r .rz_item a{ color: #EF436D  }
.account_mana .account_mana_r .links{ display: block; width: 50%; float: left; line-height: 32px; color: #EF436D}
.account_mana .user_info{ border-bottom: 1px solid #eee }
.account_mana .account_box{border: 1px solid #dddd;border-top: 4px solid #ddd;width: 400px;float: left;margin-bottom: 20px;}
.account_mana .account_box .title{ line-height: 54px;  }
.account_mana .account_box .title img{ margin-right: 5px; position: relative; top: -1px; }
.account_mana .account_box .bank_b{ border: 1px solid #ddd; line-height: 36px; text-align: center; margin-bottom: 10px }
.account_mana .account_box .bank_b .icon{width: 160px;text-align: left;padding: 0 10px;line-height: 34px; }
.account_mana .account_box .bank_b .icon img{ max-width: 100%; max-height: 22px }
.account_mana .account_box .bank_b .name{ width: 80px; }
.account_mana .account_box .add_btn{ display: block; background: #F5F5F5; text-align: center; height: 38px; line-height: 38px; border-radius: 5px; margin-top: 10px; }
.account_mana .we_account .we_b{ width: 20%; float: left; text-align: center; padding-bottom: 10px }
.account_mana .we_account .we_b img{ width: 48px; height: 48px; border-radius: 50%; }
.account_mana .we_account .we_b div{ color: #333333;  line-height: 30px; padding-top: 10px; }
.account_mana .account_box .bank_b.ali .name{ width: 45%; text-align: left; padding-left: 15px }
.account_mana .account_box .we_account .we_b .add_btn{ width: 48px; height: 48px; line-height: 48px; border-radius: 50%; margin: 0; display: inline-block; }
.account_mana .account_box .we_account .we_b .add_btn img{ width: 24px; height: 24px; }


.account_mana .account_box .bank_b.company .name2{ width: 100%; text-align: left; line-height: 20px; margin-top: 10px; padding-left: 8px }
.account_mana .account_box .bank_b.company .name{padding-left: 8px;}
.account_mana .account_box .bank_icon{height: 16px; margin-top: 12px; margin-left: 12px;}

/*添加银行账户*/
.item_col_3{ width: 33.33%; float: left; }
.item_col_6{ width: 66.66%; float: left; }
/*添加微信*/
.add_wx_div img,.add_wx_div .img{ display:inline-block; width: 120px; height: 120px; margin-bottom: 10px;  }

/*充值*/
.charge_div .input_box{line-height: 34px; }
.charge_div .input_box .input_tt{ width: 180px; line-height: 34px; padding-right: 10px;  }
.charge_div .input_box input{width: 100px;height: 34px;border-radius: 3px;}
.charge_div .color_bg{ background: #FFF5F7 }
.charge_div .moni_select input{width: 0; height: 0; opacity: 0 }
.charge_div .moni_select{ min-width: 1px; border-radius: 3px; }
.charge_div .input_box.pass .input{ width: 187px; height: 34px; line-height: 32px; border: 1px solid #ddd; border-radius: 3px; }
.charge_div .input_box.pass input{/* width: 1px */}

.charge_infos{ border-top: 1px solid #eee; padding-bottom: 30px }
.charge_infos .title{ line-height: 54px; font-size: 16px; color: #EF436D; padding-top: 10px; }
.charge_infos .item .tt{ line-height: 30px; }
.charge_infos .item .pp{ font-size: 12px; line-height: 22px; color: #656565; padding-bottom: 10px }
.charge_div .bank_l .boxs{ display: block; color: #999999; font-size: 12px; width: 200px; line-height: 38px; border: 1px solid #ddd; background: #fff; border-radius: 3px; float: left; margin-right: 10px; }
.charge_div .bank_l .boxs .icon{float: left;width: 130px;text-align: left;padding: 0 5px;line-height: 35px;}
.charge_div .bank_l .boxs .icon img{ max-width: 120px; max-height: 20px; }
.charge_div .bank_l .boxs.active{ border-color: #EF436D }
/*资金管理*/
.capital_div .capital_left{ width: 344px; height: 300px; border-radius: 4px; background: #fff; box-shadow: 0 3px 7px rgba(0,0,0,0.06) }
.capital_div .capital_left .title{ line-height: 64px; font-size: 24px; color: #333333 }
.capital_div .capital_left .title .hide_capital img.img1{ display: inline-block; }
.capital_div .capital_left .title .hide_capital img.img2{ display: none; }
.capital_div .capital_left .title .hide_capital.active img.img1{ display:  none; }
.capital_div .capital_left .title .hide_capital.active img.img2{ display:inline-block; }


.capital_div .capital_left .item .tts{ padding-top: 10px; color: #656565; line-height: 24px; }
.capital_div .capital_left .item .pp{ font-size: 24px; line-height: 32px; }
.capital_div .capital_left .item .pp.color_red{ font-size: 32px; line-height: 42px; }
.capital_div .capital_left .op_btns{ margin-right: -20px; }
.capital_div .capital_left .op_btns a{ display: block; float: left; border-radius: 3px; box-shadow: 0 4px 8px rgba(239,67,109,0.2); width: 88px; line-height: 30px; background:#EE436D; color: #fff; text-align: center; margin-right:20px; }
.capital_div .capital_right{  height: 300px; border-radius: 4px; background: #fff; box-shadow: 0 3px 7px rgba(0,0,0,0.06) }
.capital_div .capital_right .title{ line-height: 64px; font-size: 24px; color: #333333 }


.capital_div .capital_list{ border-radius: 4px; background: #fff; box-shadow: 0 3px 7px rgba(0,0,0,0.06) }
.capital_div .capital_nav{ line-height: 30px; }
.capital_div .capital_nav .tt{ font-size: 24px; color: #333333; padding-right: 20px; }
.capital_div .capital_nav a{ display: inline-block; border-bottom: 2px solid #fff; margin: 0 15px; color: #333333; font-size: 16px; }
.capital_div .capital_nav a.active{ border-color: #EF436D; color: #EF436D }
.capital_list .common_table{ border: none }
.capital_list .common_table a.share_btn{line-height: 30px;padding: 0 10px;display: inline-block;border-radius: 4px;background: #F8436F;color: #fff;}


/*代付*/
.daifu_div .daifu_tip{ line-height: 22px; color: #656565; font-size: 12px; background: #FDEDF1; border-radius: 10px; }
.daifu_div .input_box .input_tt{min-width: 150px;line-height: 34px;width: auto;}
.daifu_div .input_box .input_rr{ line-height: 34px }
.daifu_div .input_box .input_rr input{ height: 34px; width: 300px; border-radius: 3px; }
.daifu_div .input_box .input_rr input.auto_w{ width: 100% }
.daifu_div .daifu_btn{ line-height: 32px; display: inline-block; border: 1px solid #EF436D; border-radius: 3px; color: #fff; padding: 0 20px; background: #EF436D }
.daifu_div .pay_type .daifu_btn{min-width: 200px;text-align: center;border-color:#ddd;color: #333333;background: none;background: #fff;}
.daifu_div .pay_type .daifu_btn.active{ border-color:#EF436D;  background:#EF436D; color: #fff  }
.daifu_div .pay_type .bank_l .boxs{ display: inline-block; float: none }
.daifu_div .input_box .input_rr .input_tt{ text-align: left }
.daifu_div .input_box .input_rr .input_rr input{ width: 230px; }
.daifu_div .input_box .fp_tips{line-height: 24px;color: #999;padding: 10px 0;width: 400px;}
.daifu_div .pay_type .sc_tips{ line-height: 24px; }

.daifu_div .rate_input{ width: 400px; float: left; }
.daifu_div .rate_change{width: 50px;float: left;padding: 8px 0;}
.ali_account .box{ display:block; float: left; width: 200px; line-height: 38px; border:1px solid #f5f5f5; background: #fff; text-align: center; color: #333; margin-right: 10px; margin-bottom: 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.06); padding: 0 15px; }
.ali_account .box img{ width: 20px; position: relative; top: -1px; }
.ali_account .box.active{ border-color: #EF436D }

.daifu_div .input_box .moni_select{ border-radius: 3px; height: 34px; line-height: 34px; }
.daifu_div .input_box .moni_select .moni_selected{ height: 34px; line-height: 34px;  }
.daifu_div .input_box .moni_select .moni_selected img.arr{  margin-top: 14px }
.daifu_div .input_box .moni_select .moni_s_down{ top: 34px }
.daifu_div .input_box .moni_select input{ width: 0; height: 0; opacity: 0 }
.daifu_div .input_box .input_rr.vis{ overflow: visible; float: left; }

.wxs_account a{ display: block; float: left; width:100px; height: 130px; border-radius: 10px; border: 1px solid #fff; box-shadow: 0 0 15px rgba(0,0,0,0.08); background: #fff; margin-right: 15px; text-align: center; margin-bottom: 15px; }
.wxs_account a .img{ width:48px; height: 48px; overflow: hidden; border-radius: 50%; margin:20px auto 10px auto;}
.wxs_account a .img img{ width: 100%; height: 100%; float: left; }
.wxs_account a .name{ color: #333; padding: 0 10px; font-size: 13px; }
.wxs_account a.active{ border-color: #EF436D }
.wxs_account a.add{ line-height: 130px }

.daifu_success .tips_text{ line-height: 22px;  }

.confirm_account_list .box{ display: block; float: left; margin: 0 10px 20px 10px; width: 300px; border: 1px solid #ccc; border-radius: 10px; padding: 15px 20px; }
.confirm_account_list .box.active{ border-color: #EF436D }
.confirm_account_list .box .item{ line-height: 32px; color: #666; font-size: 16px; }
.confirm_account_list .box .item span{ color: #333 }

.confirm_account_goods .box{ display: block; margin-bottom: 20px; padding-bottom: 15px; background: #fff; box-shadow: 0 5px 15px rgba(0,0,0,0.08); border-radius: 10px; border: 1px solid #fff; }
.confirm_account_goods .box.active{ border-color: #EF436D }
.confirm_account_goods .box .links{ border-bottom: 1px solid #eee }
.confirm_account_goods .box .tt{ line-height: 40px; font-size: 16px; color: #333333; }
.confirm_account_goods .box .item{ line-height: 30px; font-size: 14px; color: #656565; }
.confirm_account_goods .box .item .m{ font-size: 16px; }








/*图片上传 2018.7.10*/

.up_load_image .img_s{ display: block; width: 120px; height: 120px; border: 1px solid #ddd; float: left; margin-right: 10px; margin-bottom: 10px; overflow: hidden; position: relative; }
.up_load_image .img_s .img{width: 100%; float: left;}
.up_load_image .img_s a{ display: block; width: 20px; height: 20px; z-index: 5; transition: 0.1s; -webkit-transition: 0.1s; position: absolute; right: 0px; top: 0px; }
.up_load_image .img_s a:hover{opacity: 0.8 }
.up_load_image .img_s a img{ width: 100%; float: left; }
.up_load_image .img_s input[type="file"]{ display: none }
.up_load_image .img_s_pre input[type="file"]{ display: block; width: 100%; height: 100%; position: absolute; left: 0; top: 0; cursor: pointer; opacity:0; filter:alpha(opacity=0) }

.up_load_image .img_s .set_cover{ position: absolute; width: 100%; left: 0; bottom: 0; z-index: 10; background: #000; background: rgba(0,0,0,0.5); color: #fff; line-height: 30px; text-align: center;  cursor: pointer;}
.up_load_video .img_s_pre .l_tips{ width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 20; background: rgba(0,0,0,0.7); color: #fff; line-height: 118px; text-align: center; }
body .up_load_image.up_load_file .file_img_s{ width: 300px; line-height: 32px; height: 32px; padding-left: 10px; background: #f7f7f7 }
body .up_load_image.up_load_file .img_s_pre{ width: 300px; height: 36px; line-height: 36px; color: #333 }
body .up_load_image.up_load_file .img_s_pre .file_btn{ cursor: pointer;  }
body .up_load_image.up_load_video .img_s_video{ width:auto; height: 220px; }
body .up_load_image.up_load_video .img_s_video  video{ height: 220px; }
.up_load_video .img_s_pre .l_tips{ width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 20; background: rgba(0,0,0,0.7); color: #fff; line-height: 118px; text-align: center; }


/*图表*/
.chart_d{  background: #fff }
.chart_tt{ height: 40px; line-height: 40px; }
.chart_tt .o_detail{padding: 0 20px;color: #333333;font-size: 14px;display: block;float: left;border-radius: 3px;box-shadow: 0 4px 8px rgba(239,67,109,0.2);/* width: 88px; */line-height: 30px;background: #EE436D;color: #fff;text-align: center;margin-right: 20px;margin-top: 10px;}
.chart_tt .o_detail:hover{ color: #fff }
.chart_tt .tab_btn{display: block;width: 120px;height: 40px;line-height: 40px;text-align: center;float: left;font-size: 14px;color: #656565;}
.chart_tt .tab_btn.active{ color: #EF436D; background:#FEF6F8; }
.chart_tt .tab_btn.cur{ color: #EF436D }
.chart_d .items{ height: 75px; padding: 0 30px; }
.chart_d .items .item{ width: 33.33%; float: left; padding-top: 15px; }
.chart_d .items .item .pp{color: #656565; font-size: 14px;}
.chart_d .items .item .tt{ color: #333; font-size: 24px; line-height: 30px; padding-top: 5px; }
.chart_d .tabs_type{ height: 50px;line-height: 50px; color: #666; padding: 0 30px; font-size: 14px; }
.chart_d .tabs_type a{ display: inline-block; color: #656565; margin-right: 30px; }
.chart_d .tabs_type a span{ display: inline-block; width: 10px; height: 10px; margin-right: 5px; background: #C7C7C7; }
.chart_d .tabs_type a.active span{ background:#EF436D;  }
.chart_d .chart_div{padding: 0 30px;}
.chart_d .chart_div .chart_div_d{ height: 190px; }


/*资金管理*/
.capital_right .chart_d .chart_div{ padding: 0 }
.capital_right .btn_wrap{  line-height: 30px; float: left; }
.capital_right .btn_wrap a{ padding: 0 17px; border: 1px solid #ddd; color: #666; border-radius: 0;  display: block; float: left; border-right-width: 0px; position: relative;  }
.capital_right .btn_wrap a:first-of-type{ border-radius: 3px 0 0 3px; }
.capital_right .btn_wrap a:last-of-type{ border-radius: 0 3px 3px 0;  border-right-width: 1px }
.capital_right .btn_wrap a.active{ color:#EF436D; border-color: #EF436D ;  border-right-width: 1px; z-index: 5; margin-right: -1px; }


/*公用浮窗*/
.fixed_left{ width: 170px; transition: 0.3s; -webkit-transition: 0.3s; border-radius: 5px; position: fixed; right:-180px; bottom: 230px; background: #fff; border: 1px solid #EF436D; z-index: 50 }
.fixed_left.show{ right: 0 }
.fixed_left.active{ border-radius: 0 0 5px 5px; }
.fixed_left .icon{width: 48px;position: absolute;left: 50%;margin-left: -24px;top: -64px;}
.fixed_left .l_links{background: #fff;position: relative;z-index: 51;border-radius: 5px;overflow: hidden;}
.fixed_left .l_links a{display: block;padding: 6px 7px;line-height: 24px;color: #656565;}
.fixed_left .l_links a.active,.fixed_left .l_links a:hover{ background: #FDEDF1; color: #EF436D }
.fixed_left .n_links{padding: 10px 0;background: #EF436D;z-index: 51;position: relative;}
.fixed_left .n_links a{display: block;text-align: left;color: #fff;line-height: 40px;padding-left: 30px;}
.fixed_left .n_links a img{ margin-right: 5px; }
.fixed_left .n_links a.js_close_more img{transform: rotate(180deg);}
.fixed_left .fixed_text{transition: 0.3s;-webkit-transition: 0.3s;width: 500px;height: 100%;position: absolute;overflow: hidden;right: -500px;top: 0;background: #F9F9F9;border-radius: 0 5px 5px 0;z-index: 50;}
.fixed_left .fixed_text.active{ left: 169px; }
.fixed_left .fixed_text .box{ display: none; position: absolute; left: 0; top: 0; width: 100%; height: 100%; }
.fixed_left .fixed_text .box.active{ display: block; }
.fixed_left .fixed_text .tt{line-height: 50px;border-bottom: 1px solid #eee;padding: 0 20px;font-size: 16px;font-weight: bold;}
.fixed_left .fixed_text .tt img{ float: left; height: 15px; margin-top: 17px; }
.fixed_left .fixed_text .content{

line-height: 24px;

color: #999999;

font-size: 14px;

height: 130px;

margin: 10px 0;

overflow: scroll;
}


.fixed_left .fixed_text .m{ display: block; line-height: 54px; text-align: center; position: absolute; left: 0; width: 100%; bottom: 0; background: #F9F9F9; z-index: 51; color: #999 }
.fixed_left .fixed_text .m:hover{ color: #EF436D }
.fixed_left .fixed_text .m img{ margin-left: 5px; opacity: 0.2 }

.fixed_right{ width: 64px; background: #EF436D; position: fixed; right: 0; bottom: 230px; z-index: 50; text-align: center;transition: 0.3s; -webkit-transition: 0.3s; }
.fixed_right.active{ left: -64px; }
.fixed_right a{ display: block; height: 64px; line-height: 64px; transition: 0.3s }
.fixed_right a:hover{ background: #E41448 }
.fixed_right a img{ width: 30px; }



.new_fixed_right{ width: 75px;  position: fixed; right: 30px; bottom: 230px; z-index: 50; text-align: center;transition: 0.3s; -webkit-transition: 0.3s; }
.new_fixed_right.active{ right: -95px; }
.new_fixed_right a{ display: block; height: 75px; line-height: 75px; transition: 0.3s; background: #fff; border-radius: 50%; margin-bottom: 14px;box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);}
.new_fixed_right a:hover{ opacity: 0.8; }
.new_fixed_right a img{ width: 56px; }


/*新手引导*/
.img_tips_item{ display: none; width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 200;background: rgba(0,0,0,0.8); }
.img_tips_item.active{ display: none; }
.img_tips_item .tips_item{ position: absolute; left: 50%; margin-left: -256px; }
.img_tips_item .tips_item .img_arrow{ width: 50px; left: 0px; top: -60px; position: absolute; }
.img_tips_item .tips_item .jump_tips{ color: #fff; font-size: 14px; position: absolute; right: 0; top: -35px; line-height: 30px; text-decoration: underline; }
.img_tips_item .tips_item .tips_text{position: absolute;left: 70px;top: -82px;color: #fff;line-height: 24px;}
.img_tips_item .tips_item .tips_text a{ text-decoration: underline; margin-left: 5px; }

/*阿里巴巴  天猫*/
.img_tips_item.img_tips_item1 .tips_item{width: 541px;height: 50px;top: 262px;}
.img_tips_item.img_tips_item2 .tips_item{width: 459px;height: 50px;top: 318px;}
.img_tips_item.img_tips_item3 .tips_item{width: 427px;height: 50px;top: 360px;}
.img_tips_item.img_tips_item4 .tips_item{width: 431px;height: 50px;top: 416px;}
.img_tips_item.img_tips_item5 .tips_item{width: 645px;height: 50px;top: 564px;}
/*支付宝*/
.img_tips_item.img_tips_item6 .tips_item{width: 421px;height: 60px;top: 252px;margin-left: -362px;}
.img_tips_item.img_tips_item7 .tips_item{width: 431px;height: 60px;top: 345px;margin-left: -362px;}
.img_tips_item.img_tips_item8 .tips_item{width: 431px;height:80px;top: 450px;}

/*微信*/
.img_tips_item.img_tips_item9 .tips_item{width: 517px;height:155px;top: 343px;margin-left: -362px;}
/*其他*/
.img_tips_item.img_tips_item10 .tips_item{width: 847px;top: 347px;}
/*游戏*/
.img_tips_item.img_tips_item11 .tips_item{width: 847px;height: 107px;top: 354px;}


/*发票管理*/
.user_invoice{ width: 390px; }
.user_invoice .input_box .input_tt{  width: 88px; text-align: right; }
.user_invoice .input_box .input_rr input,.user_invoice .input_box .input_rr select { height: 40px; }
.user_invoice .img_div{ text-align: center; border: 1px solid #ddd; padding: 10px; position: relative; border-radius: 5px; cursor: pointer;}
.user_invoice .img_div input{ width: 0; height: 0; opacity: 0;  position: absolute; }
.user_invoice .img_div .up_icon{ margin-top:40px; margin-bottom: 10px; }
.user_invoice .img_div .pp{ color: #999; padding-bottom: 40px; }
.user_invoice .img_div .imgs{ width: 100% }
.user_invoice .img_div .js_re_up{ display: block; color: #EF436D; font-size: 12px; text-align: left; line-height: 30px; position: absolute; width: 80px; left: 320px; top: 50%; margin-top: -15px; }
.user_invoice .img_div .js_re_up img{ position: relative; top: -1px; margin-right: 5px; }


.invoice_list{ margin-right: -16px; }
.invoice_list .box{ width: 300px; height: 300px; position: relative; float: left; margin-right: 16px; margin-bottom: 16px; border: 1px solid #eee; border-top: 5px solid #eee}
.invoice_list .box .item{ padding-bottom: 8px; line-height: 24px; color: #333333; }
.invoice_list .box .item .tt{ color: #F8436F }
.invoice_list .box .con{ max-height: 200px; overflow: auto; }
.invoice_list .box .sub_btn{ position: absolute; left: 50%; margin-left: -50px;bottom: 20px; }

.tutorial_content{ line-height: 24px; color: #666; font-size: 14px; }
.tutorial_content img{ max-width: 100%; height: auto!important }


/* 2022.4.1 */
.goods_div .cover{width: 446px;}
.goods_div .cover img{width: 100%;}
.goods_div .tt{font-size: 18px; line-height: 26px; padding: 12px 0;}
.goods_div .p_item{line-height: 40px; padding:  0 10px; margin-top: 10px; color: #999999;}
.goods_div .p_item.bg{background: #F2F2F2;}
.goods_div .p_item .key{margin-right: 12px;}
.goods_div .p_item .zw{display: inline-block; width: 28px; height: 14px;}
.goods_div .p_item .red{color: #EF436D;}
.goods_div .p_item .text{color: #333;}
.goods_div .p_item .under_l{color: #999; text-decoration: line-through;}
.goods_div .sku_item{display: block; float: left; line-height: 38px; color: #666666; margin-right: 6px; margin-bottom: 6px; border: 1px solid  #DDDDDD;}
.goods_div .sku_item .i_img{width: 38px; height: 38px;}
.goods_div .sku_item.active{border-color: #EF436D; color:  #EF436D ;}
.goods_div .sku_s .sku_item{line-height: 32px; padding: 0 11px;}
.goods_div .sub_btn{line-height: 46px; border-radius: 0; min-width: 140px; margin-top: 40px; margin-left: 10px;}
.detail_tt{background: #F7F7F7; height: 38px;}
.detail_tt .tt{padding:0 24px; line-height: 38px; background:#EF436D; color: #fff; font-weight: bold;}
.detail_tt .sub_btn{width: 80px; min-width: 80px; line-height: 28px; font-size: 14px; margin-top: 5px; margin-right: 5px; padding: 0; border-radius: 0;}
.detail_content{width: 750px; margin: 0 auto; line-height: 24px;}
.detail_content img{max-width: 100%;}

.red{color: #EF436D;}
.address_t{font-weight: bold;}
.address_t a{color: #EF436D; font-weight: normal;}
.address_t a img{height: 12px; margin-right: 5px;}
.address_list .box{border: 1px solid #C8C8C8; padding: 12px	16px; position: relative; margin-bottom: 11px; padding-bottom: 26px;}
.address_list .box .tt{font-weight: bold; line-height: 30px;}
.address_list .box .pp{padding-left: 7px; line-height: 22px;  margin-top: 5px;}
.address_list .box .pp .t{width: 70px; color: #666;}
.address_list .box .pp .t span{display: inline-block;  width: 7px; height: 10px;}
.address_list .box .del{display: block; position: absolute;right: 10px; top: 10px;}
.address_list .box .del img{height: 12px;}
.address_list .box .edit_btn{color: #EF436D; position: absolute; right: 10px; bottom: 12px;}
.address_op_btn a{display: block; padding: 0 16px; border: 1px solid #EF436D; background: rgba(239, 67, 109, 0.1); line-height: 28px; color: #EF436D; border-radius: 2px;}


.user_new_left{width: 227px;}
.user_new_left .n_tt{ line-height: 64px; padding: 0 15px;}
.nav_click_item{height: 50px; padding-left: 45px; padding-right: 18px; user-select: none; cursor: pointer;}
.nav_click_item .icon{width: 20px; height: 20px;}
.nav_click_item .icon .img1{display: block;}
.nav_click_item .icon .img2{display: none;}
.nav_click_item.active{ color:#EF436D;}
.nav_click_item.active .icon .img1{display: none;}
.nav_click_item.active .icon .img2{display: block;}
.user_new_left .n_tt_menu{background: rgba(239, 67, 109, 0.03); display: none; }
.user_new_left .n_tt_menu.active{display: block;}
.user_new_left .n_tt_menu a{color: #333; transition: 0.3s; padding-left: 50px; border-right: 2px solid rgba(0,0,0,0); height: 50px;}
.user_new_left .n_tt_menu a img{height: 25px; margin-right: 10px;}
.user_new_left .n_tt_menu a.active,.user_new_left .n_tt_menu a:hover{background:rgba(239, 67, 109, 0.1) ;color: #EF436D; border-color: #EF436D; }

.daifu_div .num_pay_type .daifu_btn{min-width: 0;}
.daifu_div .num_pay_type .daifu_btn input{width: 80px; border: none;}
.daifu_div .num_pay_type .daifu_btn.active{background: none; color: #ef436d;}
.daifu_div .num_pay_type .d2{display: block;}
.daifu_div .num_pay_type .d1{display: none;}
.daifu_div .num_pay_type .daifu_btn.active .d2{display: none;}
.daifu_div .num_pay_type .daifu_btn.active .d1{display: block;}


.up_bank_img .item{width: 60px; height: 60px; border: 1px solid #C8C8C8; position: relative; margin-right: 10px; display: flex; align-items: center; justify-content: center;}
.up_bank_img .item .add_icon{width: 36px;height: 36px;}
.up_bank_img .item .close_icon{width: 20px; height: 20px; position: absolute; right: -10px; top: -10px;}
.up_bank_img .item .img{max-width: 58px;max-height: 58px;}
.up_bank_img .item.add{cursor: pointer;}
body .daifu_div .moni_select .moni_s_down a{line-height: 24px; padding: 5px 10px; border-bottom: 1px solid #f3f3f3;}




/*集运*/
.login_tips .a{ color: #333333; transition: 0.3s; cursor: pointer; user-select: none}
.login_tips .a a{ color: #EF436D }
.login_tips .a:hover{ color:#EF436D }
.login_tips .a img{ margin-right: 4px; position: relative; top: -1px; }
.block_div{ background: #fff; border-radius: 5px; }
.transportation_user_main{width: 1200px; margin: 0 auto;}
.transportation_div .left_d{ width: 200px; margin-right: 20px; }
.transportation_div .left_d .title{ line-height: 68px; color: #EF436D; font-size: 16px; font-weight: bold; }
.transportation_div .left_d .title img{ position: relative; top: -1px; margin-right: 10px; }
.transportation_div .left_d .list_d a{ display: block; line-height: 48px; transition: 0.3s; text-align: center; color: #656565 }
.transportation_div .left_d .list_d a:hover{ color: #EF436D; }
.transportation_div .left_d .list_d a.active{ color: #EF436D; background:#ffe1e1 }
.transportation_div .left_d .list_d a span{ line-height: 20px; font-size: 12px; color: #fff; border-radius: 20px; background: #FF4F4F; padding: 0 5px; }
.transportation_div .el-form-item{ margin-bottom: 10px; }

.logistics_div .box{ border-left: 1px dashed #EF436D; position: relative; padding-bottom: 30px; line-height: 24px;}
.logistics_div .box .time{ width: 120px; position: absolute; left: -140px; text-align: center; }
.logistics_div .box .r_d{ width: 48px; height: 48px;  margin-left: -24px; margin-right: 20px; float: left;}
.logistics_div .box .r_d img{ width: 100%; height: 100%; }
.logistics_div .box .over_hide{ padding-top: 12px; }
.logistics_div .box.active .over_hide{ color:#EF436D  }

.address_table.el-table th{ background: #ffe1e1 }

.trans_address_list .box{ width: 680px; margin-bottom: 10px; }
.trans_address_list .box .ops{ width: 120px; text-align: center; line-height: 40px; }
.trans_address_list .box .text{ border: 1px solid #ddd; transition: 0.3s; line-height: 38px; padding: 0 10px; cursor: pointer; }
.trans_address_list .box.active .text{ border-color: #EF436D; color: #EF436D }

.daifu_item{ line-height: 44px; font-size: 16px; }
.daifu_item .s_input{ width: 100px; height: 34px; border: 1px solid #ddd; margin: 0 5px; border-radius: 5px; padding: 0 10px; }


.base_bank_div{ padding: 25px; min-height: 640px;}
.base_bank_div .title{ font-size: 16px; font-weight: bold; line-height: 40px;}
.base_bank_div .pp{ width: 800px; line-height: 24px; font-size: 12px; color: #656565 }
.bank_list .box{ width: 190px; transition: 0.3s; float: left; margin-right: 10px; margin-bottom: 10px; display: block; color: #333; border: 1px solid #C8C8C8; padding: 10px 13px; height: 80px; }
.bank_list .box .tt{font-size: 14px; line-height: 24px;}
.bank_list .box .p{ text-align: right; padding-top: 20px;}
.bank_list .box img{ margin: 5px 0 5px 0 }
.bank_list .box.add:hover{ border-color: #00A3FF }
.bank_list .box.active{ border-color: #00A3FF }
.bank_list .box.active .tt,.bank_list .box.active .p{ color:  #00A3FF}

.transportation_div .bank_list .box{ cursor: pointer; }
.transportation_div .el-button--primary,.transportation_div .el-button--primary:focus,.transportation_div  .el-button--primary:hover{ background-color: #EF436D;border-color: #EF436D;;}

.helps_div .notice{line-height: 28px; max-width: 100%; border: 1px solid #EF436D; border-radius: 30px;  background: #fdf0f0; color: #656565;}
.helps_div .notice .tt{ float: left; margin-right: 10px; color: #EF436D; font-weight: bold; }
.hide_input{width:0px;height:0px;opacity:0}
.el-select-dropdown.el-popper{z-index: ********!important;}


.jiyun_op_btn.color_red{font-size: 12px; background: #EF436D; color: #fff; display: inline-block; padding: 5px 5px; border-radius: 5px; line-height: 1; color: #fff;}


.yhq_list{padding: 15px;}
.yhq_list .item{width: 120px;border: 1px solid #fff; border-radius: 10px; overflow: hidden; height: 116px; position: relative; color: #EF436D; line-height: 1; background: url(../../img/pc/new_index/yhq.png); background-size: 100% 100%; margin-right: 24px; margin-bottom: 24px;}
.yhq_list .item .t{height: 80px; display:flex; justify-content: center; align-items: center;}
.yhq_list .item .time{ font-size: 10px; line-height: 36px; text-align: center;}
.yhq_list .item .tt{display: flex; justify-content: center; align-items: flex-start;}
.yhq_list .item .tt span{font-size: 12px;position: relative; top: 2px;}
.yhq_list .item .tt b{font-size: 20px;}
.yhq_list .item .pp{font-size: 10px; margin-top: 5px;}
.yhq_list .item .icon_img{width: 40px; height: 40px; position: absolute; right: -3px; bottom: 32px; z-index: 2;}
.yhq_list .item .jjgq{background: #FFEA01; font-size: 8px; color: #333333;line-height: 1; padding-top: 20px; padding-bottom: 5px; transform: rotate(-45deg); width: 70px; text-align: center; position: absolute; left: -27px;top: -8px;}
.yhq_list .item.disabled{filter: grayscale(1);}
.yhq_list .item .xz_icon{width: 24px; height: 24px; position: absolute; right: 0; bottom: 0; display: none;}
.yhq_list .item.active .xz_icon{display: block;}
.yhq_list .item.active{border: 1px solid #EF436D;}