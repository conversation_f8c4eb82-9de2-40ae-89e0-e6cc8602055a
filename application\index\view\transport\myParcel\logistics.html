{include file="common/resources" /}
{load href="__CDN__/assets/css/login.css,__CDN__/assets/js/bottom_nav.js" /}
<div class="logistics-container" id="logistics-container">
    <!-- <h3 class="logistics-title">物流進度</h3> -->
    <div class="logistics-type">
        <img :src="logisticsIcon" alt="">
        <span>{{logisticsName}}</span>
        <span>{{waybill}}</span>
    </div>
    <div class="logistics-content">
        <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :icon="activity.icon"
              :type="index ===0 ? 'success' : ''"
              :color="activity.color"
              :size="activity.size"
              :timestamp="activity.timestamp">
              <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: center; gap: 8px;">
                <span class="fs13 c333 fw" :style="{ color: index === 0 ? '#0bbd87' : '' }" >{{activity.title}}</span>
                <span class="fs13 c666">{{activity.content}}</span>
            </div>
            </el-timeline-item>
          </el-timeline>
    </div>
</div>

<script>
        const app = new Vue({
            el: '#logistics-container',
            data() {
                return {
                    activities: [],
                    waybill: '',
                    wbname: '',
                };
            },
            mounted() {
                const url = new URL(window.location.href);
                const searchParams = new URLSearchParams(url.search);
                this.waybill = searchParams.get('waybill');
                this.wbname = searchParams.get('wbname');

                this.getTWLogistics();
            },
            computed:{
                logisticsName() {
                    return this.wbname === 'tcat' ? '黑貓' : this.wbname === 'hct' ? '新竹' : this.wbname === 'ktj' ? '大榮' : '';
                },
                logisticsIcon() {
                    return this.wbname === 'tcat' ? '__CDN__/assets/img/pc/hei_ma_cat.png' : this.wbname === 'hct' ? '__CDN__/assets/img/pc/xinzhu.png' : this.wbname === 'ktj' ? '__CDN__/assets/img/pc/da_rong.png' : '';
                }
            },
            methods:{
                async getTWLogistics(){
                    try {
                        if (!this.waybill || !this.wbname) return;
                        let params = {
                            name: this.wbname,
                            bill: this.waybill
                        }
                        let res = await axios.post('/index/transport/tw_logistics', params);
                        if(res.data.code == 0){
                            this.activities = res.data.data.map(item=>({
                                title: item.status,
                                content: item.context,
                                timestamp: item.date,
                            }));
                        }
                        
                    }catch(err){
                        console.error('台灣物流信息获取失败:',err);
                        
                    }
                },
            }
        })
</script>
<style>
    .logistics-container {
        width: 750px;
        min-height: 800px;
        margin: 24px auto;
        background-color: #ffffff;
        padding: 0 20px;
        box-sizing: border-box;
        border-radius: 8px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    }

    .logistics-title {
        text-align: center;
        font-weight: 500;
        font-size: 18px;
        margin-bottom: 20px;
    }

    .logistics-type {
        margin: 0 -20px;
        padding: 0 20px;
        width: auto;
        height: 52px;
        display: flex;
        align-items: center;
        gap: 8px;
        border-bottom: 1px solid #eaeaea;
    }

    .logistics-type img {
        width: 24px;
        height: 24px;
    }

    .logistics-content {
        margin-top: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    /* 手机端适配 */
    @media (max-width: 768px) {
        .logistics-container {
            width: 375px;
            margin: 0 auto;
            padding: 0 12px;
            border-radius: 0;
            min-height: auto;
            box-shadow: none;
        }

        .logistics-type {
            margin: 0 -12px;
            padding: 0 12px;
            height: 48px;
            gap: 6px;
        }

        .logistics-type img {
            width: 20px;
            height: 20px;
        }

        .logistics-type span {
            font-size: 14px;
        }

        .logistics-content {
            margin-top: 12px;
        }

        /* Element UI timeline 移动端适配 */
        .el-timeline-item__content {
            padding-left: 8px !important;
        }

        .el-timeline-item__timestamp {
            font-size: 12px !important;
        }
    }

    /* 超小屏幕适配 */
    @media (max-width: 414px) {
        .logistics-container {
            width: 100vw;
            max-width: none;
            margin: 0;
        }
    }
</style>