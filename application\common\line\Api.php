<?php

namespace app\common\line;

use app\common\exception\ApiException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;
use think\Env;

class Api
{
    const BASE_URI = "https://api.line.me";

    const API_PATH = [
        'token' => '/oauth2/v2.1/token',
        'verify_token' => '/oauth2/v2.1/verify',
    ];

    private Client $client;
    public function __construct()
    {
        $config = [
            'base_uri' => self::BASE_URI,
            'timeout' => 5,
            'verify' => false,
        ];
        $this->client = new Client($config);
    }

    private function config(): array
    {
        return config('site.line_config');
    }

    /**
     * 获取token
     * @param string $code
     * @return mixed
     * @throws ApiException
     * @throws GuzzleException
     */
    public function token(string $code, string $type)
    {
        $config = $this->config();
        if( $type != '' ){
            $redirect_uri = $config['redirect_uri'] . '?type=' . $type;
        }else{
            $redirect_uri = $config['redirect_uri'];
        }

        $resp = $this->client->post(self::API_PATH['token'], [
            'form_params' => [
                'grant_type' => 'authorization_code',
                'code' => $code,
                'redirect_uri' => $redirect_uri,
                'client_id' => $config['appid'],
                'client_secret' => $config['secret'],
            ],
        ]);
        return $this->generateResponse($resp, '获取token失败');
    }

    /**
     * 验证access_token
     * @param string $access_token
     * @return mixed
     * @throws ApiException
     * @throws GuzzleException
     */
    public function verifyToken(string $access_token)
    {
        $resp = $this->client->get(self::API_PATH['verify_token'], [
            'query' => ['access_token' => $access_token],
        ]);

        return $this->generateResponse($resp, '验证token失败');
    }

    /**
     * 验证id token
     * @param $id_token
     * @return mixed
     * @throws ApiException
     * @throws GuzzleException
     */
    public function verifyIdToken($id_token)
    {
        $config = $this->config();
        $resp = $this->client->post(self::API_PATH['verify_token'], [
            'form_params' => [
                'id_token' => $id_token,
                'client_id' => $config['appid'],
            ]
        ]);
        return $this->generateResponse($resp, '验证id token失败');
    }

    /**
     * 请求相应处理
     * @param ResponseInterface $respone
     * @param string $error
     * @return mixed
     * @throws ApiException
     */
    private function generateResponse(ResponseInterface $respone, string $error)
    {
        if ($respone->getStatusCode() !== 200) {
            throw new ApiException($error);
        }
        $data = $respone->getBody()->getContents();
        return json_decode($data, true);
    }

    /**
     * 刷新token
     * @param string $refresh_token
     * @return mixed
     * @throws ApiException
     * @throws GuzzleException
     */
    public function refreshToken(string $refresh_token)
    {
        $config = config('site.line_config');

        $resp = $this->client->post(self::API_PATH['token'], [
            'form_params'=> [
                'grant_type' => 'refresh_token',
                'refresh_token' => $refresh_token,
                'client_id' => $config['appid'],
                'client_secret' => $config['secret'],
            ]
        ]);
        return $this->generateResponse($resp, '验证token失败');
    }
}