

{include file="common/resources" /}
{include file="common/daigouresourses" /}

<!-- 添加CSRF token -->
{:token()}
<div style="position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<div class="transport-box" id="Application">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
      <div id="app" class="min-h-screen flex flex-col items-center py-12">
        <!-- Logo和标题 -->
        <div class="mb-8 text-center">
          <img src="__CDN__/assets/img/daigou/1688/daigou.jpeg" alt="阿里代购" class="mx-auto mb-2" style="width: 220px;">
        </div>
        <div style="margin-bottom: 20px;">
            <span class="text-pink-500 font-bold" style="margin-left: 450px;margin-bottom: 40px;">匯率：{{ ehg }}</span>
        </div>
        <!-- 搜索框 -->
        <div class="flex items-center w-full max-w-xl mb-8">
          <img src="__CDN__/assets/img/daigou/1688/1688.png" alt="阿里代购" class="h-8 mr-2">
          <input v-model="searchUrl" type="text" placeholder="请输入商品链接"
            class="flex-1 border border-gray-300 rounded px-4 py-2 mr-2 focus:outline-none focus:border-pink-400">
          <button @click="searchProduct"
            class="bg-pink-500 text-white px-6 py-2 rounded font-bold mr-2">确定</button>
        </div>
        <div class="loading-more" v-if="isLoading && !product"
            style="width: 100%;display: flex; flex-direction: column; align-items: center; justify-content: center;">
            <div><i class="el-icon-loading ml8 fs24 c999"></i></div>
            <div style="margin-left: 10px; color: #999999;">加載中</span>
            </div>
        </div>
        <!-- 商品信息卡片 -->
        <div v-if="product" class="w-full max-w-xl">
          <div @click="goToDetail(product.offerId)"
            class="flex items-center border rounded-lg p-4 bg-white shadow cursor-pointer hover:shadow-lg transition mb-2">
            <img :src="getImageProxyUrl(product.productImage.images[0])" class="w-16 h-16 object-cover rounded border mr-4" />
            <div class="flex-1">
              <a class="font-bold text-gray-800 mb-1" style="color:#ef436d">{{ product.subject }}</a>
              <div class="text-gray-500 text-xs mb-1">{{ product.shop }}</div>
              <div class="font-bold text-lg">{{product.productSkuInfos[0].cargoNumber|| product.productSkuInfos[0].skuAttributes[0].value}}￥{{ product.productSkuInfos[0].fenxiaoPriceInfo.offerPrice }}</div>
            </div>
            <div class="ml-4 text-green-500 font-bold">评分 {{ product.tradeScore }}</div>
          </div>
        </div>
        
      </div>
    </div>
</div>

<script>
    const app = new Vue({
        el: '#Application',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
                searchUrl: '',
                rate: 4.207,
                product: null,
                isLoading:false,
                ehg: <?php echo json_encode($ehg); ?>,

            },
            methods: {
                getImageProxyUrl(originalUrl) {
                    if (!originalUrl) return '';
                    // 使用图片代理
                    return `imageProxy?url=${encodeURIComponent(originalUrl)}`;
                },
                async searchProduct() {
                  let params = {
                    link: this.searchUrl,
                  }
                  this.isLoading = true
                  let res = await axios.post(`aliSearch`,params);
                        
                        if (res.data && Number(res.data.code) === 0) {
                          const data1 = JSON.parse(res.data.data);
                        console.log(data1,'res');
                        console.log(data1.result.result,'result');
                            this.product = data1.result.result || [];
                            this.isLoading = false

                            // 如果返回的数据少于请求的每页数量，表示没有更多数据了
                        } else {
                          console.log(123123)
                            this.$message.error(res.data?.msg || '获取商品数据失败');
                            this.products = [];
                        }
                // 这里应调用后端API获取商品信息，下面是模拟数据
                
                },
                async goToDetail(id) {
               
                  let params = {
                    offer: id,
                  }
                  let res = await axios.post(`aliGoods`,params);
                  console.log(res,'res');
                  setTimeout(() => {
                    window.location.href = `aliGoods?offer=${id}`;
                  }, 100);
                // 或者用 vue-router: this.$router.push({ path: '/product/detail', query: { id } })
                }
            }
    })
</script>