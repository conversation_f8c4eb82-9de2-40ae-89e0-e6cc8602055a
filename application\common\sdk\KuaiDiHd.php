<?php

namespace   app\common\sdk;
use think\Exception;


class KuaiDiHd{
    private $app_key;

    private $customer;

    // private $app_secret;
    // private $app_token;

    private $url_pre = "http://poll.kuaidi100.com";

    public function __construct($config) {
        $this->app_key = $config["key"];
        $this->customer = $config["customer"];
    }


    private function post_data($url, $params) {
        $http = curl_init($url);
        curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($http, CURLOPT_POST, 1);
        curl_setopt($http, CURLOPT_POSTFIELDS, $params);
        curl_setopt($http, CURLOPT_HEADER, 0);
        $result = curl_exec($http);
        curl_close($http);
        return $result;
    }


    public function query_do($com, $num, $phone='', $from='', $to='', $resultv2='1'){
        try{
            $url_path = "/poll/query.do";
            $param = array (
                'com'   => $com,          //快递公司编码
                'num'   => $num,   //快递单号
                'phone' => $phone,                //手机号
                'from'  => $from,                 //出发地城市
                'to'    => $to,                   //目的地城市
                'resultv2' => $resultv2             //开启行政区域解析
            );

            $post_data = array();
            $post_data["customer"] = $this->customer;
            $post_data["param"] = json_encode($param);
            $sign = md5($post_data["param"]. $this->app_key .$post_data["customer"]);
            $post_data["sign"] = strtoupper($sign);

            $url = $this->url_pre . $url_path;
            $params = "";
            foreach ($post_data as $k=>$v) {
                $params .= "$k=".urlencode($v)."&";                 //默认UTF-8编码格式
            }
            $post_data = substr($params, 0, -1);

            $result = $this->post_data($url, $post_data);
            return $result;
        }catch (Exception $e){
        }

    }
    
    





}