<?php

namespace app\api\controller;

use app\common\controller\Backend;
use app\common\controller\Api;
use think\Exception;
use think\Config;
use think\Db;

/**
 * 示例接口
 */
class Ji<PERSON> extends Api
{

    // 无需登录的接口,*表示全部
    protected $noNeedLogin = '*';
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = '*';

    
    protected function get_post_data(){
        $post_data = file_get_contents('php://input', 'r' );
        $post_data = trim( $post_data,chr(239).chr(187).chr(191) );
        //$post_data = stripslashes( $post_data );
        $data = json_decode( $post_data,true );
        return $data;
    }

    protected function http_post($url,$data,$cookie=''){

        $curl = curl_init();//初始化curl模块 
        curl_setopt($curl, CURLOPT_URL, $url);//登录提交的地址 
        curl_setopt($curl, CURLOPT_HEADER, 0);//是否显示头信息 
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);//是否自动显示返回的信息 
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);//绕过ssl验证
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        if(!empty($cookie)){
            curl_setopt($curl, CURLOPT_COOKIEFILE, $cookie); //设置Cookie信息保存在指定的文件中 
        }
        curl_setopt($curl, CURLOPT_POST, 1);//post方式提交 
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);//要提交的信息 
        $result = curl_exec($curl);
        curl_close($curl);
        $res = json_encode($result, JSON_UNESCAPED_UNICODE);
        return $res;
    }

    /*
    *   更新包裹信息                            ------->仓库对接API
    */
    public function update_pg(){
        $data = $this->get_post_data();
        if(empty($data['waybill']) ){
			return $this->error('参数提交错误','','1');
		}
        // 需要快递单号，包裹数量，重量， 材积((长 * 宽 * 高)/27000 长宽高都是按厘米计算), 入库时间
        $waybill = $data['waybill'];
        $num = $data['num'];
        $scale = $data['scale'];
        $volume = $data['volume'];
        $arrivetime = $data['time'];
        //$price = $data['price'];  //目前不知道会不会需要

        $row = Db::name('package')->where('waybill', $waybill)->find();
        if(!$row){
            return $this->error('没有找到对应单号信息','','1');
        }

        $res = Db::name('package')->where('waybill', $waybill)->update([
            'status'=>2,'num'=> $num, 'scale'=>$scale, 'volume'=>$volume,'arrivetime'=>$arrivetime
        ]);
        if($res){
            return $this->success('更新成功','','0');
        }
        return $this->error('更新失败','','1');
    }


    /**
     *  通知订单包裹可以出货                            ------->仓库对接API
     *  waybill 快递单号， code  1出货， 0拒收 或者其他状态
     */
    public function waybill_out(){
        $data = $this->get_post_data();
        $url = '';
        $map = array('waybill'=>$data['waybill'], 'code'=>$data['code']);
        $res = $this->http_post($url, json_encode($map,JSON_UNESCAPED_UNICODE));
        return $res;
    }

    /**
     * 更新订单状态  比如出货成功                            ------->仓库对接API
     */
    public function waybill_status(){
        $data = $this->get_post_data();
        if(empty($data['waybill']) ){
			return $this->error('参数提交错误','','1');
		}
        if($data['code'] == 0){
            if(Db::name('package')->where('waybill', $data['waybill'])->update(['status'=>4])){
                return $this->success('更新成功','','0');
            }
        }
        return $this->error('更新状态失败','','1');
    }    

    /**
     * 更新订单的物流情况                            ------->仓库对接API
     */
    public function waybill_flow(){
        $data = $this->get_post_data();
        if(empty($data['waybill']) ){
			return $this->error('参数提交错误','','1');
		}
        // 根据快递单号， 上传物流信息


    }


    // 纯玉山转账
    // public function save_bank(){
        
    //     $data = $this->get_post_data();
    //     if( empty($data) ){
    //         return $this->return_error('数据错误');
    //     }
    //     Db::startTrans();
    //     try{
    //         $bank_map  = [ 'bank_title' => $data['bk'], 'account' => $data['id'], 'date_time' => $data['date_time'], 'save' => $data['save'], 'balance' => $data['balance'] ];
    //         $bank_info = Db::name('bank_trade')->where($bank_map)->find();
    //         if($bank_info){
    //             return $this->return_error('重复数据');
    //         }

    //         if(intval($data['taken']) > 0 && intval($data['save']) <= 0){
    //             return $this->return_error('不重要的记录');
    //         }

    //         $payment = $this->remark_detail($data['remark']);
    //         if( !$payment ){
    //             return $this->error('remark为空');
    //         }

    //         $bank_data = array(
    //             'bank_title' => $data['bk'],
    //             'account' => $data['id'],
    //             'date_time' => $data['date_time'],
    //             'version' => $data['version'],
    //             'taken' => $data['taken'] ? $data['taken'] : '0.00',
    //             'save' => $data['save'],
    //             'balance' => $data['balance'],
    //             'abstract' => $data['abstract'],
    //             'remark' => $data['remark'],
    //             'in_account' => $payment['number'],
    //             'user_id' => '0',
    //             'order_id' => '0',
    //             'createtime' => time(),
    //             'updatetime' => time(),
    //         );

    //         if( !Db::name('sys_bank')->where('account_num', $data['id'])->find() ){
    //             return $this->return_error('收账银行账号错误');
    //         }

    //         $order_bank_matching_select = Db::name('order_bank_matching')->where('number', $payment['number'])->find();
            
    //         if( !$order_bank_matching_select ){
    //             $account_six = substr($payment['number'],-6);
    //             $order_bank_matching_count = Db::name('order_bank_matching')->where('number',$account_six)->count();
    //             if( $order_bank_matching_count <= 0 || $order_bank_matching_count > 1 ){
    //                 if($order_bank_matching_count <= 0){
    //                     $bank_data['remarks'] = '未找到订单-请检查全码';    
    //                 }else{
    //                     $bank_data['remarks'] = '后6码重复，需要客服使用永久关联';
    //                 }
    //                 $msg = $this->save_bank_water($bank_data);
    //                 Db::commit();
    //                 return $msg;
    //             }

    //             /*补充全码*/
    //             $order_bank_matching_select = Db::name('order_bank_matching')->where('number', $account_six)->find();
    //             if($order_bank_matching_count == 1){
    //                 $bank_data['remarks'] = '补充全码';
    //                 $msg = $this->bank_trade_check($bank_data, $payment,$order_bank_matching_select);
    //             }
    //         }

    //         /* 获取订单 */
    //         $order_map = [
    //             'id' => $order_bank_matching_select['order_id'],
    //             'order_status' => '0',
    //         ];
    //         $order = Db::name($order_bank_matching_select['order_table'])->where($order_map)->find();
    //         if( !$order ){
    //             $bank_data['remarks'] = '未查询到订单';
    //             $msg = $this->save_bank_water($bank_data);
    //             Db::commit();
    //             return $msg;
    //         }
    //         $money = intval($data['save'] + $order['rec_money']);
    //         $actual_money = intval($order['actual_money']);
    //         $user  = Db::name('user')->where('id', $order['user_id'])->find();
    //         $order_data['rec_money'] = $money;
    //         if( $money >= $actual_money ){
    //             $pay_status = 3;
    //             if( $money == $actual_money ){
    //                 $pay_status = 2;
    //             }
    //             $type = Config::get("site.ordertype");
    //             if( $type[$order_bank_matching_select['order_table']] == 1 ){
    //                 // 钱包充值
    //                 $bank_data['remarks'] = '成功';
    //                 $bank_data['user_id'] = $user['id'];
    //                 $bank_data['order_id'] = $order['id'];
    //                 $this->save_bank_water($bank_data);
    //                 $this->orderLog("银行入账：".$data['save'], $order['id'], $order_bank_matching_select['order_table']);
    //                 Db::name('order_bank_matching')->where('id',$order_bank_matching_select['id'])->delete();
                    
    //                 $order_data['pay_status'] = $pay_status;
    //                 $order_data['order_status'] = 1;
    //                 $order_data['completetime'] = time();
    //                 $this->money_log($user['id'], '2', $order['actual_money'], $order['id'], 'recharge');
    //                 if( $pay_status == 3 ){
    //                     $this->money_log($user['id'], '10', ($money - $actual_money), $order['id'], 'recharge', '超出部分转钱包');
    //                 }
    //                 $_df_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order['id'])->update($order_data);
                    
    //                 // 感觉充值不应该送积分， 充值送了，消费后又送就重复送了
    //                 //$this->order_processing($order['id'], $order_bank_matching_select['order_table']);
    //                 Db::commit();
    //                 return $this->return_success('成功');
    //             }else if( $type[$order_bank_matching_select['order_table']] == 2 ){
    //                 // 集运订单
    //                 $bank_data['remarks'] = '成功';
    //                 $bank_data['user_id'] = $user['id'];
    //                 $bank_data['order_id'] = $order['id'];
    //                 $this->save_bank_water($bank_data);
    //                 $this->orderLog("银行入账：".$data['save'], $order['id'], $order_bank_matching_select['order_table']);
    //                 Db::name('order_bank_matching')->where('id',$order_bank_matching_select['id'])->delete();

    //                 $order_data['pay_status'] = $pay_status;
    //                 $order_data['order_status'] = 1;
    //                 if( $pay_status == 3 ){
    //                     $this->money_log($user['id'], '10', ($money - $actual_money), $order['id'], 'jyorder', '超出部分转钱包');
    //                 }
    //                 $_df_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order['id'])->update($order_data);
    //                 Db::commit();
    //                 $this->return_success('成功');
    //             }
    //         }else{
    //             // 部分支付
    //             $order_update_map = [
    //                 'pay_type'  => 1,
    //                 'rec_money' =>$money
    //             ];
    //             $_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order_bank_matching_select['order_id'])->update($order_update_map);
    //             $this->orderLog('银行卡转入'.$data['save'], $order_bank_matching_select['order_id'],$order_bank_matching_select['order_table']);
    //             if( $_order_status ){
    //                 $bank_data['remarks'] = '成功2-部分支付';
    //                 $bank_data['user_id'] = $user['id'];
    //                 $bank_data['order_id'] = $order['id'];
    //                 $bank_data['order_table'] = $order_bank_matching_select['order_table'];
    //                 $msg = $this->save_bank_water($bank_data);
    //                 Db::commit();
    //                 return $msg;
    //             }else{
    //                 Db::rollback();
    //                 return $this->return_error('部分支付-操作失败');
    //             }
    //         }
    //     }catch (Exception $e) {
    //         Db::rollback();
    //         Db::name('debugging')->insert(['title' => 'save_bank', 'msg' => $e->getMessage(), 'createtime'=>time()]);
    //     }
    //     return $this->return_error('失败2');
    // }


    //  //目前玉山 + 信托
    // public function save_bank(){
        
    //     $data = $this->get_post_data();
    //     if( empty($data) ){
    //         return $this->return_error('数据错误');
    //     }
    //     $bank_map  = [ 'bank_title' => $data['bk'], 'account' => $data['id'], 'date_time' => $data['date_time'], 'save' => $data['save'], 'balance' => $data['balance'] ];
    //     $bank_info = Db::name('bank_trade')->where($bank_map)->find();
    //     if($bank_info){
    //         return $this->return_error('重复数据');
    //     }

    //     if(intval($data['taken']) > 0 && intval($data['save']) <= 0){
    //         return $this->return_error('不重要的记录');
    //     }

    //     if( $data['bk'] == "中國信託" ){
    //         $payment['number'] = $data['remark'];
    //     }else if( $data['bk'] == "玉山銀行" ){
    //         $payment = $this->remark_detail($data['remark']);
    //     }else{
    //         return $this->return_error('未识别的银行');
    //     }

    //     if( !$payment || strlen($payment['number']) <= 0  ){
    //         return $this->error('remark为空');
    //     }

    //     Db::startTrans();
    //     try{
    //         $bank_data = array(
    //             'bank_title' => $data['bk'],
    //             'account' => $data['id'],
    //             'date_time' => $data['date_time'],
    //             'version' => $data['version'],
    //             'taken' => $data['taken'] ? $data['taken'] : '0.00',
    //             'save' => $data['save'],
    //             'balance' => $data['balance'],
    //             'abstract' => $data['abstract'],
    //             'remark' => $data['remark'],
    //             'in_account' => $payment['number'],
    //             'user_id' => '0',
    //             'order_id' => '0',
    //             'createtime' => time(),
    //             'updatetime' => time(),
    //         );

    //         if( !Db::name('sys_bank')->where('account_num', $data['id'])->find() ){
    //             Db::rollback();
    //             return $this->return_error('收账银行账号错误');
    //         }

    //         if( $data['bk'] == "玉山銀行" ){
    //             $order_bank_matching_select = Db::name('order_bank_matching')->where('number', $payment['number'])->find();
    //             if( !$order_bank_matching_select ){
    //                 $account_six = substr($payment['number'],-6);
    //                 $order_bank_matching_count = Db::name('order_bank_matching')->where('number',$account_six)->count();
    //                 if( $order_bank_matching_count <= 0 || $order_bank_matching_count > 1 ){
    //                     if($order_bank_matching_count <= 0){
    //                         $bank_data['remarks'] = '未找到订单-请检查全码';    
    //                     }else{
    //                         $bank_data['remarks'] = '后6码重复，需要客服使用永久关联';
    //                     }
    //                     $msg = $this->save_bank_water($bank_data);
    //                     Db::commit();
    //                     return $msg;
    //                 }
    //                 /*补充全码*/
    //                 $order_bank_matching_select = Db::name('order_bank_matching')->where('number', $account_six)->find();
    //                 if($order_bank_matching_count == 1){
    //                     $bank_data['remarks'] = '补充全码';
    //                     $msg = $this->bank_trade_check($bank_data, $payment,$order_bank_matching_select);
    //                 }
    //             }
    //         }else{
    //             $six_str = substr($payment['number'], -6);
    //             $six_str = str_replace("*", "_", $six_str);
    //             $order_bank_matching_select = Db::name('order_bank_matching')->where('number', 'like', '%'.$six_str)->find();
    //             if( !$order_bank_matching_select ){
    //                 $bank_data['remarks'] = '未找到订单';
    //                 $msg = $this->save_bank_water($bank_data);
    //                 Db::commit();
    //                 return $msg;
    //             }
                
    //             $order_bank_matching_count = Db::name('order_bank_matching')->where('number', 'like', '%'.$six_str)->count();
    //             if( $order_bank_matching_count > 1 ){
    //                 $bank_data['remarks'] = '后6码重复，需要客服使用永久关联';
    //                 $msg = $this->save_bank_water($bank_data);
    //                 Db::commit();
    //                 return $msg;
    //             }
    //         }

    //         /* 获取订单 */
    //         $order_map = [
    //             'id' => $order_bank_matching_select['order_id'],
    //             'order_status' => '0',
    //         ];
    //         $order = Db::name($order_bank_matching_select['order_table'])->where($order_map)->find();
    //         if( !$order ){
    //             $bank_data['remarks'] = '未查询到订单';
    //             $msg = $this->save_bank_water($bank_data);
    //             Db::commit();
    //             return $msg;
    //         }
    //         $money = intval($data['save'] + $order['rec_money']);
    //         $actual_money = intval($order['actual_money']);
    //         $user  = Db::name('user')->where('id', $order['user_id'])->find();
    //         $order_data['rec_money'] = $money;
    //         if( $money >= $actual_money ){
    //             $pay_status = 3;
    //             if( $money == $actual_money ){
    //                 $pay_status = 2;
    //             }
    //             $type = Config::get("site.ordertype");
    //             if( $type[$order_bank_matching_select['order_table']] == 1 ){
    //                 // 钱包充值
    //                 $bank_data['remarks'] = '成功';
    //                 $bank_data['user_id'] = $user['id'];
    //                 $bank_data['order_id'] = $order['id'];
    //                 $this->save_bank_water($bank_data);
    //                 $this->orderLog("银行入账：".$data['save'], $order['id'], $order_bank_matching_select['order_table']);
    //                 Db::name('order_bank_matching')->where('id',$order_bank_matching_select['id'])->delete();
                    
    //                 $order_data['pay_status'] = $pay_status;
    //                 $order_data['order_status'] = 1;
    //                 $order_data['completetime'] = time();
    //                 $this->money_log($user['id'], '2', $order['actual_money'], $order['id'], 'recharge');
    //                 if( $pay_status == 3 ){
    //                     $this->money_log($user['id'], '10', ($money - $actual_money), $order['id'], 'recharge', '超出部分转钱包');
    //                 }
    //                 $_df_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order['id'])->update($order_data);
                    
    //                 // 感觉充值不应该送积分， 充值送了，消费后又送就重复送了
    //                 //$this->order_processing($order['id'], $order_bank_matching_select['order_table']);
    //                 Db::commit();
    //                 return $this->return_success('成功');
    //             }else if( $type[$order_bank_matching_select['order_table']] == 2 ){
    //                 // 集运订单
    //                 $bank_data['remarks'] = '成功';
    //                 $bank_data['user_id'] = $user['id'];
    //                 $bank_data['order_id'] = $order['id'];
    //                 $this->save_bank_water($bank_data);
    //                 $this->orderLog("银行入账：".$data['save'], $order['id'], $order_bank_matching_select['order_table']);
    //                 Db::name('order_bank_matching')->where('id',$order_bank_matching_select['id'])->delete();

    //                 $order_data['pay_status'] = $pay_status;
    //                 $order_data['order_status'] = 1;
    //                 if( $pay_status == 3 ){
    //                     $this->money_log($user['id'], '10', ($money - $actual_money), $order['id'], 'jyorder', '超出部分转钱包');
    //                 }
    //                 $_df_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order['id'])->update($order_data);
    //                 Db::commit();
    //                 $this->return_success('成功');
    //             }
    //         }else{
    //             // 部分支付
    //             $order_update_map = [
    //                 'pay_status'  => 1,
    //                 'rec_money' =>$money
    //             ];
    //             $_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order_bank_matching_select['order_id'])->update($order_update_map);
    //             $this->orderLog('银行卡转入'.$data['save'], $order_bank_matching_select['order_id'],$order_bank_matching_select['order_table']);
    //             if( $_order_status ){
    //                 $bank_data['remarks'] = '成功2-部分支付';
    //                 $bank_data['user_id'] = $user['id'];
    //                 $bank_data['order_id'] = $order['id'];
    //                 $bank_data['order_table'] = $order_bank_matching_select['order_table'];
    //                 $msg = $this->save_bank_water($bank_data);
    //                 Db::commit();
    //                 return $msg;
    //             }else{
    //                 Db::rollback();
    //                 return $this->return_error('部分支付-操作失败');
    //             }
    //         }
    //     }catch (Exception $e) {
    //         Db::rollback();
    //         Db::name('debugging')->insert(['title' => 'save_bank', 'msg' => $e->getMessage(), 'createtime'=>time()]);
    //     }
    //     return $this->return_error('失败2');
    // }


    /**
     *  银行与TWPAY通用
     */
    public function save_bank(){
        
        $data = $this->get_post_data();
        if( empty($data) ){
            return $this->return_error('数据错误');
        }

        $pay_tool = false;
        /* 判断是TWPAY还是银行 */
        if(mb_strpos($data['bk'], 'PAY') !== false){
            $pay_tool = true;
        }

        if($pay_tool){
            //  TWPAY 还没有测试过  ？？？？？？？？？
            $data['abstract'] = str_replace("-", ",", $data['abstract']);
            $abstract_arr = explode(",", $data['abstract']);;
            $zz_code = $abstract_arr[0];
            $pay_map = ['bank_title' => $data['bk'], 'account' => $data['id'], 'save' => $data['save']];
            $bank_info = Db::name('bank_trade')->where($pay_map)->where('FIND_IN_SET(:zz, abstract)', ['zz'=>$zz_code])->find();
        }else{
            $bank_map  = [ 'bank_title' => $data['bk'], 'account' => $data['id'], 'date_time' => $data['date_time'], 'save' => $data['save'], 'balance' => $data['balance'] ];
            $bank_info = Db::name('bank_trade')->where($bank_map)->find();
        }

        if($bank_info){
            return $this->return_error('重复数据');
        }

        if( !$pay_tool ){
            if(intval($data['taken']) > 0 && intval($data['save']) <= 0){
                return $this->return_error('不重要的记录');
            }
        }
        
        if( !$pay_tool ){
            if( $data['bk'] == "中國信託" ){
                $payment['number'] = $data['remark'];
            }else if( $data['bk'] == "玉山銀行" ){
                $payment = $this->remark_detail($data['remark']);
            }else{
                return $this->return_error('未识别的银行');
            }
        }else{
            $payment['number'] = $data['remark'];
        }

        if( !$payment || strlen($payment['number']) <= 0  ){
            return $this->return_error('remark为空');
        }

        Db::startTrans();
        try{
            $bank_data = array(
                'bank_title' => $data['bk'],
                'account' => $data['id'],
                'date_time' => $data['date_time'],
                'version' => $data['version'],
                'taken' => $data['taken'] ? $data['taken'] : '0.00',
                'save' => $data['save'],
                'balance' => $data['balance'],
                'abstract' => $data['abstract'],
                'remark' => $data['remark'],
                'in_account' => $payment['number'],
                'user_id' => '0',
                'order_id' => '0',
                'createtime' => time(),
                'updatetime' => time(),
            );

            if( !$pay_tool ){
                if( !Db::name('sys_bank')->where('account_num', $data['id'])->find() ){
                    Db::rollback();
                    return $this->return_error('收账银行账号错误');
                }
            }else{
                if( !Db::name('sys_twpay')->where('account_num', $data['id'])->find() ){
                    Db::rollback();
                    return $this->return_error('TWPAY账号错误');
                }
            }

            $six_str = substr($payment['number'],-6);
            if( $data['bk'] == "玉山銀行" ){
                $order_bank_matching_select = Db::name('order_bank_matching')->where('number', $six_str)->find();
                if( !$order_bank_matching_select ){
                    $bank_data['remarks'] = '未找到订单-请检查转账码1';
                    $msg = $this->save_bank_water($bank_data);
                    Db::commit();
                    return $msg;
                }
                
                $order_bank_matching_count = Db::name('order_bank_matching')->where('number', $six_str)->count();
                if( $order_bank_matching_count > 1 ){
                    $bank_data['remarks'] = '后6码重复，需要客服使用永久关联';
                    $msg = $this->save_bank_water($bank_data);
                    Db::commit();
                    return $msg;
                }
            }else{
                $six_str = str_replace("*", "_", $six_str);
                $order_bank_matching_select = Db::name('order_bank_matching')->where('number', 'like', '%'.$six_str)->find();
                if( !$order_bank_matching_select ){
                    $bank_data['remarks'] = '未找到订单-请检查转账码2';
                    $msg = $this->save_bank_water($bank_data);
                    Db::commit();
                    return $msg;
                }
                
                $order_bank_matching_count = Db::name('order_bank_matching')->where('number', 'like', '%'.$six_str)->count();
                if( $order_bank_matching_count > 1 ){
                    $bank_data['remarks'] = '后6码重复，需要客服使用永久关联';
                    $msg = $this->save_bank_water($bank_data);
                    Db::commit();
                    return $msg;
                }
            }

            /* 获取订单 */
            $order_map = [
                'id' => $order_bank_matching_select['order_id'],
                'order_status' => '0',
            ];
            $order = Db::name($order_bank_matching_select['order_table'])->where($order_map)->find();
            if( !$order ){
                $bank_data['remarks'] = '未查询到订单';
                $msg = $this->save_bank_water($bank_data);
                Db::commit();
                return $msg;
            }

            $money = intval($data['save'] + $order['rec_money']);
            $actual_money = intval($order['actual_money']);
            $user  = Db::name('user')->where('id', $order['user_id'])->find();
            $order_data['rec_money'] = $money;

            if( $money >= $actual_money ){
                $pay_status = 3;
                if( $money == $actual_money ){
                    $pay_status = 2;
                }
                $bank_data['remarks'] = '成功';
                $bank_data['user_id'] = $user['id'];
                $bank_data['order_id'] = $order['id'];
                $bank_data['order_table'] = $order_bank_matching_select['order_table'];
                $this->save_bank_water($bank_data);
                $this->orderLog("银行入账：".$data['save'], $order['id'], $order_bank_matching_select['order_table']);
                Db::name('order_bank_matching')->where('id',$order_bank_matching_select['id'])->delete();
                $type = Config::get("site.ordertype");

                $order_data['pay_status'] = $pay_status;
                $order_data['order_status'] = 1;
                
                if( $type[$order_bank_matching_select['order_table']] == 1 ){
                    // 钱包充值
                    $order_data['completetime'] = time();
                    $this->money_log($user['id'], '2', $order['actual_money'], $order['id'], 'recharge');
                    if( $pay_status == 3 ){
                        $this->money_log($user['id'], '10', ($money - $actual_money), $order['id'], 'recharge', '超出部分转钱包');
                    }
                    // 感觉充值不应该送积分， 充值送了，消费后又送就重复送了
                    //$this->order_processing($order['id'], $order_bank_matching_select['order_table']);
                }else if( $type[$order_bank_matching_select['order_table']] == 2 ){
                    // 集运订单
                    if( $pay_status == 3 ){
                        $this->money_log($user['id'], '10', ($money - $actual_money), $order['id'], 'jyorder', '超出部分转钱包');
                    }
                }
                $_df_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order['id'])->update($order_data);
                Db::commit();
                return $this->return_success('成功');
            }else{
                // 部分支付
                $order_update_map = [
                    'pay_status'  => 1,
                    'rec_money' =>$money
                ];
                $_order_status = Db::name($order_bank_matching_select['order_table'])->where('id', $order_bank_matching_select['order_id'])->update($order_update_map);
                $this->orderLog('银行卡转入'.$data['save'], $order_bank_matching_select['order_id'],$order_bank_matching_select['order_table']);
                if( $_order_status ){
                    $bank_data['remarks'] = '成功2-部分支付';
                    $bank_data['user_id'] = $user['id'];
                    $bank_data['order_id'] = $order['id'];
                    $bank_data['order_table'] = $order_bank_matching_select['order_table'];
                    $msg = $this->save_bank_water($bank_data);
                    Db::commit();
                    return $msg;
                }else{
                    Db::rollback();
                    return $this->return_error('部分支付-操作失败');
                }
            }
        }catch (Exception $e) {
            Db::rollback();
            Db::name('debugging')->insert(['title' => 'save_bank', 'msg' => $e->getMessage(), 'createtime'=>time()]);
        }
        return $this->return_error('失败2');
    }


    private function orderLog($msg, $order_id, $table='df_order'){
        $data = array(
            'order_id' => $order_id,
            'msg' => $msg,
            'createtime' => time(),
            'updatetime' => time(),
        );
        /*获取年份*/
        $y = date('Y');
        $table = $table.'_log_'.$y;
        /*检查表是否存在*/
        $t_table = 't_'.$table;
        if(count(db()->query('SHOW TABLES LIKE '."'".$t_table."'"))){
            /*存在*/
            Db::name($table)->insert($data);
        }else{
            /*不存在*/
            $sql = 'CREATE TABLE if not exists '.$t_table.' LIKE t_recharge_log';
            Db::query($sql);
            Db::name($table)->insert($data);
        }
    }

    private function bank_trade_check($data, $card, $order_bank_matching){
        $data_bank = [
            'username'=>$card['username'],
            'code'=>$card['code'],
            'number'=>$card['number'],
            'user_id'=>$order_bank_matching['user_id'],
            'bank_id'=>$order_bank_matching['bank_id']
        ];
        if(Db::name('bank_full_number')->insert($data_bank)){
            return $this->return_success(null, $data['remarks']);
        }else{
            return $this->return_error($data['remarks']);
        }
    }

    private function save_bank_water($data){
        if(Db::name('bank_trade')->insert($data)){
            return $this->return_success(null, $data['remarks']);
        }else{
            return $this->return_error($data['remarks']);
        }
    }

    private function remark_detail($remark){
        $remark_array = null;
        $reg = "/(\D+)/";
        preg_match($reg,$remark,$username);
        if($username[1] == '/'){
            $_username = ' ';
        }else{
            $_username = $username[1];
        }
        $remark_sub = str_replace($_username, '', $remark);
        $user_info = explode('/',$remark_sub);
        $remark_array['username'] =  $_username;
        $remark_array['code'] = $user_info[0];
        $remark_array['number'] = $user_info[1];
        return $remark_array;
    }


    public function return_success($data=null, $msg=''){
        return json_encode(array('code'=>'0', 'msg'=>$msg, 'data'=>$data), JSON_UNESCAPED_UNICODE);
    }

    public function return_error($msg=''){
        return json_encode(array('code'=>'1', 'msg'=>$msg), JSON_UNESCAPED_UNICODE);
    }


    private function money_log($user_id, $type, $money, $orderid, $order_tl, $msg="" ){
        if( intval($money) <= 0 ){
            return;
        }
        $res = Backend::consumption_type($type);
        $user = Db::name('user')->where('id', $user_id)->find();
        if( !$user ){
            return;
        }
        if( $res == true ){
            $price = $user['money'] + $money;
        }else{
            $price = $user['money'] - $money;
        }
        $order = Db::name($order_tl)->where('id', $orderid)->find();
        $data = array(
            'type'          =>$type,
            'order_id'      =>$orderid,
            'order_no'      =>$order['order_no'],
            'order_table'   =>$order_tl,
            'user_id'       =>$user['id'],
            'money'         =>$money,
            'surplus_money' =>$price,
            'rae'           =>$res,
            'msg'           =>$msg,
            'createtime'    =>time(),
            'updatetime'    =>time(),
        );

        $_log = Db::name('wallet_log')->insert($data);
        $_user = Db::name('user')->where('id', $user['id'])->update(['money'=>$price]);
        if( !empty($_log) && !empty($_user) ){
            return true;
        }
        return false;
    }



    /**
     * 测试代码使用
     * @return bool|string
     */
    public function test(){

        return "1111111111111111111";
    }

    public function test1(){

        return "222222";
    }

}
