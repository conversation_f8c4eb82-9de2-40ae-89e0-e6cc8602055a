<div id="content-container" class="container">
    <div class="user-section login-section">
        <div class="logon-tab clearfix"><a href="{:url('login/login')}?url={$url|urlencode|htmlentities}">{:__('Sign in')}</a> <a class="active">{:__('Sign up')}</a></div>
        <div class="login-main">
            <form name="form1" id="register-form" class="form-vertical" method="POST" action="">
                <!--@IndexRegisterFormBegin-->
                <input type="hidden" name="invite_user_id" value="0"/>
                <input type="hidden" name="url" value="{$url|htmlentities}"/>
                {:token()}
                <div class="form-group">
                    <label class="control-label required">{:__('Email')}<span class="text-success"></span></label>
                    <div class="controls">
                        <input type="text" name="email" id="email" data-rule="required;email" class="form-control" placeholder="{:__('Email')}">
                        <p class="help-block"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">{:__('Username')}</label>
                    <div class="controls">
                        <input type="text" id="username" name="username" data-rule="required;username" class="form-control" placeholder="{:__('Username must be 3 to 30 characters')}">
                        <p class="help-block"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">{:__('Password')}</label>
                    <div class="controls">
                        <input type="password" id="password" name="password" data-rule="required;password" class="form-control" placeholder="{:__('Password must be 6 to 30 characters')}">
                        <p class="help-block"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">{:__('Mobile')}</label>
                    <div class="controls">
                        <input type="text" id="mobile" name="mobile" data-rule="required;mobile" class="form-control" placeholder="{:__('Mobile')}">
                        <p class="help-block"></p>
                    </div>
                </div>

                <!--@CaptchaBegin-->
                {if $captchaType}
                <div class="form-group">
                    <label class="control-label">{:__('Captcha')}</label>
                    <div class="controls">
                        <div class="input-group">
                            {include file="common/captcha" event="register" type="$captchaType" /}
                        </div>
                        <p class="help-block"></p>
                    </div>
                </div>
                {/if}
                <!--@CaptchaEnd-->

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-lg btn-block">{:__('Sign up')}</button>
                    <a href="{:url('login/login')}?url={$url|urlencode|htmlentities}" class="btn btn-default btn-lg btn-block mt-3 no-border">{:__('Already have an account? Sign in')}</a>
                </div>
                <!--@IndexRegisterFormEnd-->
            </form>
        </div>
    </div>
</div>
