define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'order/addservice/index' + location.search,
                    add_url: 'order/addservice/add',
                    edit_url: 'order/addservice/edit',
                    del_url: 'order/addservice/del',
                    multi_url: 'order/addservice/multi',
                    import_url: 'order/addservice/import',
                    table: 'addservice',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'pg_id', title: __('Pg_id')},
                        {field: 'package.entrust_no', title: __('Package.entrust_no'), operate: 'LIKE'},
                        {field: 'addser_type_id', title: __('Addser_type_id')},
                        {field: 'tb_money', title: __('Tb_money')},
                        {field: 'bal_money', title: __('Bal_money')},
                        {field: 'actual_money', title: __('Actual_money')},
                        {field: 'pay_type', title: __('Pay_type'), searchList: {"0":__('Pay_type0'),"1":__('Pay_type1')} , formatter: Table.api.formatter.status},
                        {field: 'order_status', title: __('Order_status'), searchList: {"0":__('Order_status0'),"1":__('Order_status1')} , formatter: Table.api.formatter.status},
                        {field: 'remarks', title: __('Remarks'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
