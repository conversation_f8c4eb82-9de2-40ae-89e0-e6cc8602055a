{include file="common/resources" /}

<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>

<!-- 使用隐藏输入字段传递数据到Vue -->
<input type="hidden" id="applData" value='{:json_encode($rows)}'>
<div class="transport-box" id="Application">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div class="absolute fw fs16" style="top: 20px; left: 20px; color: #3D3D3D">申報人管理</div>
        <div class="handle-header" style="width: 100%; margin-top: 56px">
            <div style="display: flex; gap: 24px; align-items: center">
                <div class="fs16 c333">搜索內容：</div>
                <div style="display: flex">
                    <el-input
                    style="width: 340px;"
                    placeholder="姓名/電話、身份證字號"
                    v-model="iValue">
                    <i slot="suffix" class="el-input__icon el-icon-search pointer"></i>
                  </el-input>
                    <!-- <el-input v-model="iValue" style="width: 340px;" placeholder="收件人/電話"></el-input> -->
                </div>
            </div>
            <div class="filter-btns">
                <el-button icon="el-icon-plus" style="background: #22B573" type="success" @click="openAddAppl">新增
                </el-button>
                <el-button icon="el-icon-delete" type="warning">刪除
                </el-button>
                <el-button icon="el-icon-refresh" style="border: 1px solid #D8D8D8; background: #FFFFFF; color: #666666"
                    type="info">重置
                </el-button>
            </div>
        </div>
        <div class="table mb24">
            <div v-if="aList.length === 0" style="width: 100%; min-height: 572px;">
                <el-empty description="暫無數據"></el-empty>
            </div>
            <el-table v-else :data="aList" style="width: 1581px; min-height: 572px;" class="custom-header">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column prop="username" label="姓名" width="200" align="center"></el-table-column>
                <el-table-column prop="mobile" label="電話" width="380" align="center"></el-table-column>                  
                <el-table-column prop="card" label="身份證字號" width="380" align="center"></el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <span class="pointer" style="color:#22B573; margin-right:8px;">修改</span>
                        <span class="pointer" style="color:#EF436D;" @click="deleteApplRow(scope.row.id)">刪除</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="my-footer">
            <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]" :total="aList.length || 0"
                @current-change="handleCurrentChange" @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
        </div>
    </div>
</div>

<script>
    var applDataInput = document.getElementById('applData');
    var applData = [];
    try {
        applData = JSON.parse(applDataInput.value);
    } catch (e) {
        console.error('Error parsing JSON data:', e);
    }
    console.log(applData,'applData');
    
    const app = new Vue({
        el: '#Application',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            activeMenu: 0,
            aTypeOption: [
                { value: 1, label: '姓名' },
                { value: 2, label: '號碼' },
                { value: 3, label: '地址' },
            ],
            aList:applData,
            currentPage: 1,
            pageSize: 100,
            pageSizes: [5, 10, 20, 50],
            total: 0,
            recipientType: '',
            iValue: ''
        },
        mounted() {

        },
        methods: {
            openAddAppl() {
                window.location.href = '/index/transport/addApplicant';
            },
            handleCurrentChange() {

            },
            handleSizeChange() {

            },
            async deleteApplRow(id) {
                console.log(id,'id');
                try {
                    let res = await axios.post('/index/transport/deleteApplicant', {
                        id: id
                    }).then(response => {
                        console.log(response,'response');
                    })
                }catch(err) {
                    console.log(err,'err');
                }
            }
        }
    })
</script>