<?php

namespace app\admin\model\base;

use think\Model;


class Addservicedetail extends Model
{

    

    

    // 表名
    protected $name = 'addservice_detail';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function package()
    {
        return $this->belongsTo('app\admin\model\Package', 'pid', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function addservice()
    {
        return $this->belongsTo('app\admin\model\Addservice', 'aid', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function type()
    {
        return $this->belongsTo('app\admin\model\addservice\Type', 'at_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
