/**
 * 底部导航栏通用功能
 */

// 混入对象，包含底部导航所需的所有功能
var bottomNavMixin = {
    data: function() {
        return {
            showBottomNav: true, // 默认显示底部导航栏
            showQrcode: false,
            isOverQR: false,
            num: 6, // 消息数量，可以从服务端获取
            noticeUrl: '' // 将存储通知URL
        };
    },
    mounted: function() {
        // 在组件挂载时获取通知URL
        var bottomNav = document.getElementById('bottomNav');
        if (bottomNav) {
            this.noticeUrl = bottomNav.getAttribute('data-notice-url');
        }
    },
    methods: {
        // 通知消息功能
        noticeMsg: function() {
            console.log('通知消息按钮点击');
            // 使用从DOM元素获取的URL进行跳转
            if (this.noticeUrl) {
                window.open(this.noticeUrl);
            } else {
                console.error('通知消息URL未定义');
            }
        },
        
        // 返回顶部功能
        backTop: function() {
            console.log('返回顶部按钮点击');
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        },
        
        // 切换二维码显示
        addQrcode: function() {
            this.showQrcode = !this.showQrcode;
        },
        
        // 二维码悬停显示控制
        showQR: function() {
            this.showQrcode = true;
        },
        
        hideQR: function() {
            setTimeout(() => {
                if (!this.isOverQR) {
                    this.showQrcode = false;
                }
            }, 200);
        },
        
        keepQR: function() {
            this.isOverQR = true;
        },
        
        leaveQR: function() {
            this.isOverQR = false;
            this.hideQR();
        }
    }
}; 