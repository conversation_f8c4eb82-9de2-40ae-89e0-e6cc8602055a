{include file="common/resources" /}

<div class="transport-box" id="app" style="margin: 0; padding: 0; border: none;">
    {include file="common/bottom_nav" /}

    <div class="right-container" style="margin: 0; padding: 0;">
        <div class="nav">
            <img src="__CDN__/assets/img/pc/nav_logo.png" alt="" style="height: 50%">
            <div class="nav-right" onclick="window.location.href='/index/notice/index'">
                <div class="login-message">
                    <img class="pointer" src="__CDN__/assets/img/pc/new_index/parcel19.svg" alt="通知公告">
                    {if $notice_num}
                    <span class="login-message-num">{$notice_num}</span>
                    {/if}
                    <a href="#" class="terms-link pointer">通知公告</a>
                </div>
            </div>
        </div>
        <div class="tool-bg">
            <img src="__CDN__/assets/img/pc/login/tool_bg.png" alt=""
                style="width: 100%; height: 100%; filter: brightness(50%); position: relative;">
            <span>運費試算</span>
        </div>
        <div class="calc-content">
            <!-- 顶部标题 -->
            <div class="calc-box">
                <div class="calc-header">
                    <!-- <a href="#" class="calc-view-link">查看運費</a> -->
                </div>

                <!-- 运输方式选项 -->
                <!-- <div class="transport-tabs" style="display: flex; justify-content: center; align-items: center; gap: 18px;">
                    <div class="transport-tab" :class="{ active: activeTab === 15 }" @click="activeTab = 15">海快運費
                    </div>
                    <div class="transport-tab" :class="{ active: activeTab === 16 }" @click="activeTab = 16">空運運費</div>
                    <div class="transport-tab" :class="{ active: activeTab === 17 }" @click="activeTab = 17">海運運費</div>
                </div> -->
                <div class="tab-header mb12">
                    <div v-for="(item, index) in tabList" :class="{ 'active': activeTab === item.transport }"
                        :key="item.transport" @click="activeTab = item.transport" class="tab-items fs13 fw">
                        {{item.name}}
                    </div>
                </div>

                <!-- 包裹信息输入 -->
                <div class="calc-form">
                    <div class="form-item flex-between">
                        <div class="form-label">體積 /CM</div>
                        <div class="input-group">
                            <input type="text" class="calc-input" maxlength="9999" v-model="volume.length"
                                @input="validateInput('length')" placeholder="長">
                            <input type="text" class="calc-input" maxlength="9999" v-model="volume.width"
                                @input="validateInput('width')" placeholder="寬">
                            <input type="text" class="calc-input" maxlength="9999" v-model="volume.height"
                                @input="validateInput('height')" placeholder="高">
                        </div>
                    </div>

                    <div class="form-item flex-between">
                        <div class="form-label relative"><span class="required absolute" style="left: -6px;">*</span>重量
                            /KG</div>
                        <div class="input-group">
                            <input type="text" class="calc-input weight " maxlength="9999" v-model="weight"
                                @input="validateInput('weight')" @keyup.enter="calculateFee" placeholder="重量">
                        </div>
                    </div>

                    <!-- 移除货物类型选择 -->

                    <div v-if="activeTab === 15" class="form-item">
                        <div class="tax-radio">
                            <label class="radio-option">
                                <input type="radio" name="tax" value="1" v-model="taxStyle" @change="updateCities"
                                    checked>
                                <span>不包稅</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="tax" value="2" v-model="taxStyle" @change="updateCities">
                                <span>包稅繁稅</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="tax" value="3" v-model="taxStyle" @change="updateCities">
                                <span>全包稅</span>
                            </label>
                        </div>
                    </div>
                </div>
                <!-- <div class="divider"></div> -->
                <!-- 计费选项 -->
                <div class="calc-checkbox">
                    <label>
                        <input type="checkbox" v-model="calculateWoodenBox">
                        <span>計算打木架/木箱費用</span>
                    </label>
                    <button class="combine-fee-btn" @click="calculateFee">預估運費</button>
                </div>

                <!-- <div class="divider"></div> -->

                <div v-if="forecastStatus">
                    <!-- 海快预估费用提示 -->
                    <div class="fee-note">
                        <div class="shipping-icon">
                            <img :src="activeTab === 15 ? tranIcon[0] : activeTab === 16 ? tranIcon[1] :  tranIcon[2]"
                                alt="">
                        </div>
                        <span>{{
                            activeTab === 15 ? '海快' :
                            activeTab === 16 ? '空運' :
                            activeTab === 17 ? '海運' : ''
                            }}預估費用</span>
                        <span class="note-text">{{activeTab === 15 ? '以下皆以臺幣計價' : '普/特貨同價 全包稅'}}</span>
                    </div>
                    <div class="calc-options">
                        <!-- 运费结果表格 -->
                        <div class="fee-table" v-if="activeTab === 15">
                            <!-- 表头 -->
                            <div class="fee-row header">
                                <div class="price-cell">
                                    <span class="tag-label warehouse-tag">倉庫</span>
                                </div>
                                <div class="price-cell">
                                    <span class="tag-label air-price">集運件</span>
                                </div>
                                <div class="price-cell">
                                    <span class="tag-label sea-price">商業件</span>
                                </div>
                            </div>

                            <!-- 深圳到台湾各地的价格 -->
                            <div class="fee-row" v-for="(city, index) in cities" :key="index">
                                <div class="price-cell">{{ city.name }}</div>
                                <div class="price-cell">
                                    <span class="price">{{ city.airPrice }}<span class="fs13 c333">元</span></span>
                                    <div class="price-formula">{{ city.airFormula }}</div>
                                    <!-- <div class="price-type" v-if="city.airPriceType">計價方式: {{ city.airPriceType }}</div> -->
                                </div>
                                <div class="price-cell">
                                    <span class="price">{{ city.fastPrice }}<span class="fs13 c333">元</span></span>
                                    <div class="price-formula">{{ city.fastFormula }}</div>
                                    <!-- <div class="price-type" v-if="city.fastPriceType">計價方式: {{ city.fastPriceType }}</div> -->
                                </div>
                            </div>
                        </div>

                        <div class="fee-table" v-if="activeTab === 16">
                            <!-- 表头 -->
                            <div class="fee-row header">
                                <!-- <div class="price-cell">
                                <span class="tag-label warehouse-tag">倉庫</span>
                            </div> -->
                                <div class="price-cell">
                                    <span class="tag-label air-price">貨物類型</span>
                                </div>
                                <div class="price-cell">
                                    <span class="tag-label sea-price">運費</span>
                                </div>
                            </div>

                            <!-- 深圳到台湾各地的价格 -->
                            <div v-for="(city, index) in cities" :key="index">
                                <!-- 仓库名 -->
                                <!-- <div class="fee-row">
                                <div class="city-name" :style="{ textAlign: 'center', marginLeft: '-14px' }">{{ city.name }}</div>
                                <div style="flex: 2"></div>
                            </div> -->
                                <!-- 每个货物类型的价格 -->
                                <div class="fee-row" v-for="(goods, goodsIndex) in city.goodsTypes" :key="goodsIndex">
                                    <!-- <div style="flex: 1"></div> -->
                                    <div class="price-cell">
                                        <span>{{ goods.name }}</span>
                                    </div>
                                    <div class="price-cell">
                                        <span class="price">{{ goods.airPrice }}<span class="fs13 c333">元</span></span>
                                        <div class="price-formula">{{ goods.airFormula }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="fee-table" v-if="activeTab === 17">
                            <!-- 表头 -->
                            <div class="fee-row header">
                                <!-- <div class="price-cell">
                                <span class="tag-label warehouse-tag">倉庫</span>
                            </div> -->
                                <div class="price-cell">
                                    <span class="tag-label air-price">貨物類型</span>
                                </div>
                                <div class="price-cell">
                                    <span class="tag-label sea-price">運費</span>
                                </div>
                            </div>

                            <!-- 深圳到台湾各地的价格 -->
                            <div v-for="(city, index) in cities" :key="index">
                                <!-- 仓库名 -->
                                <!-- <div class="fee-row">
                                <div class="city-name" :style="{ textAlign: 'center', marginLeft: '-14px' }">{{ city.name }}</div>
                                <div style="flex: 2"></div>
                            </div> -->
                                <!-- 每个货物类型的价格 -->
                                <div class="fee-row" v-for="(goods, goodsIndex) in city.goodsTypes" :key="goodsIndex">
                                    <!-- <div style="flex: 1"></div> -->
                                    <div class="price-cell">
                                        <span>{{ goods.name }}</span>
                                    </div>
                                    <div class="price-cell">
                                        <span class="price">{{ goods.airPrice }}<span class="fs13 c333">元</span></span>
                                        <div class="price-formula">{{ goods.airFormula }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 新竹段费用 -->
                        <div class="section-header" v-if="volumePrice > 5">
                            <span>新竹超才費</span>
                            <el-tooltip popper-class="calc-box-shadow" effect="light" content="Bottom Center 提示文字"
                                placement="bottom">
                                <div slot="content" class="calc-tooltip">
                                    <span class="fs14 c333 fw">新竹超才</span>
                                    <span
                                        style="color: rgba(0, 0, 0, 0.8); font-size: 13px; line-height: 1.5;">新竹超才：新竹物流運費包含運送1才內的包裹(1才=長*寬*高/27000)，每超過1才加收25元超才費。</span>
                                </div>
                                <div class="tooltip-icon">
                                    <i class="el-icon-question" style="color: #C7C7C7; font-size: 14px;"></i>
                                </div>
                            </el-tooltip>
                        </div>

                        <div class="price-total" v-if="volumePrice > 5">
                            <span class="total-price">{{outVolume(volumePrice)}}<span class="fs13 c333">元</span></span>
                        </div>

                        <!-- 木箱/木棧板费用 -->
                        <div class="wooden-box-section" v-if="calculateWoodenBox">
                            <div class="section-header">
                                <span>打木箱/木棧板費用</span>
                            </div>

                            <div class="wooden-box-options">
                                <div class="wooden-box-option">
                                    <div class="wooden-icon">
                                        <img src="__CDN__/assets/img/pc/send_icon03.svg" alt="">
                                    </div>
                                    <div class="wooden-price">
                                        <span>打木箱</span>
                                        <span class="price">{{woodenBoxCalc}}<span class="fs13 c333">元</span></span>
                                    </div>
                                </div>

                                <div class="wooden-box-option">
                                    <div class="wooden-icon">
                                        <img src="__CDN__/assets/img/pc/send_icon04.svg" alt="">
                                    </div>
                                    <div class="wooden-price">
                                        <span>打木棧</span>
                                        <span class="price">{{woodenFrameCalc}}<span class="fs13 c333">元</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 支持尺寸提示 -->
                        <div class="send-method">
                            <div class="section-header" style="position: relative;">
                                <span>支持派件方式</span>
                                <el-tooltip popper-class="calc-box-shadow " effect="light" content="Bottom Center 提示文字"
                                    placement="bottom">
                                    <div slot="content" class="calc-tooltip calc-tooltip-send-method custom-scrollbar">
                                        <div class="tooltip-title">支持派件方式:</div>
                                        <div class="tooltip-section">
                                            <div class="section-send-title">[自己前往新北倉取件]：</div>
                                            <div class="section-send-content">貨物抵達倉庫時，由倉服通知，並自己前往新北倉取件。</div>
                                        </div>

                                        <div class="tooltip-section">
                                            <div class="section-send-title">[貨運公司宅配到府]：</div>
                                            <div class="section-send-content">
                                                貨物抵達倉庫時，由新北倉協助安排貨運公司配送到指定地址1樓，運費優惠每箱訂不限包裹件數、不限體重量，僅需<span
                                                    class="price-highlight">800元</span>台幣(不包含偏遠地區)。</div>
                                        </div>

                                        <div class="tooltip-section">
                                            <div class="section-send-title">偏遠地區收費表：</div>
                                            <table class="tooltip-table">
                                                <tr class="table-header">
                                                    <th colspan="3" style="text-align: center;">偏遠地區</th>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">台中</td>
                                                    <td style="font-size: 10px;">和平區 東勢區 新社區</td>
                                                    <td style="font-size: 10px; width: 50px;">1200</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">南投</td>
                                                    <td style="font-size: 10px;">埔里鎮 集集鎮 國姓鄉 水里鄉 魚池鄉 竹山鎮 鹿谷鄉 信義鄉 仁愛鄉 中寮鄉
                                                        名間鄉</td>
                                                    <td style="font-size: 10px; width: 50px;">1500</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">彰化</td>
                                                    <td style="font-size: 10px;">二水鄉 田中鎮 社頭鄉 埤頭鄉</td>
                                                    <td style="font-size: 10px; width: 50px;">1500</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">嘉義</td>
                                                    <td style="font-size: 10px;">阿里山 番路鄉 大埔鄉 中埔鄉 梅山鄉 竹崎鄉</td>
                                                    <td style="font-size: 10px; width: 50px;">1500</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">台南</td>
                                                    <td style="font-size: 10px;">南化區 楠西區 左鎮區 龍崗區 東山區 白河區</td>
                                                    <td style="font-size: 10px; width: 50px;">1500</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">高雄</td>
                                                    <td style="font-size: 10px;">旗山區 甲仙區 那瑪夏區 六龜區 茂林區 甲仙區 杉林區 內門區 美濃區
                                                    </td>
                                                    <td style="font-size: 10px; width: 50px;">2000</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">屏東</td>
                                                    <td style="font-size: 10px;">恆春東西/萬丹鄉/九如鄉/新埤鄉/里港 以外地區</td>
                                                    <td style="font-size: 10px; width: 50px;">2000</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">花蓮</td>
                                                    <td style="font-size: 10px;">玉里區/瑞穗</td>
                                                    <td style="font-size: 10px; width: 50px;">5500</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">花蓮</td>
                                                    <td style="font-size: 10px;">全區</td>
                                                    <td style="font-size: 10px; width: 50px;">3000</td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 10px; width: 40px;">台東</td>
                                                    <td style="font-size: 10px;">全區</td>
                                                    <td style="font-size: 10px; width: 50px;">3500</td>
                                                </tr>
                                            </table>
                                        </div>

                                        <div class="tooltip-notes">
                                            <p><span class="red">*</span>山區目前無法提供宅配服務，僅能自取或自行聯絡回頭車取件。</p>
                                            <p><span class="red">*</span>不支持離島派件。</p>
                                        </div>
                                    </div>
                                    <div class="tooltip-icon">
                                        <i class="el-icon-question" style="color: #C7C7C7; font-size: 14px;"></i>
                                    </div>
                                </el-tooltip>
                            </div>
                            <div class="send-method-list">
                                <!-- 超商 box (selected) -->
                                <div class="send-method-item-first"
                                    :style="{ border: exceedsSupermarketLimit ? '1px solid #EF436D' : '1px solid #2BA471' }">
                                    <div class="method-icon" style="width: 36px; height: 36px;">
                                        <img src="__CDN__/assets/img/pc/send_icon05.svg" style="margin-left: -10px;"
                                            alt="超商">
                                    </div>
                                    <div class="method-name" style="margin-left: -8px;">超商</div>
                                    <div v-if="exceedsSupermarketLimit" class="method-desc">體積超商限制<br>(45cm*30cm*30cm)
                                    </div>
                                    <div v-else class="method-status">可使用</div>
                                    <!-- <div v-if="" style="position: absolute; top: -10px; right: 10px; background: #EF436D; color: white; font-size: 12px; padding: 2px 8px; border-radius: 10px;">
                                    超出限制
                                 </div> -->
                                </div>

                                <!-- 新竹 box (not selected) -->
                                <div class="send-method-item-second">
                                    <div class="method-icon">
                                        <img src="__CDN__/assets/img/pc/xinzhu.png" alt="新竹">
                                    </div>
                                    <div class="method-name">新竹</div>
                                    <div class="method-status">可使用</div>
                                </div>
                            </div>
                        </div>

                        <!-- 新竹段费用说明 -->
                        <div class="fee-description">
                            <div class="section-header">
                                <span>新竹棧板費</span>
                            </div>
                            <p class="fs13" style="color: rgba(102, 102, 102, 0.8)">
                                新竹物流包裹才積若超過10才（長*寬*高/27000=才數）因為會佔用較多的車廂空間或者物品超過50kg人力不易搬運需要使用棧板，符合其中任意一種情形，新竹可能酌情加收費用統稱為棧板費。
                            </p>
                            <p class="fs13 fw" style="color: rgba(0, 0, 0, 0.8)">棧板費依地區有不同的收費標準，如下：</p>
                            <table class="calc-table"
                                style="width: 100%; border-collapse: collapse; border-spacing: 0; table-layout: fixed;">
                                <tr style="border-bottom: 1px solid #f5f5f5;">
                                    <td
                                        style="width: 50%; padding: 16px 20px; border: none; text-align: left; font-weight: 500;">
                                        北北基</td>
                                    <td style="width: 50%; padding: 16px 20px; border: none; text-align: left;">
                                        1000<span class="fs13 c333">元</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #f5f5f5;">
                                    <td
                                        style="width: 50%; padding: 16px 20px; border: none; text-align: left; font-weight: 500;">
                                        桃竹苗</td>
                                    <td style="width: 50%; padding: 16px 20px; border: none; text-align: left;">
                                        1200<span class="fs13 c333">元</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #f5f5f5;">
                                    <td
                                        style="width: 50%; padding: 16px 20px; border: none; text-align: left; font-weight: 500;">
                                        中彰投</td>
                                    <td style="width: 50%; padding: 16px 20px; border: none; text-align: left;">
                                        1500<span class="fs13 c333">元</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #f5f5f5;">
                                    <td
                                        style="width: 50%; padding: 16px 20px; border: none; text-align: left; font-weight: 500;">
                                        雲嘉南</td>
                                    <td style="width: 50%; padding: 16px 20px; border: none; text-align: left;">
                                        1800<span class="fs13 c333">元</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #f5f5f5;">
                                    <td
                                        style="width: 50%; padding: 16px 20px; border: none; text-align: left; font-weight: 500;">
                                        高高屏</td>
                                    <td style="width: 50%; padding: 16px 20px; border: none; text-align: left;">
                                        2000<span class="fs13 c333">元</span></td>
                                </tr>
                                <tr>
                                    <td
                                        style="width: 50%; padding: 16px 20px; border: none; text-align: left; font-weight: 500;">
                                        宜花東</td>
                                    <td style="width: 50%; padding: 16px 20px; border: none; text-align: left;">
                                        2200<span class="fs13 c333">元</span></td>
                                </tr>
                            </table>
                            <p class="note-red">
                                海快單件70KG以上包裹需要轉一般倉正式報關，會產生移倉費、倉租費、稅金等等費用，無法事先預估，會實報實銷由派件公司代取，正式報關需要提供委任書、賣方資料(姓名/地址/電話)、下單畫面。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="height: 78px; background-color: #ffffff;"></div>
        <div class="home-footer">
            2017-2025 CopyRight © 森活國際事業有限公司
        </div>
    </div>
</div>
<script>
    const app = new Vue({
        el: '#app',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            activeTab: 15,
            weight: '',
            forecastStatus:false,
            tabList:[
                {
                    name:'海快運費',
                    transport:15
                },
                {
                    name:'空運運費',
                    transport:16
                },
                {
                    name:'海運運費',
                    transport:17
                }
            ],
            volumePrice: null, // 材积价格
            goodsType: null, // 1: 普貨, 2: 特貨
            goodsTypeOptions: [],   // 空運貨物類型
            seaGoodsTypeOptions: [], // 海運貨物類型
            volume: {
                length: '', // 長
                width: '', // 寬
                height: '' // 高
            },
            volumeValue:null,
            exceedsSupermarketLimit: false, // 添加变量以跟踪是否超过超商限制
            tranIcon:[
                '__CDN__/assets/img/pc/transport01.svg',
                '__CDN__/assets/img/pc/transport02.svg',
                '__CDN__/assets/img/pc/transport03.svg'
            ],
            taxList:[],
            taxStyle: '1',
            calculateWoodenBox: false,
            totalPrice: '720',
            whList:[],  // 仓库列表
            cities: [],  // 篩選仓库列表
            woodenBoxCalc: null,
            woodenFrameCalc: null,
        },
        watch: {
            activeTab(newTab) {
                this.getTaxList(newTab);
                this.forecastStatus = false; // 切换运输方式时隐藏结果
                
                // 清空长宽高和重量
                this.volume.length = '';
                this.volume.width = '';
                this.volume.height = '';
                this.weight = '';
                this.volumePrice = null; // 清空材积价格
                
                // 重置货物类型为null，不选择默认值
                this.goodsType = null;
                
                console.log(newTab);
            },
            taxStyle() {
                // 当税类型变化时更新数据，仅当显示结果时才更新
                if (this.forecastStatus) {
                    this.updateCities();
                }
            },
            calculateWoodenBox(newVal,oldVal) {
                if (!newVal) {
                    this.woodenBoxCalc = 0;
                    this.woodenFrameCalc = 0;
                } else {
                    this.calcOtherExpenses();
                }
                console.log(newVal,oldVal);

            }
        },
        computed: {

        },
        mounted() {
            // 页面加载时获取税务类型列表
            this.getTaxList(this.activeTab);
        },
        methods: {
            // 重量換算
            calculateWeight(weight) {
                return utils.calculateWeight(weight);
            },
            // 验证输入，确保只能是大于0的数字，最多保留两位小数
            validateInput(field) {
                // 只允许输入数字和小数点
                let value;
                
                // 处理volume对象中的字段
                if (field === 'length' || field === 'width' || field === 'height') {
                    value = this.volume[field].replace(/[^\d.]/g, '');
                } else {
                    value = this[field].replace(/[^\d.]/g, '');
                }
                
                // 只允许一个小数点
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }
                
                // 限制小数点后只有两位
                if (parts.length > 1 && parts[1].length > 2) {
                    value = parts[0] + '.' + parts[1].substring(0, 2);
                }
                
                // 确保值大于0
                if (parseFloat(value) <= 0 && value !== '' && value !== '.') {
                    value = '';
                }
                
                // 添加数值上限验证
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    // 设置不同字段的最大值
                    const maxValues = {
                        length: 9999,
                        width: 9999,
                        height: 9999,
                        weight: 9999
                    };
                    
                    if (numValue > maxValues[field]) {
                        value = maxValues[field].toString();
                    }
                }
                
                // 更新相应的字段
                if (field === 'length' || field === 'width' || field === 'height') {
                    this.volume[field] = value;
                    
                    // 如果已经展示了结果且输入了所有三个维度，检查超商体积限制
                    if (this.forecastStatus && 
                        this.volume.length && 
                        this.volume.width && 
                        this.volume.height) {
                        this.checkVolumeLimit();
                    }
                } else {
                    this[field] = value;
                }
            },
            getTaxList(transport) {
               try {
                   axios.get('count').then(res=>{
                       if(Number(res.data.code) === 0) {
                            const list = res.data.data;
                            
                            // 存储所有的数据，不再根据运输方式筛选
                            this.taxList = list;
                            
                            // 动态获取货物类型选项 - 空运
                            const airTypesList = list.filter(item => item.transport === 16);
                            const airTypes = [];
                            const uniqueTypes = new Set();
                            
                            airTypesList.forEach(item => {
                                if (!uniqueTypes.has(item.goodstype)) {
                                    uniqueTypes.add(item.goodstype);
                                    airTypes.push({
                                        label: item.name || '普貨', // 如果name不存在，使用默认值
                                        value: item.goodstype
                                    });
                                }
                            });
                            
                            this.goodsTypeOptions = airTypes;
                            
                            // 动态获取货物类型选项 - 海运
                            const seaTypesList = list.filter(item => item.transport === 17);
                            const seaTypes = [];
                            const uniqueSeaTypes = new Set();
                            
                            seaTypesList.forEach(item => {
                                if (!uniqueSeaTypes.has(item.goodstype)) {
                                    uniqueSeaTypes.add(item.goodstype);
                                    seaTypes.push({
                                        label: item.name || `类型${item.goodstype}`, // 如果name不存在，使用类型+编号
                                        value: item.goodstype
                                    });
                                }
                            });
                            
                            this.seaGoodsTypeOptions = seaTypes;
                            
                            // 提取出不同的仓库，根据activeTab
                            this.updateWarehouses();
                            
                            // 更新城市数据
                            this.updateCities();
                       } else {
                            this.$message.error(res.data.msg);
                       }
                   })
               } catch(err) {
                   this.$message.error('網絡异常，請稍後再試！');
               }
            },
            
            // 更新仓库列表，根据当前activeTab
            updateWarehouses() {
                // 根据当前activeTab获取对应运输方式的仓库
                const filteredList = this.taxList.filter(item => item.transport === this.activeTab);
                this.whList = [...new Set(filteredList.map(item => item.title))];
            },
            
            // 根据taxStyle和运输方式更新cities数据
            updateCities() {
                if (!this.taxList || this.taxList.length === 0) return;
                
                // 根据当前activeTab获取对应运输方式的数据
                const filteredList = this.taxList.filter(item => item.transport === this.activeTab);
                
                if(this.activeTab === 15) {
                    // 获取唯一的仓库titles
                    this.whList = [...new Set(filteredList.map(item => item.title))];
                    
                    // 为每个仓库构建数据
                    this.cities = this.whList.map(warehouse => {
                        // 获取该仓库下符合当前税类型的集运件数据
                        const collectData = filteredList.find(item => 
                            item.title === warehouse && 
                            item.type === 1 && 
                            item.style === parseInt(this.taxStyle)
                        );
                        
                        // 获取该仓库下的商业件数据
                        const commercialData = filteredList.find(item => 
                            item.title === warehouse && 
                            item.type === 2 && 
                            item.style === 1  // 商业件只获取style=1的
                        );
                        
                        // 计算价格 - 集运件
                        let collectWeightPrice = '-';
                        let collectWeightFormula = '-';
                        let collectVolumePrice = '-';
                        let collectVolumeFormula = '-';
                        let collectFinalPrice = '-';
                        let collectFinalFormula = '-';
                        let collectPriceType = '';
                        
                        // 重量计算 - 集运件
                        if (collectData && this.weight) {
                            const weightValue = parseFloat(this.calculateWeight(this.weight));
                            collectWeightPrice = Math.ceil(collectData.scale_unit * weightValue);
                            collectWeightFormula = `${collectData.scale_unit}*${weightValue}KG=${collectWeightPrice}元`;
                        }
                        
                        // 体积计算 - 集运件
                        if (collectData && this.volume.length && this.volume.width && this.volume.height) {
                            const volumeValue = parseFloat(this.volume.length) * parseFloat(this.volume.width) * parseFloat(this.volume.height) / collectData.volume;
                            const volumeCalc = Math.ceil(volumeValue);
                            console.log('volumeCalc',volumeCalc)
                            collectVolumePrice = Math.ceil(volumeValue * collectData.volume_unit);
                            this.volumePrice = volumeCalc; // 保存才数用于计算超才费
                            collectVolumeFormula = `${volumeCalc}才*${collectData.volume_unit}=${collectVolumePrice}元`;
                        }
                        
                        // 取较高值 - 集运件
                        if (collectWeightPrice !== '-' && collectVolumePrice !== '-') {
                            if (parseFloat(collectWeightPrice) >= parseFloat(collectVolumePrice)) {
                                collectFinalPrice = collectWeightPrice;
                                collectFinalFormula = collectWeightFormula;
                                collectPriceType = '重量';
                            } else {
                                collectFinalPrice = collectVolumePrice;
                                collectFinalFormula = collectVolumeFormula;
                                collectPriceType = '材積';
                            }
                        } else if (collectWeightPrice !== '-') {
                            collectFinalPrice = collectWeightPrice;
                            collectFinalFormula = collectWeightFormula;
                            collectPriceType = '重量';
                        } else if (collectVolumePrice !== '-') {
                            collectFinalPrice = collectVolumePrice;
                            collectFinalFormula = collectVolumeFormula;
                            collectPriceType = '材積';
                        }
                        
                        // 计算价格 - 商业件
                        let commercialWeightPrice = '-';
                        let commercialWeightFormula = '-';
                        let commercialVolumePrice = '-';
                        let commercialVolumeFormula = '-';
                        let commercialFinalPrice = '-';
                        let commercialFinalFormula = '-';
                        let commercialPriceType = '';
                        
                        // 重量计算 - 商业件
                        if (commercialData && this.weight) {
                            const weightValue = parseFloat(this.calculateWeight(this.weight));
                            commercialWeightPrice = Math.ceil(commercialData.scale_unit * weightValue);
                            commercialWeightFormula = `${commercialData.scale_unit}*${weightValue}KG=${commercialWeightPrice}元`;
                        }
                        
                        // 体积计算 - 商业件
                        if (commercialData && this.volume.length && this.volume.width && this.volume.height) {
                            const volumeValue = parseFloat(this.volume.length) * parseFloat(this.volume.width) * parseFloat(this.volume.height) / commercialData.volume;
                            const volumeCalc = Math.ceil(volumeValue);
                            commercialVolumePrice = Math.ceil(volumeValue * commercialData.volume_unit);
                            commercialVolumeFormula = `${volumeCalc}才*${commercialData.volume_unit}=${commercialVolumePrice}元`;
                        }
                        
                        // 取较高值 - 商业件
                        if (commercialWeightPrice !== '-' && commercialVolumePrice !== '-') {
                            if (parseFloat(commercialWeightPrice) >= parseFloat(commercialVolumePrice)) {
                                commercialFinalPrice = commercialWeightPrice;
                                commercialFinalFormula = commercialWeightFormula;
                                commercialPriceType = '重量';
                            } else {
                                commercialFinalPrice = commercialVolumePrice;
                                commercialFinalFormula = commercialVolumeFormula;
                                commercialPriceType = '材積';
                            }
                        } else if (commercialWeightPrice !== '-') {
                            commercialFinalPrice = commercialWeightPrice;
                            commercialFinalFormula = commercialWeightFormula;
                            commercialPriceType = '重量';
                        } else if (commercialVolumePrice !== '-') {
                            commercialFinalPrice = commercialVolumePrice;
                            commercialFinalFormula = commercialVolumeFormula;
                            commercialPriceType = '材積';
                        }

                        return {
                            name: warehouse,
                            airPrice: collectFinalPrice,
                            airFormula: collectFinalFormula,
                            airPriceType: collectPriceType,
                            fastPrice: commercialFinalPrice,
                            fastFormula: commercialFinalFormula,
                            fastPriceType: commercialPriceType
                        };
                    });
                }
               
                if(this.activeTab === 16){
                    // 获取空运相关仓库
                    this.whList = [...new Set(filteredList.map(item => item.title))];
                    
                    // 为每个仓库构建数据 - 显示所有货物类型的价格
                    this.cities = this.whList.map(warehouse => {
                        // 获取该仓库下的所有货物类型数据
                        const warehouseItems = filteredList.filter(item => item.title === warehouse);
                        
                        // 构建每种货物类型的价格信息
                        const goodsTypeResults = [];
                        
                        warehouseItems.forEach(airData => {
                            let airPrice = '-';
                            let airFormula = '-';
                            let goodsTypeName = airData.name || `类型${airData.goodstype}`;
                            
                            // 如果有数据且输入了重量
                            if (airData && this.weight) {
                                const weightValue = parseFloat(this.calculateWeight(this.weight));
                                // 空运使用fl_weight计算
                                const price = weightValue * airData.fl_weight;
                                airPrice = Math.ceil(price);
                                airFormula = `${weightValue}KG*${airData.fl_weight}=${airPrice}元`;
                            }
                            
                            // 添加材积计算，用于计算新竹超才费
                            if (airData && this.volume.length && this.volume.width && this.volume.height) {
                                const volumeValue = parseFloat(this.volume.length) * parseFloat(this.volume.width) * parseFloat(this.volume.height) / 27000;
                                const volumeCalc = Math.ceil(volumeValue);
                                this.volumePrice = volumeCalc; // 保存才数用于计算超才费
                            }
                            
                            goodsTypeResults.push({
                                name: goodsTypeName,
                                airPrice: airPrice,
                                airFormula: airFormula
                            });
                        });
                        
                        return {
                            name: warehouse,
                            goodsTypes: goodsTypeResults
                        };
                    });
                }

                if(this.activeTab === 17){
                    // 获取海运相关仓库
                    this.whList = [...new Set(filteredList.map(item => item.title))];
                    
                    // 为每个仓库构建数据 - 显示所有货物类型的价格
                    this.cities = this.whList.map(warehouse => {
                        // 获取该仓库下的所有货物类型数据
                        const warehouseItems = filteredList.filter(item => item.title === warehouse);
                        
                        // 构建每种货物类型的价格信息
                        const goodsTypeResults = [];
                        
                        warehouseItems.forEach(seaData => {
                            let weightPrice = '-';
                            let weightFormula = '-';
                            let volumePrice = '-';
                            let volumeFormula = '-';
                            let finalPrice = '-';
                            let finalFormula = '-';
                            let goodsTypeName = seaData.name || `类型${seaData.goodstype}`;
                            
                            // 重量计算
                            if (seaData && this.weight) {
                                const weightValue = parseFloat(this.calculateWeight(this.weight));
                                weightPrice = Math.ceil(seaData.scale_unit * weightValue);
                                weightFormula = `${seaData.scale_unit}*${weightValue}KG=${weightPrice}元`;
                            }
                            
                            // 体积计算
                            if (seaData && this.volume.length && this.volume.width && this.volume.height) {
                                const volumeValue = parseFloat(this.volume.length) * parseFloat(this.volume.width) * parseFloat(this.volume.height) / seaData.volume;
                                const volumeCalc = Math.ceil(volumeValue);
                                volumePrice = Math.ceil(volumeValue * seaData.volume_unit);
                                volumeFormula = `${volumeCalc}才*${seaData.volume_unit}=${volumePrice}元`;
                                
                                // 保存才数用于计算超才费
                                this.volumePrice = volumeCalc;
                            }
                            
                            // 取较高值
                            if (weightPrice !== '-' && volumePrice !== '-') {
                                if (parseFloat(weightPrice) >= parseFloat(volumePrice)) {
                                    finalPrice = weightPrice;
                                    finalFormula = weightFormula;
                                } else {
                                    finalPrice = volumePrice;
                                    finalFormula = volumeFormula;
                                }
                            } else if (weightPrice !== '-') {
                                finalPrice = weightPrice;
                                finalFormula = weightFormula;
                            } else if (volumePrice !== '-') {
                                finalPrice = volumePrice;
                                finalFormula = volumeFormula;
                            }
                            
                            goodsTypeResults.push({
                                name: goodsTypeName,
                                airPrice: finalPrice,
                                airFormula: finalFormula
                            });
                        });
                        
                        return {
                            name: warehouse,
                            goodsTypes: goodsTypeResults
                        };
                    });
                }
            
                // 更新总价
                this.updateTotalPrice();
                console.log('超才num', this.volumePrice);
                console.log('仓库数据', this.cities);
            },

            outVolume(val) {
                return (val - 5) * 25;
            },
            
            updateTotalPrice() {
                // 选择第一个仓库的集运件价格作为总价，可根据实际需求修改
                if (this.cities && this.cities.length > 0 && this.cities[0].airPrice !== '-') {
                    this.totalPrice = this.cities[0].airPrice;
                } else {
                    this.totalPrice = '0';
                }
            },

            calculateFee() {
                // 检查必填项
                if (!this.weight) {
                    this.$message.error('請輸入包裹重量');
                    return;
                }
                
                // 计算木箱/木架费用（如果勾选）
                this.calcOtherExpenses();
                
                // 显示结果区域
                this.forecastStatus = true;
                
                // 获取选中的税务类型
                const selectedStyle = this.taxStyle;
                console.log('计算运费时使用的税务类型:', selectedStyle);
                
                // 检查体积是否超过超商限制
                this.checkVolumeLimit();
                
                // 更新表格数据
                this.updateCities();
            },
            
            // 检查体积是否超过超商限制
            checkVolumeLimit() {
                if (this.volume.length && this.volume.width && this.volume.height) {
                    // 计算体积
                    const length = parseFloat(this.volume.length);
                    const width = parseFloat(this.volume.width);
                    const height = parseFloat(this.volume.height);
                    
                    // 确保所有维度都有有效值
                    if (!isNaN(length) && !isNaN(width) && !isNaN(height)) {
                        // 超商限制为 45*30*30 = 40500
                        const supermarketLimit = 45 * 30 * 30;
                        const currentVolume = length * width * height;
                        
                        // 更新超过限制的状态
                        this.exceedsSupermarketLimit = currentVolume > supermarketLimit;
                        
                        console.log('体积检查:', currentVolume, '超商限制:', supermarketLimit, '超出:', this.exceedsSupermarketLimit);
                    } else {
                        this.exceedsSupermarketLimit = false;
                    }
                } else {
                    this.exceedsSupermarketLimit = false;
                }
            },

            calcOtherExpenses() {
                if (this.calculateWoodenBox) {
                    if (this.volume.length && this.volume.width && this.volume.height) {
                        let volume = this.volume.length * this.volume.width * this.volume.height;
                        let cbm = 100 * 100 * 100; // 1立方米
                        this.woodenBoxCalc = Math.ceil(volume * (2100 / cbm)) || 0;
                        this.woodenFrameCalc = Math.ceil(volume * (2800 / cbm)) || 0;
                    } else {
                        this.woodenBoxCalc = 0;
                        this.woodenFrameCalc = 0;
                    }
                } else {
                    this.woodenBoxCalc = 0;
                    this.woodenFrameCalc = 0;
                }
            }
        }
    })
</script>

<style>
    .price-type {
        font-size: 10px;
        color: #666666;
        margin-top: 2px;
    }

    .price-formula {
        width: 100%;
        word-wrap: break-word;
        overflow-wrap: break-word;
        white-space: normal;
        word-break: break-all;
        font-size: 10px;
        color: #666666;
    }

    /* 设置el-select和el-option高度为24px */
    .calc-form .el-select,
    .calc-form .el-input {
        height: 24px !important;
        width: 156px !important;
        min-height: 24px !important;
        line-height: 24px !important;
    }

    .calc-form .el-input__inner {
        height: 24px !important;
        min-height: 24px !important;
        line-height: 24px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        width: 156px !important;
    }

    /* 下拉菜单项高度 */
    .el-select-dropdown__item {
        height: 24px !important;
        min-height: 24px !important;
        line-height: 24px !important;
        width: 156px !important;
    }
    
    /* 调整下拉箭头图标垂直居中 */
    .calc-form .el-input__icon,
    .calc-form .el-select__caret {
        height: 24px !important;
        line-height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    /* 设置fee-row自动调整高度 */
    .fee-table .fee-row {
        min-height: 48px;
        height: auto;
        align-items: stretch;
    }
    
    /* 调整price-cell的布局，使其能自动调整高度 */
    .fee-table .fee-row .price-cell {
        height: auto;
        min-height: 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 4px;
    }
    
    /* 确保city-name也能自动调整高度 */
    .fee-table .fee-row {
        height: auto;
        min-height: 40px;
        display: flex;
        align-items: center;
        padding: 4px;
    }
    
    /* 确保所有内容都可以换行 */
    .fee-table .fee-row .price-cell span,
    .fee-table .fee-row {
        word-break: break-word;
        white-space: normal;
    }
</style>
