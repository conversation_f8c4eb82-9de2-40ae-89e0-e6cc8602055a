define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'news/help/index' + location.search,
                    add_url: 'news/help/add',
                    edit_url: 'news/help/edit',
                    del_url: 'news/help/del',
                    multi_url: 'news/help/multi',
                    import_url: 'news/help/import',
                    table: 'help',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'type', title: __('Type')},
                        {field: 'title', title: __('Title'), operate: 'LIKE'},
                        {field: 'weight', title: __('Weight')},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Normal'),"1":__('Hidden')}, formatter: Table.api.formatter.status, custom:{'0':'success','1':'primary'}},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
