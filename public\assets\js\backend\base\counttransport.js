define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'base/counttransport/index' + location.search,
                    add_url: 'base/counttransport/add',
                    edit_url: 'base/counttransport/edit',
                    del_url: 'base/counttransport/del',
                    multi_url: 'base/counttransport/multi',
                    import_url: 'base/counttransport/import',
                    table: 'count_transport',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'warehouse.title', title: __('Warehouse.title'), operate: 'LIKE'},
                        // {field: 'wh_id', title: __('Wh_id')},
                        {field: 'dict.name', title: __('Transport'), operate: 'LIKE'},
                        // {field: 'transport', title: __('Transport')},
                        {field: 'minday', title: __('Minday')},
                        {field: 'maxday', title: __('Maxday')},
                        {field: 'goodstype.title', title: __('Goodstype.title'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'goodstype', title: __('Goodstype')},
                        {field: 'type', title: __('Type'), searchList: {"0":__('Type0'), "1":__('Type1'),"2":__('Type2')}, formatter: Table.api.formatter.status},
                        {field: 'style', title: __('Style'), searchList: {"0":__('Style0'), "1":__('Style1'),"2":__('Style2'),"3":__('Style3')}, formatter: Table.api.formatter.status},
                        {field: 'scale_unit', title: __('Scale_unit')},
                        {field: 'volume_unit', title: __('Volume_unit')},
                        {field: 'volume', title: __('Volume')},
                        {field: 'fr_weight', title: __('Fr_weight')},
                        {field: 'fl_weight', title: __('Fl_weight')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
