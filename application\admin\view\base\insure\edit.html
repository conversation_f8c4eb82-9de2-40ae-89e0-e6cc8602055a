<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Alias')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-alias" data-rule="required" class="form-control" name="row[alias]" type="text" value="{$row.alias|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-content" data-rule="required" class="form-control" name="row[content]" type="text" value="{$row.content|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cg_proprotion')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cg_proprotion" data-rule="required" class="form-control" step="0.01" name="row[cg_proprotion]" type="number" value="{$row.cg_proprotion|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cp_proportion')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cp_proportion" data-rule="required" class="form-control" step="0.01" name="row[cp_proportion]" type="number" value="{$row.cp_proportion|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ns_min')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ns_min" data-rule="required" class="form-control" name="row[ns_min]" type="number" value="{$row.ns_min|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ns_max')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ns_max" data-rule="required" class="form-control" name="row[ns_max]" type="number" value="{$row.ns_max|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Most')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-most" data-rule="required" class="form-control" name="row[most]" type="number" value="{$row.most|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Describe')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-describe" data-rule="required" class="form-control" name="row[describe]" type="text" value="{$row.describe|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Json_data')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-json_data" data-rule="required" class="form-control" name="row[json_data]" type="text" value="{$row.json_data|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="{$row.status|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
