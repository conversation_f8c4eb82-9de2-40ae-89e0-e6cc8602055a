{include file="common/resources" /}

<!-- 添加CSRF token -->
{:token()}

<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="imp">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div class="absolute fw fs16" style="top: 20px; left: 20px; color: #3D3D3D">進口憑證</div>
        <div style="box-shadow: 0 1px 4px rgba(0,0,0,0.05); min-height: 64px;padding: 20px; margin:56px 0 24px;">
            <el-form :model="form" label-width="80px" ref="form">
                <div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="稅單編號：" label-width="100px">
                                <el-input placeholder="請輸入" v-model="form.deliveryId" style="width: 340px;"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="稅單日期：" label-width="100px">
                                <el-date-picker v-model="impData" type="daterange" align="center" unlink-panels
                                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                                                :picker-options="pickerOptions">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <div class="handle-btn" style="display: flex; gap: 16px; justify-content: flex-end;">
                                <el-button icon="el-icon-search" style="background: #EF436D" type="danger" @click="searchParcel">搜索
                                </el-button>
                                <el-button icon="el-icon-refresh" style="background: #FFFFFF" @click="resetInput">重置
                                </el-button>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
        </div>
        <div class="order-table">
            <div v-if="impList.length === 0" style="width: 100%; min-height: 580px;">
                <el-empty description="暫無數據"></el-empty>
            </div>
            <el-table v-else :data="impList" style="width: 100%; min-height: 580px;" class="custom-header">
                <el-table-column prop="createTime" label="稅單編號"></el-table-column>
                <el-table-column prop="orderId" label="主運單號"></el-table-column>
                <el-table-column prop="amount" label="稅金（TNS）"></el-table-column>
                <el-table-column prop="weight" label="稅單時間"></el-table-column>
                <el-table-column prop="expressId" label="稅單狀態"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <span style="color: #EF436D; cursor: pointer; margin-right: 8px;">查看訂單詳情</span>
                        <span v-if="scope.row.status === '待支付'" style="color: #EF436D; cursor: pointer; margin-right: 8px;">去支付</span>
                        <span v-if="scope.row.status === '待支付'" style="color: #EF436D; cursor: pointer;">取消訂單</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="my-footer">
            <el-pagination :current-page="currentPage" :page-size="100" :page-sizes="[5,10,20,50]" :total="impList.length"
                @current-change="handleCurrentChange" @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#imp',
        mixins: [bottomNavMixin],
        data: {
            activeIndex: 0,
            tabList: ['集運訂單', '附加服務訂單'],
            form: {
                warehouseName: '',
                deliveryId: ''
            },
            warehouseOptions: [
                { value: 'warehouse1', label: 'Warehouse 1' },
                { value: 'warehouse2', label: 'Warehouse 2' }
            ],
            currentPage: 1,
            currentPageSize: 5,
            pageSize: [5, 10, 20, 50],
            impData: '',
            pickerOptions: {
                shortcuts: [{
                    text: '近1個月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 1); // Subtract 1 month
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近3個月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 3); // Subtract 3 months
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近12個月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 12); // Subtract 12 months
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            impList:[],
            // impList: [
            //     {
            //         createTime: '2025-03-07 13:58:03',
            //         orderId: '9461031785204283',
            //         amount: 'NT$ 275',
            //         weight: '2.75KG',
            //         expressId: '9461031785204283',
            //         sendTime: '2025-03-07 13:50:03',
            //         status: '待支付',
            //         sender: '巧巧'
            //     },
            //     {
            //         createTime: '2025-03-07 13:58:03',
            //         orderId: '9461031785204283',
            //         amount: 'NT$ 275',
            //         weight: '2.75KG',
            //         expressId: '9461031785204283',
            //         sendTime: '2025-03-07 13:50:03',
            //         status: '待簽收',
            //         sender: '巧巧'
            //     },
            //     {
            //         createTime: '2025-03-07 13:58:03',
            //         orderId: '9461031785204283',
            //         amount: 'NT$ 275',
            //         weight: '2.75KG',
            //         expressId: '9461031785204283',
            //         sendTime: '2025-03-07 13:50:03',
            //         status: '已完成',
            //         sender: '巧巧'
            //     },
            //     {
            //         createTime: '2025-03-07 13:58:03',
            //         orderId: '9461031785204283',
            //         amount: 'NT$ 275',
            //         weight: '2.75KG',
            //         expressId: '9461031785204283',
            //         sendTime: '2025-03-07 13:50:03',
            //         status: '已完成',
            //         sender: '巧巧'
            //     },
            //     {
            //         createTime: '2025-03-07 13:58:03',
            //         orderId: '9461031785204283',
            //         amount: 'NT$ 275',
            //         weight: '2.75KG',
            //         expressId: '9461031785204283',
            //         sendTime: '2025-03-07 13:50:03',
            //         status: '已完成',
            //         sender: '巧巧'
            //     }
            // ],
        },
        methods: {
            changeTabs(index) {
                this.activeIndex = index;
                console.log('Active tab changed to:', this.tabList[index]);
            },
            onWarehouseChange() {
                console.log('Selected warehouse:', this.form.warehouseName);
            },
            searchParcel() {
                console.log('Searching for parcel with ID:', this.form.deliveryId);
            },
            resetInput() {
                this.form.warehouseName = '';
                this.form.deliveryId = '';
            },
            handleCurrentChange() {
                console.log('******');
            },
            handleSizeChange() {
                console.log('Page size changed');
            }
        }
    });
</script>
<style>
    .el-form-item__label {
        margin-top: -4px;
    }
</style>