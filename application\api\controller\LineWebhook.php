<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\exception\ApiException;
use app\common\controller\Frontend;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use think\Log;
use think\Request;
use think\Exception;

class LineWebhook extends Api
{
    const CHANNEL_ACCESS_TOKEN = 'PhE5QY6Y4ds8CvUGbHTcu4HG0BgDO8BM1bbjFKNUxcAsD9AFV5Met4G35p4KPi8tuXQ0zAyxSDaoJOd3tPZ2jJFaRaqVYdRDqkm+lYGMb1TQQvwl7r3uEUFNS61uC62RoLbCbMdPQzCqmoVs+wRrjAdB04t89/1O/w1cDnyilFU=';
    const CHANNEL_SECRET = '9bec1de61d5eb7b113ada926a1e1d0f7';
    const API_URI = 'https://api.line.me/v2/bot/';

    protected $noNeedLogin = '*';


    private Client  $client;
    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->client = new Client([
            'base_uri' => self::API_URI,
            'timeout' => 5,
            'verify' => false,
        ]);

    }


    /**
     * webhook callback
     * @return void
     */
    public function index()
    {
        $signature = $_SERVER['HTTP_X_LINE_SIGNATURE'] ?? '';
        $body = file_get_contents('php://input');
        if (!$this->verify_signature($body, $signature)) {
            return;
        }
        $data = json_decode($body, true);
        $events = $data['events'];

        foreach ($events as $event) {
            $eventType = $event['type'] ?? '';
            switch ($eventType) {
                case 'message': // 消息
                    $message = $events['message']['text'] ?? '';
                    break;
                case 'unfollow': // 屏蔽
                    Log::record('unfollow');
                    break;
                default:

            }
        }

    }

    private function verify_signature($body, $signature): bool
    {
        return $signature === base64_encode(hash_hmac('sha256', $body, self::CHANNEL_SECRET, true));
    }

    /**
     * 推送消息
     * @param string $message 消息體
     * @param string $to 接受賬號
     * @param string $title
     * @return array
     * @throws 
     */
    public function push_message(string $message, string $to, string $title='message'): array
    {
        $resp = $this->client->post('message/push', [
            'json' => [
                'to' => $to,
                'messages' => [
                    [
                        'type' => 'flex',
                        'altText' => $title,
                        'contents'=> json_decode($message, true),
                    ]
                ],
            ],
            'headers' => [
                'Authorization' => 'Bearer ' . self::CHANNEL_ACCESS_TOKEN,
            ],
        ]);
        if ($resp->getStatusCode() !== 200) {
            throw new Exception('推送失敗');
        }
        return json_decode($resp->getBody()->getContents(), true);
    }

    /**
     * 推送訂單信息
     * @param array $order_data 訂單數據
     * @param string $to 接受人
     * @return void
     */
    public function order(array $order_data, string $to)
    {
        $order_data = [
            'create_time' => '2025-07-03 10:36:00',
            'order_no' => 'Y8051237354912687',
            // 'img' => 'https://jiyun.frp.nxun.cn:10021/uploads/1.png',
            'good_name' => '任天堂Switch2 主機switch 2 瑪麗歐賽車世界 主機組2代原廠公司貨 NS2 游戲主機 Q哥電玩',
            'good_price' => '￥3999.9',
            'good_weight' => '2.9KG',
            'order_total' => 'NT$ 18,045',
            'pay_status' => '待付款',
            'redirect_url' => 'https://linecorp.com'
        ];
        $message = $this->getTemplate('order');
        $this->send_message($order_data, $message, $to,'訂單消息');
    }

    private function getTemplate(string $type): string
    {
        $path = ROOT_PATH . 'public' . DS . 'template' . DS . 'line' . DS . $type . '.json';
        if (!$this->checkPath($path)) {
            throw new FileException('消息模板不存在');
        }
        return file_get_contents($path);
    }

    private function checkPath(string $path): bool
    {
        return is_file($path);
    }


    /**
     * 推送物流信息
     * @param array $delivery_data 配送數據
     * @param string $to 接收人
     * @return void
     */
    public function delivery(array $delivery_data, string $to)
    {
        $delivery_data = [
            'create_time' => '2025-7-2 15:20:53',
            'order_no' => 'Y8051237354912687',
            'delivery_no' => 'SF9461031785204283',
            'url' => 'https://linecorp.com',
        ];
        $message = $this->getTemplate('delivery');
        $this->send_message($delivery_data, $message, $to, '配送消息');
    }

    /**
     * @param array $message_data
     * @param $message
     * @param string $to
     * @param string $title
     * @return void
     */
    private function send_message(array $message_data, $message, string $to, string $title='message'): void
    {
        foreach ($message_data as $key => $val) {
            $message = str_replace("<<$key>>", $val, $message);
        }
        try {
            $data = $this->push_message($message, $to, $title);
        } catch (GuzzleException|Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('ok', $data ?? []);
    }

    public function botIsFriend(string $lineid): bool
    {
        try {
            $resp = $this->client->get('profile/'.$lineid, [
                'headers' => [
                    'Authorization' => 'Bearer ' . self::CHANNEL_ACCESS_TOKEN,
                ],
            ]);
            if ($resp->getStatusCode() !== 200) {
                return false;
            }
            return true;
        } catch (GuzzleException $e) {
            return false;
        }
    }
}