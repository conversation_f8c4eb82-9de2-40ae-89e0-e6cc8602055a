<?php

namespace app\admin\controller\news;

use app\common\controller\Backend;
use think\Config;



/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Jiyunmap extends Backend
{

    /**
     * Jiyunmap模型对象
     * @var \app\admin\model\news\Jiyunmap
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\news\Jiyunmap;

        $this->view->assign("statusList", $this->model->getStatusList());
    }


    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where)
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
			$sitemap = Config::get('site.jiyunmap');
            foreach ($list as $key=>$row) {
				$list[$key]['type'] = $sitemap[$row['type']];
				$list[$key]['type_id'] = $row['type'];
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */







}
