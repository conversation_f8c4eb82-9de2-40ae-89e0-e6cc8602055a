define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'order/jyorder/index' + location.search,
                    add_url: 'order/jyorder/add',
                    edit_url: 'order/jyorder/edit',
                    del_url: 'order/jyorder/del',
                    multi_url: 'order/jyorder/multi',
                    import_url: 'order/jyorder/import',
                    table: 'jyorder',  
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'pid', title: __('Pid')},
                        {field: 'order_no', title: __('Order_no'), operate: 'LIKE'},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'num', title: __('Num')},
                        {field: 'scale', title: __('Scale'), operate:'BETWEEN'},
                        {field: 'volume', title: __('Volume'), operate:'BETWEEN'},
                        {field: 'volumetwo', title: __('Volumetwo'), operate:'BETWEEN'},
                        {field: 'dispatch', title: __('Dispatch')},
                        {field: 'seaway', title: __('Seaway')},
                        {field: 'prot_type', title: __('Prot_type')},
                        {field: 'prot_money', title: __('Prot_money')},
                        {field: 'tb_money', title: __('Tb_money')},
                        {field: 'bal_money', title: __('Bal_money')},
                        {field: 'user_coupon_id', title: __('User_coupon_id')},
                        {field: 'actual_money', title: __('Actual_money')},
                        {field: 'rec_money', title: __('Rec_money')},
                        {field: 'outer_island', title: __('Outer_island')},
                        {field: 'pay_status', title: __('Pay_status')},
                        {field: 'pay_type', title: __('Pay_type')},
                        {field: 'bank_id', title: __('Bank_id')},
                        {field: 'bank_num', title: __('Bank_num'), operate: 'LIKE'},
                        {field: 'order_status', title: __('Order_status')},
                        {field: 'is_invoice', title: __('Is_invoice')},
                        {field: 'invoice_id(', title: __('Invoice_id(')},
                        {field: 'remarks', title: __('Remarks'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'package.entrust_no', title: __('Package.entrust_no'), operate: 'LIKE'},
                        {field: 'user.username', title: __('User.username'), operate: 'LIKE'},
                        {field: 'coupon.title', title: __('Coupon.title'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'trade.id', title: __('Trade.id')},
                        {field: 'trade.bank_title', title: __('Trade.bank_title'), operate: 'LIKE'},
                        {field: 'trade.account', title: __('Trade.account'), operate: 'LIKE'},
                        {field: 'trade.in_account', title: __('Trade.in_account'), operate: 'LIKE'},
                        {field: 'trade.version', title: __('Trade.version'), operate: 'LIKE'},
                        {field: 'trade.date_time', title: __('Trade.date_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'trade.taken', title: __('Trade.taken'), operate:'BETWEEN'},
                        {field: 'trade.save', title: __('Trade.save'), operate:'BETWEEN'},
                        {field: 'trade.balance', title: __('Trade.balance'), operate:'BETWEEN'},
                        {field: 'trade.abstract', title: __('Trade.abstract'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'trade.remark', title: __('Trade.remark'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'trade.admin_id', title: __('Trade.admin_id'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'trade.remarks', title: __('Trade.remarks'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'trade.user_id', title: __('Trade.user_id')},
                        {field: 'trade.jorder_id', title: __('Trade.jorder_id')},
                        {field: 'trade.rorder_id', title: __('Trade.rorder_id')},
                        {field: 'trade.is_normal', title: __('Trade.is_normal')},
                        {field: 'trade.createtime', title: __('Trade.createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'trade.updatetime', title: __('Trade.updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
