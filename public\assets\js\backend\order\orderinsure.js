define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'order/orderinsure/index' + location.search,
                    add_url: 'order/orderinsure/add',
                    edit_url: 'order/orderinsure/edit',
                    // del_url: 'order/orderinsure/del',
                    multi_url: 'order/orderinsure/multi',
                    import_url: 'order/orderinsure/import',
                    table: 'order_insure',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user.username', title: __('User.username'), operate: 'LIKE'},
                        {field: 'user.mobile', title: __('User.mobile'), operate: 'LIKE'},
                        {field: 'jyorder.order_no', title: __('Jyorder.order_no'), operate: 'LIKE'},
                        // {field: 'jy_order', title: __('Jy_order')},
                        // {field: 'insure_id', title: __('Insure_id')},
                        {field: 'insure.name', title: __('Insure.name'), operate: 'LIKE'},
                        {field: 'no', title: __('No'), operate: 'LIKE'},
                        // {field: 'user_id', title: __('User_id')},
                        {field: 'bj_price', title: __('Bj_price')},
                        {field: 'goods_price', title: __('Goods_price')},
                        {field: 'ag_price', title: __('Ag_price')},
                        {field: 'base_price', title: __('Base_price')},
                        {field: 'total_price', title: __('Total_price')},
                        {field: 'collect_price', title: __('Collect_price')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
