{include file="common/meta" /}
{css href="__CDN__/assets/css/transportation.css" /}


<div class="user-info" class="js_ajax_win" >
    {if $user['username']}
    <el-avatar size="medium"   href="{:url('/index/user/edit_avatar')}"><?=($user['username']);?></el-avatar>
    {else}
    <el-avatar icon="el-icon-user-solid" style="display: flex;justify-content: center;align-items: center;"></el-avatar>
    {/if}
    {if $user['username']}
    <span class="ml6 fs14"><?=($user['username']);?></span>
    {else}
    <span class="ml6 fs14">
        <?php
            $mobile = $user['mobile'];
            if(strlen($mobile) == 10 && substr($mobile, 0, 2) == '09') {
                echo substr($mobile, 0, 2) . '****' . substr($mobile, -4);
            } else {
                echo $mobile;
            }
        ?>
    </span>
    {/if}
</div>
<div class="log-out-btn">
    {include file="common/logout" /}
</div>







