{include file="common/resources" /} 

<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="recharge">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <div class="wallet-page">
            <div class="title">集運幣儲值</div>
            <div class="sub-title">儲值金額</div>
            <div class="input-container">
                <div class="left-label">新臺幣</div>
                <div class="input-wrapper" style="height: 48px;">
                    <input :min="0" @input="handleAmount"  class="custom-input" placeholder="請輸入臺幣金額"
                        style="padding-right: 60px; border-radius: 0 8px 8px 0;" type="text" v-model="TWDAmount" />
                    <span class="currency-symbol">TWD</span>
                </div>
            </div>
            <div class="marking">*T幣僅能用於支付集運相關費用，無法用於代採購服務。</div>
            <div class="">
                <div class="sub-title">付款方式</div>
                <div>
                    <el-radio label="1" v-model="payRadio">網絡ATM/ATM櫃員機</el-radio>
                    <el-radio label="2" v-model="payRadio">超商付款</el-radio>
                </div>
                <div v-if="payRadio === '1'">
                    <div class="checkbox-group">
                        {volist name="ubank" id="item"}
                            <el-radio-group v-model="selectedBankId" @change="selectBankInfo">
                                <div class="checkbox-item"
                                    :class="{selected: selectedBankId == '{$item.id}'}"
                                    @click="selectBankCard('{$item.id}', '{$item.account_name}', '{$item.account_six}')"
                                    style="">
                                    <el-radio :label="String({$item.id})" :key="{$item.id}" style="margin-left: 16px; opacity: 0;" @click.stop>
                                        <span class="fs16" style="display: none;">{$item.id}</span>
                                    </el-radio>
                                    <div class="checkbox-content">
                                        <div class="bank-icon">
                                            <img :src="selectedBankId == '{$item.id}' ? bankIcon.active : bankIcon.inactive" alt="">
                                        </div>
                                        <div class="bank-value" style="display: none">{$item.id}</div>
                                        <div class="bank-value">{$item.name}</div>
                                        <div class="bank-name" >{$item.account_name}</div>
                                        <div class="bank-code">{$item.account_six}</div>
                                    </div>
                                </div>
                            </el-radio-group>
                        {/volist}
                        <!-- 添加银行卡按钮 -->
                        <div class="add-card pointer" @click="handAddBand">
                            <div class="card-icon" style="width: 27px; height: 17px;">
                                <img :src="bankIcon.inactive" alt="">
                            </div>
                            <div>添加銀行卡</div>
                        </div>
                    </div>
                    <div class="marking">*超過30000金額可多選銀行</div>
                </div>
            </div>
            <div class="footer-btn">
                <div class="left">
                    <el-checkbox v-model="isChecked" style="margin-top: 4px"></el-checkbox>
                    <span style="font-size: 12px; margin-left: 10px;" :style="{color: isChecked ? '#EF436D' : '#333'}">我同意儲值F币僅能用於支付訂單，無法提現臺幣</span>
                </div>
                <div class="right">
                    <el-button style="border-radius: 4px; min-width: 98px;  background: #FFFFFF " @click="resetForm">重 置
                    </el-button>
                    <el-button style="background: #ef436d; border-radius: 4px; min-width: 98px;" type="danger" @click="submitRecharge">確 定
                    </el-button>
                </div>
            </div>

        </div>
        <el-dialog title="新增銀行卡" class="forward" color="red" :visible.sync="addDialogVisible"
            :close-on-click-modal="false" width="514px" right>
            <div style="width: 466px; height: 284px; ">
                <img style="width: 100%; height: 100%; object-fit: cover;"
                    src="__CDN__/assets/img/pc/new_index/bank_bg.png" alt="">
            </div>
            <div class="fs13 c333" style="margin-top: 24px"><span
                    style="color: #D54941">*注意事項：</span>付款銀行帳號新增上限是3個，請注意填寫且無法隨意更改。</div>
            <div class="mt24 fs14">銀行類別<span class="red">*</span></div>
            <div class="mt24">
                <el-radio-group v-model="bankRadio" @input="selectBankType">
                    <el-radio v-for="(item,index) in bankOptions" :key="index" :label="index">{{item.name}}</el-radio>
                </el-radio-group>
            </div>
            <div class="mt12">
                <el-select v-model="form.payCard" placeholder="请选择" width="100%">
                    <el-option v-for="(item,index) in bankList" :key="index" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <div class="mt12">
                賬戶姓名<span class="red">*</span>
                <div>
                    <el-input v-model="form.uname" placeholder="請輸入賬戶姓名" style="margin-top: 8px" clearable>
                    </el-input>
                </div>
            </div>
            <div class="mt12">
                賬戶末6位<span class="red">*</span>
                <div>
                    <el-input v-model="form.lastNum" style="margin-top: 8px" placeholder="请輸入銀行賬戶末6位" clearable
                             maxlength="6" @input="validateLastNum">
                    </el-input>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeAddBank">取 消</el-button>
                <el-button type="danger" style="background: #EF436D" @click="submitAddBank">確 定</el-button>
            </span>
        </el-dialog>
    </div>
</div>
<script>
    const app = new Vue({
        el: '#recharge',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            addDialogVisible: false,
            isCardStatus: false,
            TWDAmount: '',
            payRadio: '1',
            isChecked: false,   // 协议
            bankIcon: {
                inactive: '__CDN__/assets/img/pc/new_index/bank.svg',
                active: '__CDN__/assets/img/pc/new_index/active_bank.svg'
            },
            selectedBankId: '', // 选中的银行ID
            selectedBanks: [],
            bankList: [],
            bankOptions: [],
            bankRadio: 0,   // 銀行類型
            form: {
                lastNum: '',    // 銀行卡末6位
                payCard: '', // 銀行卡
                uname: '',   // 賬戶姓名

            },
            bankInfo: {
                id: '', // 银行ID
                uname: '', // 银行用户名
                lastSix: '', // 银行卡尾6位数
            }
        },
        mounted() {
            this.$nextTick(() => {
                const firstBank = document.querySelector('.checkbox-item');
                if (firstBank) {
                    // 取第一个卡片的 id
                    const bankId = firstBank.querySelector('.bank-value').innerText;
                    this.selectedBankId = String(bankId);
                    // 设置 bankInfo
                    this.bankInfo.id = bankId;
                    this.bankInfo.uname = firstBank.querySelector('.bank-name').innerText;
                    this.bankInfo.lastSix = firstBank.querySelector('.bank-code').innerText;
                } else {
                    this.selectedBankId = '';
                    this.bankInfo = { id: '', uname: '', lastSix: '' };
                }
            });
        },
        methods: {
            handleAmount(event) {
                let value = event.target.value;
                value = value.replace(/[^\d]/g, ''); // 只保留数字
                value = value.replace(/^0+/, '');    // 去除前导0
                this.TWDAmount = value;
            },

            resetForm() {
                this.form = { lastNum: '', payCard: '',  uname: '' };
                this.TWDAmount = '';
                this.payRadio = '1'; // 默認選擇網絡ATM/ATM櫃員機
                this.isChecked = false;
                this.bankInfo = { id: '', uname: '', lastSix: '' };
            },

            // 關閉添加銀行
            closeAddBank() {
                this.resetForm();
                this.addDialogVisible = false;
            },
            // 添加銀行卡
            async handAddBand() {
                try {
                    let res = await axios.get('addbank');
                    console.log(res, 'res');
                    if (res.data.code == 0) {
                        this.addDialogVisible = true;
                        this.bankOptions = res.data?.bank;
                        if (this.bankOptions && this.bankOptions.length > 0) {
                            this.bankRadio = 0;
                            this.selectBankType(0); // 如果需要同步 bankList
                        }
                    }else {
                        this.$message.error(res.data.msg);
                    }
                } catch (err) {
                    this.$message.error('網絡錯誤，請稍後再試');
                }
            },
            selectBankType(index) {
                console.log(index);
                this.bankList = this.bankOptions[index].list;
                this.form.payCard = ''; // Reset the selected bank when bank type changes
            },
            validateLastNum(event) {
                // Only allow digits and limit to 6 characters
                this.form.lastNum = this.form.lastNum.replace(/\D/g, '').slice(0, 6);
            },
            async submitAddBank() {
                // Validate bank account number is exactly 6 digits
                if (!this.form.payCard) {
                    this.$message.warning('請選擇銀行');
                    return;
                }
                if (!this.form.uname) {
                    this.$message.warning('請輸入賬戶姓名');
                    return;
                }
                if (!this.form.lastNum || this.form.lastNum.length !== 6 || !/^\d{6}$/.test(this.form.lastNum)) {
                    this.$message.warning('請輸入正確的銀行賬戶末6位數字');
                    return;
                }
                
                const data = {
                    bank: this.form.payCard,
                    name: this.form.uname,
                    lastsix: this.form.lastNum,
                }
                try {
                    let res = await axios.post('addbank', data);
                    console.log(res, '提交信息')
                    if (res.data.code == '0') {
                        this.addDialogVisible = false;
                        this.$message.success(res.data.msg);
                        this.resetForm();
                    }
                } catch (err) {
                    this.$message.error('網絡錯誤，請稍後再試');
                }
            },
            // 点击卡片时，选中radio并保存银行信息
            selectBankCard(id, account_name, account_six) {
                this.selectedBankId = String(id);
                this.bankInfo = {
                    id: id,
                    uname: account_name,
                    lastSix: account_six
                };
                console.log('this.bankInfo', this.bankInfo);

                // 这里手动触发selectBankInfo逻辑（如果el-radio-group的@change不会自动触发）
                this.selectBankInfo(this.selectedBankId);
            },
            // el-radio-group切换时
            selectBankInfo(val) {
                this.$nextTick(() => {
                    const el = document.querySelector(`.checkbox-item.selected`);
                    if (el) {
                        this.bankInfo = {
                            id: el.querySelector('.bank-value').innerText,
                            uname: el.querySelector('.bank-name').innerText,
                            lastSix: el.querySelector('.bank-code').innerText
                        };
                    }
                });
            },
            async submitRecharge() {
                const data = {
                    money: this.TWDAmount,
                    pay_type: this.payRadio, // 1: 網絡ATM/ATM櫃員機, 2: 超商付款
                    bank_id: this.bankInfo.id, // 选中的银行ID
                }
                if (!this.isChecked) {
                    this.$message.warning('請同意儲值F币僅能用於支付訂單，無法提現臺幣');
                    return;
                }
                try {
                    let res = await axios.post('', data);
                    console.log(res,'res')
                    if (res.data.code == 0) {
                        this.$message.success(res.data.msg);
                    } else {
                        this.$message.error(res.data.msg);
                    }
                    console.log(res, '提交信息');
                } catch (err) {
                    this.$message.error('網絡錯誤，請稍後再試');
                }
            },
        },
    })
</script>