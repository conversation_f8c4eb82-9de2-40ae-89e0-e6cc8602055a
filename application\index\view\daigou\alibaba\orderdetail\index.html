<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../css/element-ui-index.css">
    <link href="../css/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/common.css">
    <script src="../js/vue.js"></script>
    <!-- 引入组件库 -->
    <script src="../js/element-ui-index.js"></script>

</head>

<body>
    <!-- 订单列表 -->
    <div id="app" class="max-w-screen-2xl mx-auto ">
        <div class="max-w-2xl mx-auto bg-white rounded shadow p-6 mt-8 mb-12">
          <!-- 订单号 -->
          <div class="text-center text-base font-bold bg-pink-50 py-3 rounded-t">订单详情单 - 438172232480973115</div>

          <!-- 买家信息 -->
          <div class="mt-6">
            <div class="flex items-center mb-2">
              <svg class="w-4 h-4 text-pink-500 mr-1" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2a6 6 0 016 6v1a6 6 0 01-12 0V8a6 6 0 016-6zm0 12a8 8 0 00-8 8h16a8 8 0 00-8-8z"/></svg>
              <span class="text-pink-500 font-bold">买家信息</span>
            </div>
            <div class="border rounded mb-4">
              <div class="grid grid-cols-4 text-sm border-b">
                <div class="p-2 bg-gray-50">买家</div>
                <div class="p-2">杭州市宝跨境电子商务有限公司</div>
                <div class="p-2 bg-gray-50">买家会员名</div>
                <div class="p-2">hzpaybao</div>
              </div>
              <div class="grid grid-cols-4 text-sm border-b">
                <div class="p-2 bg-gray-50">收货地址</div>
                <div class="p-2 col-span-3">小碎步(DG285099-9590) 17305005560<br>福建省 福州市 闽侯县 祥谦镇 祥谦路86号正广通物流园C02栋三楼沛全公司-285099号DG9590</div>
              </div>
              <div class="grid grid-cols-4 text-sm border-b">
                <div class="p-2 bg-gray-50">手机号</div>
                <div class="p-2">15395842457</div>
                <div class="p-2 bg-gray-50">电话号码</div>
                <div class="p-2"></div>
              </div>
              <div class="grid grid-cols-4 text-sm">
                <div class="p-2 bg-gray-50">下单公司主体：</div>
                <div class="p-2"></div>
                <div class="p-2 bg-gray-50"></div>
                <div class="p-2"></div>
              </div>
            </div>
          </div>

          <!-- 卖家信息 -->
          <div class="mt-6">
            <div class="flex items-center mb-2">
              <svg class="w-4 h-4 text-pink-500 mr-1" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2a6 6 0 016 6v1a6 6 0 01-12 0V8a6 6 0 016-6zm0 12a8 8 0 00-8 8h16a8 8 0 00-8-8z"/></svg>
              <span class="text-pink-500 font-bold">卖家信息</span>
            </div>
            <div class="border rounded mb-4">
              <div class="grid grid-cols-4 text-sm border-b">
                <div class="p-2 bg-gray-50">卖家</div>
                <div class="p-2">惠州市欣晟表业有限公司</div>
                <div class="p-2 bg-gray-50">卖家会员名</div>
                <div class="p-2">欣晟表业</div>
              </div>
              <div class="grid grid-cols-4 text-sm border-b">
                <div class="p-2 bg-gray-50">手机号</div>
                <div class="p-2">15395842457</div>
                <div class="p-2 bg-gray-50">电话号码</div>
                <div class="p-2"></div>
              </div>
            </div>
          </div>

          <!-- 订单信息 -->
          <div class="mt-6">
            <div class="flex items-center mb-2">
              <svg class="w-4 h-4 text-pink-500 mr-1" fill="currentColor" viewBox="0 0 20 20"><path d="M4 4h12v12H4z"/></svg>
              <span class="text-pink-500 font-bold">订单信息</span>
            </div>
            <div class="border rounded mb-4">
              <div class="grid grid-cols-4 text-sm border-b">
                <div class="p-2 bg-gray-50">交易订单号</div>
                <div class="p-2">438172232480973115</div>
                <div class="p-2 bg-gray-50">下单时间</div>
                <div class="p-2">2025-06-11 15:57:53</div>
              </div>
              <div class="grid grid-cols-4 text-sm">
                <div class="p-2 bg-gray-50">发货时间</div>
                <div class="p-2"></div>
                <div class="p-2 bg-gray-50"></div>
                <div class="p-2"></div>
              </div>
            </div>
          </div>

          <!-- 收货和物流信息 -->
          <div class="mt-6">
            <div class="flex items-center mb-2">
              <svg class="w-4 h-4 text-pink-500 mr-1" fill="currentColor" viewBox="0 0 20 20"><path d="M4 4h12v12H4z"/></svg>
              <span class="text-pink-500 font-bold">收货和物流信息</span>
            </div>
            <div class="border rounded mb-4">
              <table class="w-full text-xs text-center border-collapse">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="py-2 font-normal">序号</th>
                    <th class="py-2 font-normal">货号</th>
                    <th class="py-2 font-normal">货品名称</th>
                    <th class="py-2 font-normal">规格</th>
                    <th class="py-2 font-normal">数量</th>
                    <th class="py-2 font-normal">单价（元）</th>
                    <th class="py-2 font-normal">优惠（元）</th>
                    <th class="py-2 font-normal">金额（元）</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="py-2">1</td>
                    <td class="py-2">pvc数字</td>
                    <td class="py-2">学生党读书支教ins小众设计时尚潮流时尚复刻初高中情侣百搭表表</td>
                    <td class="py-2">颜色:pvc暗棕木大号黑色</td>
                    <td class="py-2">1</td>
                    <td class="py-2">4.00元/个</td>
                    <td class="py-2">0.00</td>
                    <td class="py-2">3.96</td>
                  </tr>
                </tbody>
              </table>
              <div class="p-4 text-xs text-gray-700">
                货品合计：4.00元 &nbsp;&nbsp; 实付款：3.96元<br>
                货品总量：1 &nbsp;&nbsp; 运费：0.00元 &nbsp;&nbsp; 优惠：0.00元
              </div>
            </div>
          </div>

          <!-- 买家留言 -->
          <div class="mt-6 mb-8">
            <div class="flex items-center mb-2">
              <svg class="w-4 h-4 text-pink-500 mr-1" fill="currentColor" viewBox="0 0 20 20"><path d="M4 4h12v12H4z"/></svg>
              <span class="text-pink-500 font-bold">买家留言</span>
            </div>
            <div class="border rounded p-4 text-sm text-gray-700 bg-gray-50">无</div>
          </div>

          <!-- 底部按钮 -->
          <div class="flex justify-end mt-6">
            <button class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded font-bold">列印订单详情</button>
          </div>
        </div>
    </div>

    <script>
      new Vue({
        el: '#app',
        data: {
          date: '',
          keyword: '',
          selectedOrders: [],
          activeStatus: 'all',
          orders: [
            {
              id: '6174486540999855',
              tbOrderId: '6174486540999855',
              status: 'cancelled',
              createTime: '2025-03-07 13:58:03',
              shopName: 'wildchildclub旗舰店',
              productName: '七小七核桃燕麦奶儿童坚果乳植物蛋白饮料学生健康营养早餐奶饮品',
              productSpec: '核桃燕麦乳*8袋 ×1',
              productImg: '../img/product1.png',
              amount: '31.90',
              shipping: '0',
              address: '深圳仓',
              freeShipping: true
            },
            {
              id: '6174486540999856',
              tbOrderId: '6174486540999856',
              status: 'pending',
              createTime: '2025-03-07 13:58:03',
              shopName: 'wildchildclub旗舰店',
              productName: '七小七核桃燕麦奶儿童坚果乳植物蛋白饮料学生健康营养早餐奶饮品',
              productSpec: '核桃燕麦乳*8袋 ×1',
              productImg: '../img/product1.png',
              amount: '31.90',
              shipping: '0',
              address: '深圳仓'
            },
            {
              id: '6174486540999857',
              tbOrderId: '6174486540999857',
              status: 'shipped',
              createTime: '2025-03-07 13:58:03',
              shopName: 'wildchildclub旗舰店',
              productName: '七小七核桃燕麦奶儿童坚果乳植物蛋白饮料学生健康营养早餐奶饮品',
              productSpec: '核桃燕麦乳*8袋 ×1',
              productImg: '../img/product1.png',
              amount: '31.90',
              shipping: '0',
              address: '深圳仓',
              logistics: {
                company: '极兔速递',
                number: 'JT311131959201',
                status: '【揽收中】打包完成，正在等待揽收...'
              },
              invoice: {
                to: '财团法人早见疾病基金会'
              }
            }
          ]
        },
        computed: {
          filteredOrders() {
            if (this.activeStatus === 'all') {
              return this.orders;
            }
            return this.orders.filter(order => order.status === this.activeStatus);
          }
        }
      })
    </script>
</body>

</html>