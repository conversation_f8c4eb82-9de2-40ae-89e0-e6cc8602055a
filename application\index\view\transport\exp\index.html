{include file="common/resources" /}
<!-- 添加CSRF token -->
{:token()}
<div class="transport-box" id="exp">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container relative">
        <div class="tab-header">
            <div :class="{ 'active': activeIndex === index }" :key="index" @click="changeTabs(activeIndex = index)"
                class="tab-item pointer fs16" v-for="(item, index) in tabList">
                {{ item }}
            </div>
        </div>
        <div class="absolute fw fs16" style="top: 20px; left: 20px; color: #3D3D3D">集運說明</div>
        <div class="re-content">
            <div class="card-grid">
                <div class="card-item" v-for="(item, index) in activeIndex === 0 ? firstExpList : activeIndex === 1 ? secondExpList : thirdExpList" :key="index">
                    <div class="card-title">
                        <img :src="item.img">
                        {{ item.title }}
                    </div>
                    <div class="card-content" v-for="(content, index) in item.content" :key="index">
                        {{ content }}
                    </div>
                </div>
            </div>
            <div class="faq-section">
                <div class="faq-col">
                    <a href="{:url('/index/login/prohibited')}">禁運說明</a>
                    <a href="{:url('/index/login/tran_agree')}">集運說明</a>
                    <a href="{:url('/index/login/goods_type')}">普貨特貨說明</a>
                </div>
                <div class="faq-col">
                    <a href="#">托寄商品说明 付细</a>
                    <a href="#">收到账时计费事项</a>
                    <a href="#">包裹入仓及问题包裹处理事项及规定 付细</a>
                </div>
                <div class="faq-col">
                    <a href="#">集运商品运送终点 付细</a>
                    <a href="#">理赔说明 付细</a>
                    <a href="#">集运常见问题 付细</a>
                </div>
                <div class="faq-col">
                    <a href="#">禁运商品列表</a>
                    <a href="#">集运（空运）费用说明</a>
                    <a href="#">集运教 付细</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const app = new Vue({
        el: '#exp',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            isCardStatus: false,
            activeIndex: 0,
            tabList: ['海快說明', '空運説明', '海運説明'],
            firstExpList: [
               {
                title: '服務説明',
                img: '__CDN__/assets/img/pc/new_index/exp_01.svg',
                content: [
                    '包裹最低5公斤起運，不足5公斤以5公斤計費 。',
                    '運費皆已包含配送費、清關費，根據不同運費方案提供包稅和不包稅的服務。',
                    '配合的台灣物流為【新竹】、【大榮】。'
                ]
               },{
                title: '拋貨/超才費用',
                img: '__CDN__/assets/img/pc/new_index/exp_02.svg',
                content: [
                    '海運包裹：長*寬*高/27000=體積重量',
                    '空運包裹：長*寬*高/6000=體積重量',
                    '體積重量與實際重量取較大的重量計重',
                    '體積重量大於實際重量3倍以內全部優惠（即按實際重量計重）',
                    '體積重量大於實際重量3倍以上享有50%優惠（即按體積重量/2計重）',
                    '不超過5才的不收取超才費，超過按照實際費用收取。'
                ]
               },{
                title: '體積/重量限制',
                img: '__CDN__/assets/img/pc/new_index/exp_03.svg',
                content:[
                    '1. 海快單件物品限制長寬高 180cm*180cm*長(不限)，三邊有其中兩邊超過180cm無法通過海關x光機，需要轉一般倉正式報關，重量超過70kg 也需轉正式報關，時效慢且會產生高額雜費(新台幣3-6千)，稅金另計，總費用向您實報實銷。' ,
                    '2. 單件包裹材積超過30才或重量超過50公斤時，物流可能會外派專車配送，若有產生專車費用時將會向您實報實銷。 由於超過最大的透明袋尺寸，當合併包裹下單時會無法封口，倉庫只能將長度超過115cm的商品(無論是否細長型) 單獨打包，因此會產生多個包裹出貨。']
               },{
                title: '進口説明',
                img: '__CDN__/assets/img/pc/new_index/exp_04.svg',
                content: [
                    '特貨：為帶電池、帶磁、化妝品、液體、粉末、膏狀...等等',
                    '普貨：為不帶電、不帶磁、不含液體粉末膏狀物品...等等',
                    '禁運：食品、農產品、乾燥花、地板、瓷磚、電話卡(網卡)、醫藥用品、酒、毒品、槍支、易燃易爆物、電子煙相關產品... 等等 建議購物前先使用本站的 " 能寄嗎 " 功能查詢，國際運輸有一定風險，針對易碎品務必請賣家包裝好。'
                ]
               }
            ],
            secondExpList: [
                {
                    title: '服務説明',
                    img: '__CDN__/assets/img/pc/new_index/exp_01.svg',
                    content: ['EZ集運通是以從事海快為主的集運公司，空運服務是為了彌補會員偶爾需要寄輕小件及一些海快不可以寄但空運可以寄的商品。目前空運可以寄特貨和不含肉食品，普貨特貨同價。']
                },{
                    title: '拋貨/超才費用',
                    img: '__CDN__/assets/img/pc/new_index/exp_02.svg',
                    content: [
                        '空運包裹長x寬x高/6000=體積重量',
                        '體積重量與實際重量取較大的重量計重',
                        '體積重量大於實際重量3倍以內全部優惠（即按實際重量計重）',
                        '體積重量大於實際重量3倍以上享有50%優惠（即按體積重量/2計重）'
                    ]
                },{
                    title: '體積重量限制',
                    img: '__CDN__/assets/img/pc/new_index/exp_03.svg',
                    content: [
                        '最大體積/重量限制：',
                        '物品單邊長度不可超過160cm， 三邊若兩邊超過 70cm 和 85cm，就會多出額外報關移倉費用(新台幣$4700起，以清關公司收費為主) ，重量限制70KG/件以内。',
                        '單邊超過115cm的物體視為超長件，由於超過最大的透明袋尺寸，無法封口，當合併包裹下單時，倉庫只能將長度超過115cm的商品（無論是不是細長）單獨打包，因此會產生多個包裹。強烈建議使用巧拼，節省稅金',
                        '超長件下單注意事項：',
                        '1.有超長件合併包裹下單時，由於同一筆訂單會打成多個包裹，只能選擇x3申報（不包稅）',
                        '2.合併打包後的包裹如果單個集運包裹不足1KG將以1KG收費，如果超長件包裹不足1KG由於是單獨打包將以1KG收費。'
                    ]
                },{
                title: '進口説明',
                img: '__CDN__/assets/img/pc/new_index/exp_04.svg',
                content: [
                    '普貨-普貨為不帶電、不帶磁、不含液體粉末膏狀物品...等等 ',
                    '特貨-特貨為帶電池、帶磁、化妝品、液體、粉末、膏狀...等等',
                    '禁運説明：',
                    '酒、毒品、槍支、水果、植物、電話卡(網卡)、易燃易爆物、電動車類(平衡車、滑板車)、電子煙相關產品...',
                    '建議購物前先使用本站的 “ 能寄嗎 ” 功能查詢，國際運輸有一定風險，針對易碎品務必請賣家包裝好'
                ]
               }
            ],
            thirdExpList: [
                
            ]
        },
        mounted() {
            this.changeTabs(0);
        },
        computed: {
            
        },
        methods: {
            changeTabs(index) {
                this.activeIndex = index;
                console.log(this.activeIndex, 'activeIndex');
            }
        }
    })
</script>