<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Alias')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-alias" data-rule="required" class="form-control" name="row[alias]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-content" data-rule="required" class="form-control" name="row[content]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cg_proprotion')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cg_proprotion" data-rule="required" class="form-control" step="0.01" name="row[cg_proprotion]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cp_proportion')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cp_proportion" data-rule="required" class="form-control" step="0.01" name="row[cp_proportion]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ns_min')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ns_min" data-rule="required" class="form-control" name="row[ns_min]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ns_max')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ns_max" data-rule="required" class="form-control" name="row[ns_max]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Most')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-most" data-rule="required" class="form-control" name="row[most]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Describe')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-describe" data-rule="required" class="form-control" name="row[describe]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Json_data')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-json_data" data-rule="required" class="form-control" name="row[json_data]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="0">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
