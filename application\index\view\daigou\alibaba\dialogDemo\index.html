<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹框演示</title>
    <link href="../css/tailwind.min.css" rel="stylesheet">
    <script src="../js/vue.js"></script>
</head>
<body>
<div id="app">
    <!-- 充值弹框 -->
    <div v-if="showRechargeDialog"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
        <div class="bg-white rounded-lg shadow-lg p-8 w-[600px] max-w-full text-center relative">
            <div class="text-pink-500 text-xl font-bold mb-6 tracking-wider">收银臺6174910963399594</div>
            <div class="text-left mb-6">
                <div class="text-pink-500 font-bold mb-2 flex items-center">
                    <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>费用结算
                </div>
                <div class="bg-gray-50 rounded p-4 mb-4">
                    <div class="flex justify-between text-sm mb-2"><span>货品总价：</span><span>￥31.90</span></div>
                    <div class="flex justify-between text-sm mb-2"><span>运费：</span><span>￥0.00</span></div>
                    <div class="flex justify-between text-sm mb-2"><span>订单金额：</span><span class="text-pink-500 font-bold">31.90元（计费匯率 4.585）</span></div>
                    <div class="flex justify-between text-sm mb-2"><span>订单金额(台币)：</span><span>134元</span></div>
                    <div class="flex justify-between text-sm mb-2"><span>1%服务费(台币)：</span><span>1元</span></div>
                    <div class="flex justify-between text-base font-bold"><span>合计台币：</span><span class="text-pink-500">135元</span></div>
                </div>
            </div>
            <div class="text-left mb-6">
                <div class="text-pink-500 font-bold mb-2 flex items-center">
                    <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>您的付款银行账户
                </div>
                <div class="flex space-x-4 mb-2">
                    <div class="bg-white border rounded px-6 py-2 flex items-center text-gray-700">摩芮喵 002222</div>
                    <div class="bg-white border rounded px-6 py-2 flex items-center text-gray-700">摩芮喵 002222</div>
                    <div class="bg-white border rounded px-6 py-2 flex items-center text-gray-700">摩芮喵 002222</div>
                </div>
                <div class="bg-yellow-50 text-yellow-700 p-2 rounded text-xs flex items-center mb-2">
                    <span class="mr-2">⚠️</span>请使用您已登记的银行卡号转账付款，非上述银行卡号转账入账无法完成交易！
                </div>
            </div>
            <div class="text-left mb-6">
                <div class="500 font-bold mb-2 flex items-center">
                    <span class="w-1 h-4 bg-pink-500 mr-2 inline-block rounded"></span>網路ATM/ATM櫃員機
                </div>
                <div class="flex items-center mb-2">
                    <div class="rounded-xl py-6 flex flex-col items-center w-full max-w-md cursor-pointer"
                        @click="showBankModal = true">
                        <img src="../img/card.jpg" class="" />
                    </div>
                </div>
                <div class="bg-yellow-50 text-yellow-700 p-2 rounded text-xs flex items-center mb-2">
                    1688代采服务商的付款公司全程服务，请务必对订单金额入账转账，如金额有误或误购入其他账户将无法即时入账。
                </div>
            </div>
            <div class="flex items-center justify-center mt-6">
                <button class="bg-pink-500 text-white px-12 py-2 rounded text-lg font-bold"
                    @click="showRechargeDialog = false">我已匯款</button>
            </div>
        </div>
    </div>
    <!-- 银行弹窗 -->
    <div v-if="showBankModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
        <div class="bg-white rounded-2xl shadow-xl p-8 w-full max-w-lg relative">
            <div class="text-center text-pink-500 text-2xl font-bold mb-6">溫馨提示</div>
            <div class="flex items-start mb-6">
                <span class="text-pink-500 text-2xl mr-2">!</span>
                <div class="text-gray-700 text-base leading-relaxed">
                    請使用以下銀行賬號至ATM或網路銀行完成轉賬
                </div>
            </div>
            <div class="flex space-x-4 justify-center mb-6">
                <div class="flex flex-col items-center border rounded-xl px-6 py-4 w-40">
                    <img src="../img/bank.png" class="w-10 h-10 mb-2" />
                    <div class="text-gray-700 font-bold">中國信托</div>
                    <div class="text-gray-500 text-lg font-mono tracking-widest">002222</div>
                </div>
                <div class="flex flex-col items-center border rounded-xl px-6 py-4 w-40">
                    <img src="../img/bank.png" class="w-10 h-10 mb-2" />
                    <div class="text-gray-700 font-bold">郵局</div>
                    <div class="text-gray-500 text-lg font-mono tracking-widest">002222</div>
                </div>
                <div class="flex flex-col items-center border rounded-xl px-6 py-4 w-40">
                    <img src="../img/bank.png" class="w-10 h-10 mb-2" />
                    <div class="text-gray-700 font-bold">郵局</div>
                    <div class="text-gray-500 text-lg font-mono tracking-widest">002222</div>
                </div>
            </div>
            <div class="bg-yellow-100 text-yellow-700 rounded-lg px-4 py-3 flex items-center mb-4">
                <span class="text-2xl mr-2">&#9888;</span>
                <span>若您尚未完成轉賬，請儘快於ATM或網路銀行完成轉賬</span>
            </div>
            <div class="text-xs text-gray-500 mb-2">
                *不是以上銀行匯款，請<a href="#" class="text-pink-500 underline">登記新的銀行末六位碼</a>
                <span class="float-right text-green-500">已登記末六位碼</span>
            </div>
            <div class="flex justify-end space-x-4 mt-6">
                <button class="px-8 py-2 rounded border text-gray-500 bg-white hover:bg-gray-100"
                    @click="showBankModal = false">取消</button>
                <button class="px-8 py-2 rounded bg-pink-500 text-white font-bold hover:bg-pink-600"
                    @click="showBankModal = false">確定</button>
            </div>
        </div>
    </div>
    <!-- 温馨提示弹框 -->
    <div v-if="showTipDialog" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
        <div class="bg-white rounded-xl shadow-lg p-8 text-center relative" style="width:400px">
            <div class="flex justify-center items-center mb-4">
                <span class="text-yellow-500 text-3xl mb-2 mr-4">!</span>
                <span class="text-yellow-500 text-xl font-bold mb-2">温馨提示</span>
            </div>
            <div class="text-gray-700 text-base mb-2 leading-relaxed">
                请於收到商品清点无误后，点击「完成订单」按钮，系统将於隔日凌晨开立电子发票。
            </div>
            <div class="text-pink-500 text-xs mb-4">
                *系统默认包裹签收后15日，自动确认交易。
            </div>
            <div class="flex justify-between mt-6 space-x-4">
                <button class="flex-1 px-4 py-2 rounded border text-gray-500 bg-white hover:bg-gray-100"
                    @click="showTipDialog = false">返回</button>
                <button class="flex-1 px-4 py-2 rounded bg-pink-500 text-white font-bold hover:bg-pink-600"
                    @click="showRechargeDialogtrue">我已瞭解</button>
            </div>
        </div>
    </div>
</div>
<script>
new Vue({
    el: '#app',
    data: {
        showRechargeDialog: false,
        showBankModal: false,
        showTipDialog: true
    },
    methods: {
        showRechargeDialogtrue() {
            this.showRechargeDialog = true;
            this.showTipDialog = false;
        }
    }
})
</script>
</body>
</html> 