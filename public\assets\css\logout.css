/* Logout 相关样式 */

/* 确认对话框样式 */
.logout-confirm-dialog {
    border-radius: 8px;
}

.logout-confirm-dialog .el-message-box__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 20px;
}

.logout-confirm-dialog .el-message-box__title {
    color: white;
    font-size: 18px;
    font-weight: 600;
}

.logout-confirm-dialog .el-message-box__headerbtn .el-message-box__close {
    color: white;
}

.logout-confirm-dialog .el-message-box__headerbtn .el-message-box__close:hover {
    color: #f0f0f0;
}

.logout-confirm-dialog .el-message-box__content {
    padding: 30px 20px;
    text-align: center;
    font-size: 16px;
    color: #606266;
}

.logout-confirm-dialog .el-message-box__btns {
    padding: 0 20px 20px;
    text-align: center;
}

/* Logout 按钮样式 */
#logout-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#logout-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#logout-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#logout-btn:hover::before {
    left: 100%;
}

/* Dropdown 组件样式 */
.logout-dropdown-container {
    display: inline-block;
}

.el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: rgba(64, 158, 255, 0.1);
    border: 1px solid rgba(64, 158, 255, 0.2);
}

.el-dropdown-link:hover {
    background: rgba(64, 158, 255, 0.2);
    border-color: rgba(64, 158, 255, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.el-dropdown-link .username {
    margin: 0 8px;
    font-weight: 500;
}

.el-dropdown-link i {
    font-size: 16px;
}

/* Dropdown 菜单样式 */
.el-dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border: none;
    padding: 8px 0;
}

.el-dropdown-menu__item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 2px 8px;
}

.el-dropdown-menu__item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(4px);
}

.el-dropdown-menu__item i {
    margin-right: 8px;
    font-size: 16px;
}

.el-dropdown-menu__item--divided {
    border-top: 1px solid #ebeef5;
    margin-top: 8px;
    padding-top: 12px;
}

/* 加载动画样式 */
.el-loading-mask {
    backdrop-filter: blur(2px);
}

.el-loading-spinner .el-loading-text {
    color: #409eff;
    font-size: 14px;
    font-weight: 500;
}

/* 消息提示样式 - 修复字体颜色问题 */
.el-message {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none !important;
    padding: 12px 20px !important;
    min-width: 300px !important;
    max-width: 80% !important;
    z-index: 9999 !important;
}

/* 成功消息样式 */
.el-message--success {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
    border: none !important;
}

.el-message--success .el-message__content {
    color: white !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.el-message--success .el-message__icon {
    color: white !important;
}

/* 错误消息样式 */
.el-message--error {
    background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;
    border: none !important;
}

.el-message--error .el-message__content {
    color: white !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.el-message--error .el-message__icon {
    color: white !important;
}

/* 警告消息样式 */
.el-message--warning {
    background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
    border: none !important;
}

.el-message--warning .el-message__content {
    color: white !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.el-message--warning .el-message__icon {
    color: white !important;
}

/* 信息消息样式 */
.el-message--info {
    background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%) !important;
    border: none !important;
}

.el-message--info .el-message__content {
    color: white !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.el-message--info .el-message__icon {
    color: white !important;
}

/* 关闭按钮样式 */
.el-message .el-message__closeBtn {
    color: white !important;
    opacity: 0.8 !important;
}

.el-message .el-message__closeBtn:hover {
    color: white !important;
    opacity: 1 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .logout-confirm-dialog {
        width: 90% !important;
        margin: 0 auto;
    }
    
    .logout-confirm-dialog .el-message-box__content {
        padding: 20px 15px;
        font-size: 14px;
    }
    
    .el-dropdown-link {
        padding: 6px 10px;
    }
    
    .el-dropdown-link .username {
        display: none;
    }
    
    .el-dropdown-menu__item {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    /* 移动端消息样式调整 */
    .el-message {
        min-width: 280px !important;
        max-width: 90% !important;
        padding: 10px 16px !important;
        font-size: 14px !important;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.logout-confirm-dialog {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.el-dropdown-menu {
    animation: slideInRight 0.2s ease-out;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .logout-confirm-dialog .el-message-box__content {
        background: #2b2b2b;
        color: #e0e0e0;
    }
    
    .el-dropdown-link {
        background: rgba(64, 158, 255, 0.05);
        border-color: rgba(64, 158, 255, 0.1);
        color: #e0e0e0;
    }
    
    .el-dropdown-link:hover {
        background: rgba(64, 158, 255, 0.1);
        border-color: rgba(64, 158, 255, 0.2);
    }
    
    .el-dropdown-menu {
        background: #2b2b2b;
        border: 1px solid #4a4a4a;
    }
    
    .el-dropdown-menu__item {
        color: #e0e0e0;
    }
    
    .el-dropdown-menu__item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .el-dropdown-menu__item--divided {
        border-top-color: #4a4a4a;
    }
} 