
{load href="__CDN__/assets/css/notice.css" /}
<!-- 方法 1: 包含公共资源文件 -->
{include file="common/resources" /}
<div class="nav">
    <img src="__CDN__/assets/img/pc/nav_logo.png" alt="" style="height: 50%; cursor: pointer;" onclick="window.location.href='{:url('/index/login/login')}'">
    <div class="nav-right">
        <div style="display: flex; align-items: center; gap: 8px;">
            <img src="__CDN__/assets/img/pc/new_index/parcel18.svg" alt="APP下載">
            <a href="javascript:void(0)" class="terms-link" onclick="window.scrollTo({top: document.documentElement.scrollHeight, behavior: 'smooth'})">APP下載</a>
        </div>
        <!-- <div style="display: flex; align-items: center; gap: 8px;">
            <img src="__CDN__/assets/img/pc/new_index/parcel19.svg" alt="通知公告">
            <a href="#" class="terms-link">通知公告</a>
        </div> -->
    </div>
</div>

<div class="notice">
    <div class="notice-container">
        <div class="notice-tabs">
            {php}
                $currentType = input('type', '1');

                $noticeTypes = config('site.notice');

                if (isset($noticeTypes[38])) {
                    unset($noticeTypes[38]);
                }
                
                // echo "当前type: ".$currentType.", 数据条数: ".count($list);
            {/php}
            
            {foreach name="noticeTypes" item="name" key="key"}
                <a href="{:url('index/notice/index', ['type' => $key])}" class="tab-item {$currentType==$key?'active':''}">
                    {$name}
                </a>
            {/foreach}
        </div>
        
        <div class="notice-list">
            {php}
                if(empty($list)) {
                    echo '<div class="no-data">暫無相關公告</div>';
                }
            {/php}
            {volist name="list" id="notice" empty=""}
            <div class="notice-item" onclick="window.location.href = '{:url('notice/details')}?id={$notice.id}'">
                <div class="notice-content">
                    <span class="notice-text">{$notice.title}</span>
                    <span class="notice-date">{:date('Y-m-d', $notice.createtime)}</span>
                </div>
            </div>
            {/volist}
        </div>
    </div>
</div>
{include file="common/footer" /}

<style>
.no-data {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
}
</style>