define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'base/applicant/index' + location.search,
                    add_url: 'base/applicant/add',
                    edit_url: 'base/applicant/edit',
                    del_url: 'base/applicant/del',
                    multi_url: 'base/applicant/multi',
                    import_url: 'base/applicant/import',
                    table: 'applicant',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user.username', title: __('User.username'), operate: 'LIKE'},
                        {field: 'user.mobile', title: __('User.mobile'), operate: 'LIKE'},
                        {field: 'type', title: __('Type'), searchList: {"0":__('Type0'),"1":__('Type1')} , formatter: Table.api.formatter.status},
                        // {field: 'user_id', title: __('User_id')},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'card', title: __('Card'), operate: 'LIKE'},
                        {field: 'co_code', title: __('Co_code'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status0'),"1":__('Status1')} , formatter: Table.api.formatter.status},
                        {field: 'is_def', title: __('Is_def'), searchList: {"0":__('Is_def0'),"1":__('Is_def1')} , formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
