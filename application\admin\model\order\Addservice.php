<?php

namespace app\admin\model\order;

use think\Model;


class Addservice extends Model
{

    

    

    // 表名
    protected $name = 'addservice';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function package()
    {
        return $this->belongsTo('app\admin\model\order\Package', 'pg_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
