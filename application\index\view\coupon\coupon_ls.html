{include file="common/resources" /}

<!-- 添加CSRF token -->
{:token()}
<div style="width: 1636px; position: absolute; top: 20px; left:13%; z-index: 1000">
    {include file="common/notice" /}
</div>
<div class="transport-box" id="app">
    {include file="common/bottom_nav" /}
    <div class="left_d">
        {include file="common/left_page" /}
    </div>
    <div class="right-container">
        <div class="wallet-page">
            <div class="title">優惠券</div>
            <el-tabs class="custom-tabs" v-model="activeName">
                <el-tab-pane :key="index" :label="item" :lazy="true" :name="item" v-for="(item,index) in tabsName">
                    <div style="display: flex; gap: 24px">
                        <div :key="index"
                             :style="backImg(item.is_use)"
                             class="coupon-item" v-for="(item,index) in filterCouponList">
                            <div class="coupon-item-top">
                                <div class="">
                                    <span :style="textColor(item.is_use)" class="fs12">nt$</span>
                                    <span :style="textColor(item.is_use)" class="fs20 fw">{{item.reaching_amount}}</span>
                                </div>
                                <div :style="textColor(item.is_use)" class="fs12">{{item.title}}</div>
                            </div>
                            <div :style="textColor(item.is_use)" class="coupon-item-btm">{{formatDate(item.start_time)}}-{{formatDate(item.end_time)}}</div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</div>
<script>
    let rows = JSON.parse('<?=json_encode($rows, JSON_UNESCAPED_UNICODE)?>');
    console.log(rows);
    const app = new Vue({
        el: '#app',
        mixins: [bottomNavMixin], // 引入底部导航栏功能
        data: {
            tabsName: ['全部', '已使用優惠券', '未使用優惠券', '失效優惠券'],
            activeName: '',
            couponImg: [
                '__CDN__/assets/img/pc/new_index/coupon01.png',
                '__CDN__/assets/img/pc/new_index/coupon02.png',
                '__CDN__/assets/img/pc/new_index/coupon03.png',
            ],
            couponList:rows
        },
        created() {
            this.activeName = this.tabsName[0];
        },
        mounted() {
        },
        computed:{
            textColor() {
                return (status) => ({
                    color: status === 0 ? '#EF436D' : '#999999'
                })
            },
            backImg() {
                return (status) => ({
                    backgroundImage: `url('${this.couponImg[status]}')`
                })
            },
            // 选项卡切换
            filterCouponList() {
                switch (this.activeName) {
                    case this.tabsName[1]:
                        return this.couponList.filter(item=>item.is_use === 1);
                    case this.tabsName[2]:
                        return this.couponList.filter(item=>item.is_use === 2);
                    case this.tabsName[3]:
                        return this.couponList.filter(item=>item.start_time >= item.end_time );
                    default:
                        return this.couponList;
                }
            }
        },
        methods:{

            formatDate(timestamp, type) {
                return utils.formatDate(timestamp, type);
            },

            handleAmount(event) {
                let value = event.target.value;
                // 移除非数字和小数点
                value = value.replace(/[^\d.]/g, '');
                // 处理多个小数点，只保留第一个
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }
                // 处理以小数点开头的情况，前面加0
                if (value.startsWith('.')) {
                    value = '';
                }
                // 处理前导零（如00123 → 123，但允许0.123）
                if (value.length > 1 && value[0] === '0' && value[1] !== '.') {
                    value = value.replace(/^0+/, '');
                    // 如果替换后为空或以小数点开头，补0
                    if (value === '' || value.startsWith('.')) {
                        value = '0' + value;
                    }
                }
                this.TWDAmount = value;
            },

        },
    })
</script>